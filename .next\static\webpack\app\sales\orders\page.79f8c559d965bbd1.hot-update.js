"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/sales/orders/page",{

/***/ "(app-pages-browser)/./src/app/sales/orders/page.tsx":
/*!***************************************!*\
  !*** ./src/app/sales/orders/page.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ OrderManagementPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/date-picker/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/steps/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/app/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tag/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tooltip/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/badge/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/popconfirm/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input-number/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/row/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/col/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/statistic/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/alert/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/table/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_43__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_ExportOutlined_PlusOutlined_SearchOutlined_SwapOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,ExportOutlined,PlusOutlined,SearchOutlined,SwapOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/SwapOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_ExportOutlined_PlusOutlined_SearchOutlined_SwapOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,ExportOutlined,PlusOutlined,SearchOutlined,SwapOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_ExportOutlined_PlusOutlined_SearchOutlined_SwapOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,ExportOutlined,PlusOutlined,SearchOutlined,SwapOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/SearchOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_ExportOutlined_PlusOutlined_SearchOutlined_SwapOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,ExportOutlined,PlusOutlined,SearchOutlined,SwapOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ExportOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_ExportOutlined_PlusOutlined_SearchOutlined_SwapOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,ExportOutlined,PlusOutlined,SearchOutlined,SwapOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlusOutlined.js\");\n/* harmony import */ var _services_dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/dataAccess/DataAccessManager */ \"(app-pages-browser)/./src/services/dataAccess/DataAccessManager.ts\");\n/* harmony import */ var _utils_dataAccessErrorHandler__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/dataAccessErrorHandler */ \"(app-pages-browser)/./src/utils/dataAccessErrorHandler.ts\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _components_sales_AddOrderModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/sales/AddOrderModal */ \"(app-pages-browser)/./src/components/sales/AddOrderModal.tsx\");\n/* harmony import */ var _hooks_useProductionOrders__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./hooks/useProductionOrders */ \"(app-pages-browser)/./src/app/sales/orders/hooks/useProductionOrders.ts\");\n/* harmony import */ var _services_OrderCancellationService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/services/OrderCancellationService */ \"(app-pages-browser)/./src/services/OrderCancellationService.ts\");\n/* harmony import */ var _services_OrderQuantityChangeService__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/services/OrderQuantityChangeService */ \"(app-pages-browser)/./src/services/OrderQuantityChangeService.ts\");\n/* harmony import */ var _services_OrderDeliveryDateChangeService__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/services/OrderDeliveryDateChangeService */ \"(app-pages-browser)/./src/services/OrderDeliveryDateChangeService.ts\");\n/* harmony import */ var _hooks_useDebouncedCallback__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/hooks/useDebouncedCallback */ \"(app-pages-browser)/./src/hooks/useDebouncedCallback.ts\");\n/* harmony import */ var _hooks_useEventListener__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/hooks/useEventListener */ \"(app-pages-browser)/./src/hooks/useEventListener.ts\");\n/* harmony import */ var _hooks_useDataAccessMonitor__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/hooks/useDataAccessMonitor */ \"(app-pages-browser)/./src/hooks/useDataAccessMonitor.ts\");\n/* harmony import */ var _hooks_useOrdersData__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/hooks/useOrdersData */ \"(app-pages-browser)/./src/hooks/useOrdersData.ts\");\n/* harmony import */ var _utils_architectureCompliance__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/utils/architectureCompliance */ \"(app-pages-browser)/./src/utils/architectureCompliance.ts\");\n/* harmony import */ var _services_validation_OrderValidationService__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/services/validation/OrderValidationService */ \"(app-pages-browser)/./src/services/validation/OrderValidationService.ts\");\n/* harmony import */ var _components_common_OrderDetailModal__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/common/OrderDetailModal */ \"(app-pages-browser)/./src/components/common/OrderDetailModal/index.ts\");\n/* harmony import */ var _components_common_OrderDetailModal_configs_salesOrderConfig__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/common/OrderDetailModal/configs/salesOrderConfig */ \"(app-pages-browser)/./src/components/common/OrderDetailModal/configs/salesOrderConfig.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// 移除useSalesStore依赖，使用dataAccessManager统一数据访问\n\n\n\n\n// 🔧 新增：导入生产订单相关组件和Hook\n\n\n\n\n// ✅ 架构合规：使用DataAccessManager统一监控和合规的Hooks\n\n\n\n\n// ✅ 架构合规性验证工具\n\n// 🔧 P4-3数据验证统一：导入统一验证服务\n\n// 🔧 P5-1订单详情组件重构：导入通用订单详情组件\n\n\nconst { Option } = _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"];\nconst { TextArea } = _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"];\nconst { RangePicker } = _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"];\nconst { Step } = _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"];\nconst OrderManagement = ()=>{\n    _s();\n    const { message, modal } = _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"].useApp();\n    // MRP相关状态保留用于UI显示\n    const [mrpExecuting, setMRPExecuting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDetailModalVisible, setIsDetailModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isChangeModalVisible, setIsChangeModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAddOrderModalVisible, setIsAddOrderModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedOrder, setSelectedOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedChangeType, setSelectedChangeType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Form实例 - 用于订单变更\n    const [changeForm] = _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"].useForm();\n    // ✅ 架构合规：使用useOrdersData Hook替代手动数据加载\n    const { orders: ordersFromHook, loading: ordersLoading, error: ordersError, refreshOrders, loadOrders, hasOrders, isEmpty } = (0,_hooks_useOrdersData__WEBPACK_IMPORTED_MODULE_14__.useOrdersData)({\n        autoLoad: true,\n        enableCache: true\n    });\n    // 保持原有的orders状态以兼容现有代码\n    const [orders, setOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 同步Hook数据到本地状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setOrders(ordersFromHook);\n    }, [\n        ordersFromHook\n    ]);\n    // 显示错误信息\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (ordersError) {\n            message.error(ordersError);\n        }\n    }, [\n        ordersError,\n        message\n    ]);\n    // ✅ 架构合规性检查功能（仅开发环境）\n    const handleArchitectureComplianceCheck = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDD0D 开始执行架构合规性检查...\");\n            const result = await (0,_utils_architectureCompliance__WEBPACK_IMPORTED_MODULE_15__.performArchitectureComplianceCheck)();\n            (0,_utils_architectureCompliance__WEBPACK_IMPORTED_MODULE_15__.printComplianceReport)(result);\n            if (result.compliant) {\n                message.success(\"架构合规性检查通过！评分: \".concat(result.score, \"/100\"));\n            } else {\n                message.warning(\"架构合规性检查未通过，评分: \".concat(result.score, \"/100，请查看控制台详情\"));\n            }\n        } catch (error) {\n            console.error(\"架构合规性检查失败:\", error);\n            message.error(\"架构合规性检查失败\");\n        }\n    };\n    // 🔧 P4-2架构升级：使用数据变更监听器自动刷新数据\n    (0,_hooks_useEventListener__WEBPACK_IMPORTED_MODULE_12__.useDataChangeListener)(\"sales-orders-page\", {\n        onOrderCreated: ()=>{\n            console.log(\"\\uD83D\\uDCDD 检测到销售订单创建，自动刷新数据\");\n            refreshOrders();\n        },\n        onOrderUpdated: ()=>{\n            console.log(\"\\uD83D\\uDCDD 检测到销售订单更新，自动刷新数据\");\n            refreshOrders();\n        },\n        onOrderDeleted: ()=>{\n            console.log(\"\\uD83D\\uDCDD 检测到销售订单删除，自动刷新数据\");\n            refreshOrders();\n        }\n    });\n    // 🔧 P4-2架构升级：优化搜索性能，使用防抖搜索\n    const [searchText, setSearchText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchInput, setSearchInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\") // 输入框的即时值\n    ;\n    const [filterStatus, setFilterStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [filterProductionStatus, setFilterProductionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 🔧 P4-2架构升级：使用防抖搜索优化性能\n    const debouncedSearch = (0,_hooks_useDebouncedCallback__WEBPACK_IMPORTED_MODULE_11__.useDebouncedSearch)((query)=>{\n        setSearchText(query);\n    }, 300, []);\n    // ✅ 架构合规：使用DataAccessManager统一监控体系\n    const { metrics, cacheStats, isMonitoring, clearCache, getPerformanceAlerts, formatMemorySize, formatPercentage, isHealthy, needsOptimization } = (0,_hooks_useDataAccessMonitor__WEBPACK_IMPORTED_MODULE_13__.useDataAccessMonitor)({\n        interval: 60000,\n        enabled: true,\n        showDetails: \"development\" === \"development\"\n    });\n    // 批量操作相关状态\n    const [selectedRowKeys, setSelectedRowKeys] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedOrders, setSelectedOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [batchLoading, setBatchLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // MRP相关状态 - 仅保留UI显示需要的\n    const [mrpResult, setMrpResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showMRPResult, setShowMRPResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mrpExecutionStep, setMrpExecutionStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [mrpExecutionSteps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        \"启动MRP\",\n        \"MRP分析\",\n        \"生成生产订单\",\n        \"完成\"\n    ]);\n    const [productInventory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // 🔧 新增：获取选中订单的历史生产订单\n    const { productionOrders: historicalProductionOrders, loading: productionOrdersLoading } = (0,_hooks_useProductionOrders__WEBPACK_IMPORTED_MODULE_7__.useMRPProductionOrders)((selectedOrder === null || selectedOrder === void 0 ? void 0 : selectedOrder.orderNumber) || \"\");\n    // 获取状态标签\n    const getStatusTag = (status)=>{\n        const statusMap = {\n            \"pending\": {\n                color: \"red\",\n                text: \"未审核\"\n            },\n            \"confirmed\": {\n                color: \"green\",\n                text: \"已审核\"\n            },\n            \"completed\": {\n                color: \"gray\",\n                text: \"完成\"\n            },\n            \"cancelled\": {\n                color: \"orange\",\n                text: \"已取消\"\n            }\n        };\n        const config = statusMap[status] || {\n            color: \"default\",\n            text: \"未知\"\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n            color: config.color,\n            children: config.text\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n            lineNumber: 214,\n            columnNumber: 12\n        }, undefined);\n    };\n    // 获取生产状态标签（与订单状态联动）\n    const getProductionStatusTag = (orderStatus, productionStatus)=>{\n        // 如果订单未审核或已取消，不显示生产状态\n        if (orderStatus === \"pending\" || orderStatus === \"cancelled\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                style: {\n                    color: \"#999\"\n                },\n                children: \"-\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                lineNumber: 221,\n                columnNumber: 14\n            }, undefined);\n        }\n        // 如果订单已审核，显示生产状态（默认为未开始）\n        const actualStatus = orderStatus === \"confirmed\" && !productionStatus ? \"not_started\" : productionStatus;\n        const statusMap = {\n            \"not_started\": {\n                color: \"orange\",\n                text: \"未开始\"\n            },\n            \"pending\": {\n                color: \"blue\",\n                text: \"待生产\"\n            },\n            \"in_progress\": {\n                color: \"green\",\n                text: \"生产中\"\n            },\n            \"completed\": {\n                color: \"cyan\",\n                text: \"已完成\"\n            }\n        };\n        const config = statusMap[actualStatus] || {\n            color: \"orange\",\n            text: \"未开始\"\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n            color: config.color,\n            children: config.text\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n            lineNumber: 234,\n            columnNumber: 12\n        }, undefined);\n    };\n    // 获取MRP状态标签\n    const getMRPStatusTag = (mrpStatus)=>{\n        const statusMap = {\n            \"not_started\": {\n                color: \"default\",\n                text: \"未启动\"\n            },\n            \"in_progress\": {\n                color: \"processing\",\n                text: \"执行中\"\n            },\n            \"completed\": {\n                color: \"success\",\n                text: \"已完成\"\n            },\n            \"failed\": {\n                color: \"error\",\n                text: \"执行失败\"\n            }\n        };\n        const config = statusMap[mrpStatus] || {\n            color: \"default\",\n            text: \"未启动\"\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n            color: config.color,\n            children: config.text\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n            lineNumber: 246,\n            columnNumber: 12\n        }, undefined);\n    };\n    // 获取付款状态标签\n    const getPaymentStatusTag = (status)=>{\n        const statusMap = {\n            \"unpaid\": {\n                color: \"red\",\n                text: \"未付款\"\n            },\n            \"partial\": {\n                color: \"orange\",\n                text: \"部分付款\"\n            },\n            \"paid\": {\n                color: \"green\",\n                text: \"已付款\"\n            }\n        };\n        const config = statusMap[status] || {\n            color: \"default\",\n            text: \"未知\"\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n            color: config.color,\n            children: config.text\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n            lineNumber: 257,\n            columnNumber: 12\n        }, undefined);\n    };\n    // 计算智能交期\n    const calculateDeliveryDate = (productModelCode, quantity)=>{\n        const inventory = productInventory[productModelCode];\n        if (!inventory) return \"待确认\";\n        const { stock, dailyCapacity } = inventory;\n        const needProduction = Math.max(0, quantity - stock);\n        const productionDays = Math.ceil(needProduction / dailyCapacity);\n        const totalDays = productionDays + 3;\n        const deliveryDate = new Date();\n        deliveryDate.setDate(deliveryDate.getDate() + totalDays);\n        return deliveryDate.toISOString().split(\"T\")[0];\n    };\n    const convertUnit = (quantity, fromUnit, toUnit, productModelCode)=>{\n        const defaultWeightPerPiece = 12.0;\n        if (fromUnit === \"个\" && toUnit === \"吨\") {\n            return quantity * defaultWeightPerPiece / 1000000;\n        } else if (fromUnit === \"吨\" && toUnit === \"个\") {\n            return quantity * 1000000 / defaultWeightPerPiece;\n        } else if (fromUnit === \"个\" && toUnit === \"克\") {\n            return quantity * defaultWeightPerPiece;\n        } else if (fromUnit === \"克\" && toUnit === \"个\") {\n            return quantity / defaultWeightPerPiece;\n        }\n        return quantity;\n    };\n    // 表格列定义\n    const columns = [\n        {\n            title: \"销售订单号\",\n            dataIndex: \"orderNumber\",\n            key: \"orderNumber\",\n            width: 140,\n            fixed: \"left\",\n            render: (orderNumber, record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                    type: \"link\",\n                    onClick: ()=>handleViewDetail(record),\n                    style: {\n                        padding: 0,\n                        height: \"auto\",\n                        fontWeight: \"bold\"\n                    },\n                    children: orderNumber\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                    lineNumber: 300,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: \"订单状态\",\n            dataIndex: \"status\",\n            key: \"status\",\n            width: 100,\n            render: (status)=>getStatusTag(status)\n        },\n        {\n            title: \"创建时间\",\n            dataIndex: \"createdAt\",\n            key: \"createdAt\",\n            width: 160,\n            sorter: (a, b)=>new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),\n            defaultSortOrder: \"descend\",\n            render: (createdAt)=>{\n                if (!createdAt) return \"-\";\n                return dayjs__WEBPACK_IMPORTED_MODULE_5___default()(createdAt).format(\"YYYY-MM-DD HH:mm:ss\");\n            }\n        },\n        {\n            title: \"客户名称\",\n            dataIndex: \"customerName\",\n            key: \"customerName\",\n            width: 180,\n            ellipsis: true\n        },\n        {\n            title: \"订单日期\",\n            dataIndex: \"orderDate\",\n            key: \"orderDate\",\n            width: 120,\n            sorter: (a, b)=>new Date(a.orderDate).getTime() - new Date(b.orderDate).getTime()\n        },\n        {\n            title: \"订单金额\",\n            dataIndex: \"finalAmount\",\n            key: \"finalAmount\",\n            width: 150,\n            sorter: (a, b)=>a.finalAmount - b.finalAmount,\n            render: (finalAmount, record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontWeight: \"bold\",\n                                color: \"#1890ff\"\n                            },\n                            children: [\n                                \"\\xa5\",\n                                (finalAmount || 0).toLocaleString(\"zh-CN\", {\n                                    minimumFractionDigits: 2,\n                                    maximumFractionDigits: 2\n                                })\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 11\n                        }, undefined),\n                        (record.discountAmount || 0) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: \"12px\",\n                                color: \"#666\"\n                            },\n                            children: [\n                                \"折扣: \\xa5\",\n                                (record.discountAmount || 0).toLocaleString(\"zh-CN\", {\n                                    minimumFractionDigits: 2,\n                                    maximumFractionDigits: 2\n                                })\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 354,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                    lineNumber: 349,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: \"生产状态\",\n            dataIndex: \"productionStatus\",\n            key: \"productionStatus\",\n            width: 120,\n            render: (productionStatus, record)=>getProductionStatusTag(record.status, productionStatus)\n        },\n        {\n            title: \"付款状态\",\n            dataIndex: \"paymentStatus\",\n            key: \"paymentStatus\",\n            width: 100,\n            render: (status)=>getPaymentStatusTag(status)\n        },\n        {\n            title: \"变更次数\",\n            key: \"changeCount\",\n            width: 100,\n            render: (_, record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: record.changes.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                        title: \"点击查看变更历史\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                            count: record.changes.length,\n                            size: \"small\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                type: \"link\",\n                                size: \"small\",\n                                children: [\n                                    record.changes.length,\n                                    \"次\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 384,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                        lineNumber: 383,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: {\n                            color: \"#666\"\n                        },\n                        children: \"无\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                        lineNumber: 391,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                    lineNumber: 381,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: \"操作\",\n            key: \"action\",\n            width: 180,\n            fixed: \"right\",\n            render: (_, record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                    size: \"small\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                            type: \"link\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_ExportOutlined_PlusOutlined_SearchOutlined_SwapOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 19\n                            }, void 0),\n                            onClick: ()=>handleOrderChange(record),\n                            children: \"变更\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                            title: \"确定要删除这个订单吗？\",\n                            onConfirm: ()=>handleDelete(record.id),\n                            okText: \"确定\",\n                            cancelText: \"取消\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                type: \"link\",\n                                danger: true,\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_ExportOutlined_PlusOutlined_SearchOutlined_SwapOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                    lineNumber: 417,\n                                    columnNumber: 46\n                                }, void 0),\n                                children: \"删除\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 411,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                    lineNumber: 402,\n                    columnNumber: 9\n                }, undefined)\n        }\n    ];\n    // 过滤后的订单数据\n    const filteredOrders = orders.filter((order)=>{\n        const matchesSearch = !searchText || order.orderNumber.toLowerCase().includes(searchText.toLowerCase()) || order.customerName.toLowerCase().includes(searchText.toLowerCase()) || order.customerContact.toLowerCase().includes(searchText.toLowerCase());\n        const matchesStatus = !filterStatus || order.status === filterStatus;\n        const matchesProductionStatus = !filterProductionStatus || order.productionStatus === filterProductionStatus;\n        // 日期范围过滤\n        const matchesDateRange = !dateRange || !dateRange[0] || !dateRange[1] || new Date(order.orderDate) >= dateRange[0].toDate() && new Date(order.orderDate) <= dateRange[1].toDate();\n        return matchesSearch && matchesStatus && matchesProductionStatus && matchesDateRange;\n    });\n    // 统计数据\n    const stats = {\n        total: orders.length,\n        pending: orders.filter((o)=>o.status === \"pending\").length,\n        confirmed: orders.filter((o)=>o.status === \"confirmed\").length,\n        completed: orders.filter((o)=>o.status === \"completed\").length,\n        cancelled: orders.filter((o)=>o.status === \"cancelled\").length,\n        totalAmount: orders.reduce((sum, o)=>sum + o.finalAmount, 0),\n        delayedOrders: orders.filter((o)=>new Date(o.deliveryDate) < new Date() && o.status !== \"completed\" && o.status !== \"cancelled\").length\n    };\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleViewDetail = (order)=>{\n        setSelectedOrder(order);\n        setIsDetailModalVisible(true);\n    };\n    const handleOrderChange = (order)=>{\n        setSelectedOrder(order);\n        setSelectedChangeType(\"\") // 重置变更类型\n        ;\n        setIsChangeModalVisible(true);\n        changeForm.resetFields();\n    };\n    // 🔧 新增：根据变更类型自动填充原始值\n    const handleChangeTypeSelect = (changeType)=>{\n        if (!selectedOrder) return;\n        setSelectedChangeType(changeType) // 更新选择的变更类型\n        ;\n        let originalValue = \"\";\n        switch(changeType){\n            case \"quantity\":\n                var _selectedOrder_items;\n                // 获取订单总数量\n                const totalQuantity = ((_selectedOrder_items = selectedOrder.items) === null || _selectedOrder_items === void 0 ? void 0 : _selectedOrder_items.reduce((sum, item)=>sum + item.quantity, 0)) || 0;\n                originalValue = totalQuantity.toString();\n                break;\n            case \"delivery_date\":\n                // 获取交期，格式化为YYYY-MM-DD\n                try {\n                    const date = new Date(selectedOrder.deliveryDate);\n                    originalValue = date.toISOString().split(\"T\")[0] // 格式化为YYYY-MM-DD\n                    ;\n                } catch (error) {\n                    originalValue = selectedOrder.deliveryDate.split(\"T\")[0] // 备用方案\n                    ;\n                }\n                break;\n            case \"cancel\":\n                // 订单取消时，原始值为当前状态\n                const statusMap = {\n                    \"pending\": \"未审核\",\n                    \"confirmed\": \"已审核\",\n                    \"completed\": \"完成\",\n                    \"cancelled\": \"已取消\"\n                };\n                originalValue = statusMap[selectedOrder.status] || selectedOrder.status;\n                break;\n            default:\n                originalValue = \"\";\n        }\n        // 自动填充原始值，并清空新值\n        changeForm.setFieldsValue({\n            originalValue: originalValue,\n            newValue: \"\" // 清空新值，让用户重新输入\n        });\n    };\n    // 🔧 新增：根据变更类型获取输入提示\n    const getPlaceholderByChangeType = (changeType, field)=>{\n        var _placeholders_changeType;\n        if (!changeType) return field === \"originalValue\" ? \"请输入原始值\" : \"请输入新值\";\n        const placeholders = {\n            quantity: {\n                originalValue: \"当前订单总数量\",\n                newValue: \"请输入新的数量\"\n            },\n            delivery_date: {\n                originalValue: \"当前交期日期\",\n                newValue: \"请选择新的交期日期\"\n            },\n            cancel: {\n                originalValue: \"当前订单状态\",\n                newValue: \"取消原因\"\n            }\n        };\n        return ((_placeholders_changeType = placeholders[changeType]) === null || _placeholders_changeType === void 0 ? void 0 : _placeholders_changeType[field]) || (field === \"originalValue\" ? \"请输入原始值\" : \"请输入新值\");\n    };\n    // 🔧 新增：根据变更类型渲染不同的输入组件\n    const renderInputByChangeType = (changeType, field)=>{\n        const placeholder = getPlaceholderByChangeType(changeType, field);\n        const isReadOnly = field === \"originalValue\" && !!selectedChangeType;\n        switch(changeType){\n            case \"quantity\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                    placeholder: placeholder,\n                    min: 0,\n                    style: {\n                        width: \"100%\"\n                    },\n                    readOnly: isReadOnly\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                    lineNumber: 547,\n                    columnNumber: 11\n                }, undefined);\n            case \"delivery_date\":\n                if (field === \"originalValue\") {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                        placeholder: placeholder,\n                        readOnly: true\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                        lineNumber: 556,\n                        columnNumber: 18\n                    }, undefined);\n                } else {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                        placeholder: placeholder,\n                        style: {\n                            width: \"100%\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                        lineNumber: 558,\n                        columnNumber: 18\n                    }, undefined);\n                }\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    placeholder: placeholder,\n                    readOnly: isReadOnly\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                    lineNumber: 563,\n                    columnNumber: 11\n                }, undefined);\n        }\n    };\n    const handleDelete = async (id)=>{\n        const result = await (0,_utils_dataAccessErrorHandler__WEBPACK_IMPORTED_MODULE_4__.handleApiResponse)(()=>_services_dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_3__.dataAccessManager.orders.delete(id), \"删除订单\");\n        if (result !== null) {\n            await refreshOrders();\n            message.success(\"订单删除成功\");\n        } else {\n            message.error(\"删除订单失败\");\n        }\n    };\n    // 新增订单处理函数\n    const handleAddOrder = ()=>{\n        setIsAddOrderModalVisible(true);\n    };\n    const handleAddOrderSuccess = async (newOrder)=>{\n        // 刷新订单列表以获取最新数据\n        await refreshOrders();\n        message.success(\"订单创建成功\");\n        setIsAddOrderModalVisible(false);\n    };\n    const handleAddOrderCancel = ()=>{\n        setIsAddOrderModalVisible(false);\n    };\n    const handleStartMRP = async (order)=>{\n        try {\n            // 验证前置条件\n            if (order.status !== \"confirmed\") {\n                message.error(\"只有已审核的订单才能启动MRP\");\n                return;\n            }\n            if (order.mrpStatus === \"completed\") {\n                message.warning(\"该订单的MRP已经执行完成\");\n                return;\n            }\n            if (order.mrpStatus === \"in_progress\") {\n                message.warning(\"该订单的MRP正在执行中，请等待完成\");\n                return;\n            }\n            // 开始MRP执行\n            setMRPExecuting(true);\n            setMrpExecutionStep(1);\n            setShowMRPResult(false);\n            setMrpResult(null);\n            // 更新订单MRP状态为执行中\n            try {\n                await _services_dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_3__.dataAccessManager.orders.update(order.id, {\n                    mrpStatus: \"in_progress\",\n                    updatedAt: new Date().toISOString()\n                });\n                // 🔧 修复：立即更新selectedOrder状态，确保按钮立即禁用\n                if (selectedOrder && selectedOrder.id === order.id) {\n                    setSelectedOrder({\n                        ...selectedOrder,\n                        mrpStatus: \"in_progress\",\n                        updatedAt: new Date().toISOString()\n                    });\n                }\n                // 立即刷新本地状态\n                await refreshOrders();\n            } catch (error) {\n                console.error(\"更新订单MRP状态失败:\", error);\n            }\n            // 动态导入MRP服务\n            const { mrpService } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_services_mrpService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/services/mrpService */ \"(app-pages-browser)/./src/services/mrpService.ts\"));\n            // 步骤1: 启动MRP\n            message.info(\"正在启动MRP...\");\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            setMrpExecutionStep(2);\n            // 步骤2: MRP分析\n            message.info(\"正在进行MRP分析...\");\n            await new Promise((resolve)=>setTimeout(resolve, 1500));\n            setMrpExecutionStep(3);\n            // 步骤3: 生成生产订单\n            message.info(\"正在生成生产订单...\");\n            // 执行MRP\n            const mrpResult = await mrpService.executeMRP({\n                salesOrder: order,\n                executedBy: \"当前用户\",\n                executionDate: new Date().toISOString()\n            });\n            setMrpExecutionStep(4);\n            await new Promise((resolve)=>setTimeout(resolve, 500));\n            // 步骤4: 完成\n            setMRPExecuting(false);\n            setMrpResult(mrpResult);\n            setShowMRPResult(true);\n            // 更新订单MRP状态为已完成\n            try {\n                await _services_dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_3__.dataAccessManager.orders.update(order.id, {\n                    mrpStatus: \"completed\",\n                    mrpExecutedAt: new Date().toISOString(),\n                    mrpExecutedBy: \"当前用户\",\n                    mrpResultId: mrpResult.id,\n                    updatedAt: new Date().toISOString()\n                });\n                // 🔧 修复：立即更新selectedOrder状态，确保按钮状态正确\n                if (selectedOrder && selectedOrder.id === order.id) {\n                    setSelectedOrder({\n                        ...selectedOrder,\n                        mrpStatus: \"completed\",\n                        mrpExecutedAt: new Date().toISOString(),\n                        mrpExecutedBy: \"当前用户\",\n                        mrpResultId: mrpResult.id,\n                        updatedAt: new Date().toISOString()\n                    });\n                }\n            } catch (error) {\n                console.error(\"更新订单MRP完成状态失败:\", error);\n            }\n            // 步骤5: MRP执行完成，刷新订单数据\n            await refreshOrders() // 刷新订单列表以获取最新状态\n            ;\n            if (mrpResult.generatedProductionOrders && mrpResult.generatedProductionOrders.length > 0) {\n                message.success(\"MRP执行完成！生成了 \".concat(mrpResult.totalProductionOrders, \" 个生产订单，请前往生产管理模块查看\"));\n            } else {\n                message.success(\"MRP执行完成！未生成新的生产订单（可能库存充足）\");\n            }\n        } catch (error) {\n            setMRPExecuting(false);\n            // 更新订单MRP状态为失败\n            const updateResult = await (0,_utils_dataAccessErrorHandler__WEBPACK_IMPORTED_MODULE_4__.handleApiResponse)(()=>_services_dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_3__.dataAccessManager.orders.update(order.id, {\n                    mrpStatus: \"failed\",\n                    updatedAt: new Date().toISOString()\n                }), \"更新订单MRP状态\");\n            if (updateResult) {\n                // 🔧 修复：立即更新selectedOrder状态\n                if (selectedOrder && selectedOrder.id === order.id) {\n                    setSelectedOrder({\n                        ...selectedOrder,\n                        mrpStatus: \"failed\",\n                        updatedAt: new Date().toISOString()\n                    });\n                }\n                await refreshOrders();\n            }\n            message.error(\"MRP执行失败: \".concat(error instanceof Error ? error.message : \"未知错误\"));\n        }\n    };\n    const handleChangeModalOk = ()=>{\n        changeForm.validateFields().then(async (values)=>{\n            if (!selectedOrder) return;\n            // 🔧 P4-3数据验证统一：使用OrderValidationService验证订单变更\n            const changeValidation = _services_validation_OrderValidationService__WEBPACK_IMPORTED_MODULE_16__[\"default\"].validateOrderChange(selectedOrder, values.changeType, values.originalValue, values.newValue);\n            if (!changeValidation.isValid) {\n                message.error(\"变更验证失败：\".concat(changeValidation.errors[0]));\n                return;\n            }\n            // 显示警告信息（如果有）\n            if (changeValidation.warnings && changeValidation.warnings.length > 0) {\n                changeValidation.warnings.forEach((warning)=>{\n                    message.warning(warning);\n                });\n            }\n            const now = new Date().toISOString();\n            const newChange = {\n                id: Date.now().toString(),\n                orderNumber: selectedOrder.orderNumber,\n                ...values,\n                changeStatus: \"pending\",\n                applicant: \"当前用户\",\n                customerConfirmed: false,\n                productionStatus: selectedOrder.productionStatus,\n                createdAt: now\n            };\n            // 更新订单变更记录\n            const currentOrder = orders.find((o)=>o.id === selectedOrder.id);\n            if (currentOrder) {\n                const updatedChanges = [\n                    ...currentOrder.changes || [],\n                    newChange\n                ];\n                const result = await (0,_utils_dataAccessErrorHandler__WEBPACK_IMPORTED_MODULE_4__.handleApiResponse)(()=>_services_dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_3__.dataAccessManager.orders.update(selectedOrder.id, {\n                        changes: updatedChanges,\n                        updatedAt: now\n                    }), \"提交变更申请\");\n                if (result) {\n                    await refreshOrders();\n                    setIsChangeModalVisible(false);\n                    changeForm.resetFields();\n                    message.success(\"订单变更申请提交成功\");\n                } else {\n                    message.error(\"提交变更申请失败\");\n                }\n            }\n        });\n    };\n    const handleChangeModalCancel = ()=>{\n        setIsChangeModalVisible(false);\n        setSelectedChangeType(\"\") // 重置变更类型\n        ;\n        changeForm.resetFields();\n    };\n    // 处理变更审批\n    const handleChangeApproval = async (changeId, action, reason)=>{\n        if (!selectedOrder) return;\n        try {\n            var _currentOrder_changes;\n            const now = new Date().toISOString();\n            const currentOrder = orders.find((o)=>o.id === selectedOrder.id);\n            if (!currentOrder) return;\n            // 更新变更记录状态\n            const updatedChanges = ((_currentOrder_changes = currentOrder.changes) === null || _currentOrder_changes === void 0 ? void 0 : _currentOrder_changes.map((change)=>{\n                if (change.id === changeId) {\n                    return {\n                        ...change,\n                        changeStatus: action === \"approve\" ? \"approved\" : \"rejected\",\n                        approver: \"当前用户\",\n                        approvedAt: now,\n                        ...reason && {\n                            rejectionReason: reason\n                        }\n                    };\n                }\n                return change;\n            })) || [];\n            // 如果变更被批准，需要执行相应的变更逻辑\n            const approvedChange = updatedChanges.find((c)=>c.id === changeId);\n            if (action === \"approve\" && approvedChange) {\n                switch(approvedChange.changeType){\n                    case \"cancel\":\n                        // 验证订单是否可以取消\n                        const cancelValidation = await _services_OrderCancellationService__WEBPACK_IMPORTED_MODULE_8__.OrderCancellationService.validateCancellation(selectedOrder);\n                        if (!cancelValidation.canCancel) {\n                            message.error(\"无法取消订单: \".concat(cancelValidation.reason));\n                            return;\n                        }\n                        if (cancelValidation.warnings.length > 0) {\n                            // 显示警告信息，让用户确认\n                            await new Promise((resolve, reject)=>{\n                                modal.confirm({\n                                    title: \"订单取消确认\",\n                                    content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"确定要取消此订单吗？\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                                lineNumber: 851,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                style: {\n                                                    marginTop: 8,\n                                                    paddingLeft: 20\n                                                },\n                                                children: cancelValidation.warnings.map((warning, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        style: {\n                                                            color: \"#fa8c16\"\n                                                        },\n                                                        children: warning\n                                                    }, index, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 854,\n                                                        columnNumber: 27\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                                lineNumber: 852,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                        lineNumber: 850,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    onOk: ()=>resolve(),\n                                    onCancel: ()=>reject(new Error(\"用户取消操作\"))\n                                });\n                            });\n                        }\n                        await handleOrderCancellation(selectedOrder, approvedChange);\n                        break;\n                    case \"quantity\":\n                        // 执行数量变更\n                        await handleQuantityChange(selectedOrder, approvedChange);\n                        break;\n                    case \"delivery_date\":\n                        // 执行交期变更\n                        await handleDeliveryDateChange(selectedOrder, approvedChange);\n                        break;\n                }\n                // 更新变更记录状态为已执行\n                const finalChanges = updatedChanges.map((change)=>{\n                    if (change.id === changeId) {\n                        return {\n                            ...change,\n                            changeStatus: \"executed\",\n                            executedAt: new Date().toISOString()\n                        };\n                    }\n                    return change;\n                });\n                // 更新订单变更记录\n                const updateResult = await (0,_utils_dataAccessErrorHandler__WEBPACK_IMPORTED_MODULE_4__.handleApiResponse)(()=>_services_dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_3__.dataAccessManager.orders.update(selectedOrder.id, {\n                        changes: finalChanges,\n                        updatedAt: now\n                    }), \"更新订单变更记录\");\n                if (!updateResult) {\n                    message.error(\"变更\".concat(action === \"approve\" ? \"批准\" : \"拒绝\", \"失败\"));\n                    return;\n                }\n            } else {\n                // 如果是拒绝变更，只更新变更记录\n                const updateResult = await (0,_utils_dataAccessErrorHandler__WEBPACK_IMPORTED_MODULE_4__.handleApiResponse)(()=>_services_dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_3__.dataAccessManager.orders.update(selectedOrder.id, {\n                        changes: updatedChanges,\n                        updatedAt: now\n                    }), \"更新变更记录\");\n                if (!updateResult) {\n                    message.error(\"变更\".concat(action === \"approve\" ? \"批准\" : \"拒绝\", \"失败\"));\n                    return;\n                }\n            }\n            await refreshOrders();\n            message.success(\"变更\".concat(action === \"approve\" ? \"批准\" : \"拒绝\", \"成功\"));\n        } catch (error) {\n            message.error(\"变更\".concat(action === \"approve\" ? \"批准\" : \"拒绝\", \"失败: \").concat(error instanceof Error ? error.message : \"未知错误\"));\n            console.error(\"变更审批失败:\", error);\n        }\n    };\n    // 处理订单取消的核心逻辑\n    const handleOrderCancellation = async (order, change)=>{\n        try {\n            // 使用专门的订单取消服务\n            const result = await _services_OrderCancellationService__WEBPACK_IMPORTED_MODULE_8__.OrderCancellationService.executeOrderCancellation(order, change);\n            if (result.success) {\n                message.success(\"订单 \".concat(order.orderNumber, \" 已成功取消，\") + \"同时取消了 \".concat(result.productionOrdersCancelled, \" 个生产订单和 \").concat(result.workOrdersCancelled, \" 个工单\"));\n            } else {\n                throw new Error(result.errors.join(\"; \"));\n            }\n        } catch (error) {\n            console.error(\"订单取消处理失败:\", error);\n            throw error;\n        }\n    };\n    // 处理数量变更的核心逻辑\n    const handleQuantityChange = async (order, change)=>{\n        try {\n            // 使用专门的数量变更服务\n            const result = await _services_OrderQuantityChangeService__WEBPACK_IMPORTED_MODULE_9__.OrderQuantityChangeService.executeQuantityChange(order, change);\n            if (result.success) {\n                message.success(\"订单 \".concat(order.orderNumber, \" 数量变更成功，\") + \"从 \".concat(change.originalValue, \" 变更为 \").concat(change.newValue, \"，\") + \"影响了 \".concat(result.productionOrdersAffected, \" 个生产订单和 \").concat(result.workOrdersAffected, \" 个工单\"));\n            } else {\n                throw new Error(result.errors.join(\"; \"));\n            }\n        } catch (error) {\n            console.error(\"数量变更执行失败:\", error);\n            throw error;\n        }\n    };\n    // 处理交期变更的核心逻辑\n    const handleDeliveryDateChange = async (order, change)=>{\n        try {\n            // 使用专门的交期变更服务\n            const result = await _services_OrderDeliveryDateChangeService__WEBPACK_IMPORTED_MODULE_10__.OrderDeliveryDateChangeService.executeDeliveryDateChange(order, change);\n            if (result.success) {\n                const successMessage = \"订单 \".concat(order.orderNumber, \" 交期变更成功，\") + \"从 \".concat(change.originalValue, \" 变更为 \").concat(change.newValue, \"，\") + \"影响了 \".concat(result.productionOrdersAffected, \" 个生产订单和 \").concat(result.workOrdersAffected, \" 个工单\");\n                if (result.scheduleAdjustmentsRequired) {\n                    message.warning(successMessage + \"\\n注意：有 \".concat(result.scheduleImpact.conflictingOrders.length, \" 个订单的排程需要调整\"));\n                } else {\n                    message.success(successMessage);\n                }\n            } else {\n                throw new Error(result.errors.join(\"; \"));\n            }\n        } catch (error) {\n            console.error(\"交期变更执行失败:\", error);\n            throw error;\n        }\n    };\n    // 批量操作函数\n    const handleBatchApprove = ()=>{\n        if (selectedRowKeys.length === 0) {\n            message.warning(\"请先选择要审核的订单\");\n            return;\n        }\n        const pendingOrders = selectedOrders.filter((order)=>order.status === \"pending\");\n        if (pendingOrders.length === 0) {\n            message.warning(\"所选订单中没有未审核的订单\");\n            return;\n        }\n        modal.confirm({\n            title: \"批量审核确认\",\n            content: \"确定要审核 \".concat(pendingOrders.length, \" 个订单吗？\"),\n            onOk: async ()=>{\n                setBatchLoading(true);\n                try {\n                    // 批量更新订单状态\n                    const updatePromises = pendingOrders.map((order)=>(0,_utils_dataAccessErrorHandler__WEBPACK_IMPORTED_MODULE_4__.handleApiResponse)(()=>_services_dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_3__.dataAccessManager.orders.update(order.id, {\n                                status: \"confirmed\",\n                                productionStatus: \"not_started\",\n                                updatedAt: new Date().toISOString()\n                            }), \"审核订单 \".concat(order.orderNumber)));\n                    const results = await Promise.all(updatePromises);\n                    const successCount = results.filter((result)=>result !== null).length;\n                    if (successCount > 0) {\n                        await refreshOrders();\n                        setSelectedRowKeys([]);\n                        setSelectedOrders([]);\n                        message.success(\"成功审核 \".concat(successCount, \" 个订单\"));\n                    } else {\n                        message.error(\"批量审核失败，没有订单被成功审核\");\n                    }\n                } catch (error) {\n                    message.error(\"批量审核失败: \".concat(error instanceof Error ? error.message : \"未知错误\"));\n                    console.error(\"批量审核异常:\", error);\n                } finally{\n                    setBatchLoading(false);\n                }\n            }\n        });\n    };\n    const handleBatchUnapprove = ()=>{\n        if (selectedRowKeys.length === 0) {\n            message.warning(\"请先选择要反审核的订单\");\n            return;\n        }\n        const confirmedOrders = selectedOrders.filter((order)=>order.status === \"confirmed\");\n        if (confirmedOrders.length === 0) {\n            message.warning(\"所选订单中没有已审核的订单\");\n            return;\n        }\n        modal.confirm({\n            title: \"批量反审核确认\",\n            content: \"确定要反审核 \".concat(confirmedOrders.length, \" 个订单吗？\"),\n            onOk: async ()=>{\n                setBatchLoading(true);\n                try {\n                    // 批量更新订单状态\n                    const updatePromises = confirmedOrders.map((order)=>(0,_utils_dataAccessErrorHandler__WEBPACK_IMPORTED_MODULE_4__.handleApiResponse)(()=>_services_dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_3__.dataAccessManager.orders.update(order.id, {\n                                status: \"pending\",\n                                productionStatus: \"not_started\",\n                                updatedAt: new Date().toISOString()\n                            }), \"反审核订单 \".concat(order.orderNumber)));\n                    const results = await Promise.all(updatePromises);\n                    const successCount = results.filter((result)=>result !== null).length;\n                    if (successCount > 0) {\n                        await refreshOrders();\n                        setSelectedRowKeys([]);\n                        setSelectedOrders([]);\n                        message.success(\"成功反审核 \".concat(successCount, \" 个订单\"));\n                    } else {\n                        message.error(\"批量反审核失败，没有订单被成功反审核\");\n                    }\n                } catch (error) {\n                    message.error(\"批量反审核失败: \".concat(error instanceof Error ? error.message : \"未知错误\"));\n                    console.error(\"批量反审核异常:\", error);\n                } finally{\n                    setBatchLoading(false);\n                }\n            }\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"24px\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                gutter: [\n                    16,\n                    16\n                ],\n                style: {\n                    marginBottom: \"24px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                        xs: 24,\n                        sm: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                title: \"订单总数\",\n                                value: stats.total,\n                                suffix: \"个\",\n                                valueStyle: {\n                                    color: \"#1890ff\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 1105,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 1104,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                        lineNumber: 1103,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                        xs: 24,\n                        sm: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                title: \"待审核\",\n                                value: stats.pending,\n                                suffix: \"个\",\n                                valueStyle: {\n                                    color: \"#faad14\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 1115,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 1114,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                        lineNumber: 1113,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                        xs: 24,\n                        sm: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                title: \"已确认\",\n                                value: stats.confirmed,\n                                suffix: \"个\",\n                                valueStyle: {\n                                    color: \"#52c41a\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 1125,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 1124,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                        lineNumber: 1123,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                        xs: 24,\n                        sm: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                title: \"总金额\",\n                                value: stats.totalAmount,\n                                precision: 2,\n                                prefix: \"\\xa5\",\n                                valueStyle: {\n                                    color: \"#f50\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 1135,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 1134,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                        lineNumber: 1133,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                lineNumber: 1102,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                style: {\n                    marginBottom: \"16px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                    gutter: [\n                        16,\n                        16\n                    ],\n                    align: \"middle\",\n                    justify: \"space-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                            xs: 24,\n                            lg: 18,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                wrap: true,\n                                size: \"middle\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        placeholder: \"搜索订单号、客户名称\",\n                                        prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_ExportOutlined_PlusOutlined_SearchOutlined_SwapOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1153,\n                                            columnNumber: 25\n                                        }, void 0),\n                                        value: searchText,\n                                        onChange: (e)=>setSearchText(e.target.value),\n                                        style: {\n                                            width: \"256px\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                        lineNumber: 1151,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        placeholder: \"订单状态\",\n                                        value: filterStatus,\n                                        onChange: setFilterStatus,\n                                        style: {\n                                            width: \"128px\"\n                                        },\n                                        allowClear: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                                value: \"pending\",\n                                                children: \"待审核\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                                lineNumber: 1165,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                                value: \"confirmed\",\n                                                children: \"已确认\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                                lineNumber: 1166,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                                value: \"completed\",\n                                                children: \"已完成\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                                lineNumber: 1167,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                                value: \"cancelled\",\n                                                children: \"已取消\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                                lineNumber: 1168,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                        lineNumber: 1158,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RangePicker, {\n                                        value: dateRange,\n                                        onChange: setDateRange,\n                                        placeholder: [\n                                            \"开始日期\",\n                                            \"结束日期\"\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                        lineNumber: 1170,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 1150,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 1149,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                            xs: 24,\n                            lg: 6,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_ExportOutlined_PlusOutlined_SearchOutlined_SwapOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1179,\n                                            columnNumber: 29\n                                        }, void 0),\n                                        children: \"导出\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                        lineNumber: 1179,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                        type: \"primary\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_ExportOutlined_PlusOutlined_SearchOutlined_SwapOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1182,\n                                            columnNumber: 23\n                                        }, void 0),\n                                        onClick: ()=>setIsAddOrderModalVisible(true),\n                                        children: \"新建订单\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                        lineNumber: 1180,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 1178,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 1177,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                    lineNumber: 1148,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                lineNumber: 1147,\n                columnNumber: 7\n            }, undefined),\n            selectedRowKeys.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                style: {\n                    marginBottom: \"16px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_41__[\"default\"], {\n                    message: \"已选择 \".concat(selectedRowKeys.length, \" 个订单\"),\n                    type: \"info\",\n                    showIcon: true,\n                    action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                size: \"small\",\n                                onClick: handleBatchApprove,\n                                loading: batchLoading,\n                                children: \"批量审核\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 1201,\n                                columnNumber: 17\n                            }, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                size: \"small\",\n                                onClick: handleBatchUnapprove,\n                                loading: batchLoading,\n                                children: \"批量反审核\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 1208,\n                                columnNumber: 17\n                            }, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                size: \"small\",\n                                danger: true,\n                                onClick: handleBatchDelete,\n                                loading: batchLoading,\n                                children: \"批量删除\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 1215,\n                                columnNumber: 17\n                            }, void 0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                        lineNumber: 1200,\n                        columnNumber: 15\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                    lineNumber: 1195,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                lineNumber: 1194,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                title: \"销售订单列表\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_42__[\"default\"], {\n                    columns: columns,\n                    dataSource: filteredOrders,\n                    rowKey: \"id\",\n                    loading: ordersLoading || loading,\n                    rowSelection: {\n                        selectedRowKeys,\n                        onChange: (keys, rows)=>{\n                            setSelectedRowKeys(keys);\n                            setSelectedOrders(rows);\n                        },\n                        getCheckboxProps: (record)=>({\n                                disabled: record.status === \"completed\"\n                            })\n                    },\n                    pagination: {\n                        total: filteredOrders.length,\n                        pageSize: 10,\n                        showSizeChanger: true,\n                        showQuickJumper: true,\n                        showTotal: (total, range)=>\"第 \".concat(range[0], \"-\").concat(range[1], \" 条/共 \").concat(total, \" 条\"),\n                        pageSizeOptions: [\n                            \"10\",\n                            \"20\",\n                            \"50\",\n                            \"100\"\n                        ]\n                    },\n                    scroll: {\n                        x: 1600\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                    lineNumber: 1231,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                lineNumber: 1230,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_OrderDetailModal__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                visible: isDetailModalVisible,\n                onCancel: ()=>setIsDetailModalVisible(false),\n                order: selectedOrder,\n                config: _components_common_OrderDetailModal_configs_salesOrderConfig__WEBPACK_IMPORTED_MODULE_18__[\"default\"]\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                lineNumber: 1259,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sales_AddOrderModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                visible: isAddOrderModalVisible,\n                onCancel: ()=>setIsAddOrderModalVisible(false),\n                onSuccess: ()=>{\n                    setIsAddOrderModalVisible(false);\n                    refreshOrders();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                lineNumber: 1267,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_43__[\"default\"], {\n                title: \"订单变更\",\n                open: isChangeModalVisible,\n                onOk: handleChangeSubmit,\n                onCancel: ()=>setIsChangeModalVisible(false),\n                width: 600,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                    form: changeForm,\n                    layout: \"vertical\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"].Item, {\n                            name: \"changeType\",\n                            label: \"变更类型\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择变更类型\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                placeholder: \"请选择变更类型\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"quantity\",\n                                        children: \"数量变更\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                        lineNumber: 1291,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"deliveryDate\",\n                                        children: \"交期变更\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                        lineNumber: 1292,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"cancel\",\n                                        children: \"订单取消\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                        lineNumber: 1293,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 1290,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 1285,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"].Item, {\n                            name: \"reason\",\n                            label: \"变更原因\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入变更原因\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TextArea, {\n                                rows: 4,\n                                placeholder: \"请详细说明变更原因\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 1301,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 1296,\n                            columnNumber: 11\n                        }, undefined),\n                        selectedChangeType === \"quantity\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"].Item, {\n                            name: \"newQuantity\",\n                            label: \"新数量\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入新数量\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                min: 1,\n                                style: {\n                                    width: \"100%\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 1309,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 1304,\n                            columnNumber: 13\n                        }, undefined),\n                        selectedChangeType === \"deliveryDate\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"].Item, {\n                            name: \"newDeliveryDate\",\n                            label: \"新交期\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择新交期\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                style: {\n                                    width: \"100%\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 1318,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 1313,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                    lineNumber: 1284,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                lineNumber: 1277,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n        lineNumber: 1100,\n        columnNumber: 5\n    }, undefined);\n};\n_s(OrderManagement, \"ulpOxUkaGHBxc+E0GASVMjRmbxw=\", false, function() {\n    return [\n        _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"].useApp,\n        _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"].useForm,\n        _hooks_useOrdersData__WEBPACK_IMPORTED_MODULE_14__.useOrdersData,\n        _hooks_useEventListener__WEBPACK_IMPORTED_MODULE_12__.useDataChangeListener,\n        _hooks_useDebouncedCallback__WEBPACK_IMPORTED_MODULE_11__.useDebouncedSearch,\n        _hooks_useDataAccessMonitor__WEBPACK_IMPORTED_MODULE_13__.useDataAccessMonitor,\n        _hooks_useProductionOrders__WEBPACK_IMPORTED_MODULE_7__.useMRPProductionOrders,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = OrderManagement;\nfunction OrderManagementPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(OrderManagement, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n            lineNumber: 1330,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n        lineNumber: 1329,\n        columnNumber: 5\n    }, this);\n}\n_c1 = OrderManagementPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"OrderManagement\");\n$RefreshReg$(_c1, \"OrderManagementPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/sales/orders/page.tsx\n"));

/***/ })

});