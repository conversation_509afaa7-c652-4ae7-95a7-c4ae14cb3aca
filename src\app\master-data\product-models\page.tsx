'use client'

import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Input,
  Select,
  Modal,
  Form,
  InputNumber,
  Row,
  Col,
  Statistic,
  Popconfirm,
  Descriptions,
  Tabs,
  App,
  Divider
} from 'antd'
import { 
  PlusOutlined, 
  SearchOutlined, 
  EditOutlined, 
  DeleteOutlined,
  EyeOutlined,
  ExportOutlined,
  ImportOutlined,
  BarcodeOutlined,
  ToolOutlined,
  DollarOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import { ProductModel } from '@/types'
import { styleHelpers } from '@/utils/styles/antdHelpers'
// ✅ 架构合规：移除违规的useMasterDataStore，使用DataAccessManager统一数据访问
import { dataAccessManager } from '@/services/dataAccess/DataAccessManager'
import { handleApiResponse } from '@/utils/dataAccessErrorHandler'
import { ValidationUtils } from '@/utils/validationUtils'

const { Option } = Select

const ProductModelsManagement: React.FC = () => {
  const { message } = App.useApp()
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false)
  const [editingModel, setEditingModel] = useState<ProductModel | null>(null)
  const [viewingModel, setViewingModel] = useState<ProductModel | null>(null)
  const [currentFormingMold, setCurrentFormingMold] = useState<string>('')
  const [isClient, setIsClient] = useState(false)
  const [statusFilter, setStatusFilter] = useState<string | undefined>(undefined)
  const [form] = Form.useForm()

  // ✅ 架构合规：使用本地状态管理替代Store
  const [productModels, setProductModels] = useState<ProductModel[]>([])
  const [loading, setLoading] = useState(false)

  // ✅ 架构合规：使用DataAccessManager加载产品数据
  const refreshProductModels = async () => {
    setLoading(true)
    try {
      const result = await handleApiResponse(
        () => dataAccessManager.products.getAll(),
        '获取产品数据'
      )
      
      if (result && result.items) {
        setProductModels(result.items)
      }
    } catch (error) {
      console.error('加载产品数据失败:', error)
      message.error('加载产品数据失败')
    } finally {
      setLoading(false)
    }
  }

  // 初始化数据加载
  useEffect(() => {
    setIsClient(true)
    refreshProductModels()
  }, [])

  // 使用本地状态数据
  const productModelsData = productModels

  const statusMap = {
    active: { color: 'green', text: '启用' },
    inactive: { color: 'red', text: '停用' }
  }

  // 生成下一个产品编码（格式：P00001）
  const generateNextProductCode = (): string => {
    const existingCodes = productModelsData.map(model => model.modelCode)
    const pCodes = existingCodes.filter(code => /^P\d{5}$/.test(code))

    if (pCodes.length === 0) {
      return 'P00001'
    }

    const maxNumber = Math.max(...pCodes.map(code => {
      const match = code.match(/^P(\d{5})$/)
      return match ? parseInt(match[1], 10) : 0
    }))

    const nextNumber = maxNumber + 1

    // 5位数范围：P00001 到 P99999
    if (nextNumber > 99999) {
      message.warning('编码已达到最大值P99999，请手动输入编码')
      return ''
    }

    return `P${nextNumber.toString().padStart(5, '0')}`
  }

  // 使用统一验证工具检查编码唯一性
  const checkCodeUniqueness = (code: string, excludeId?: string): boolean => {
    return ValidationUtils.checkProductCodeUniqueness(code, productModelsData, excludeId).isValid
  }

  const columns: ColumnsType<ProductModel> = [
    {
      title: '产品编码',
      dataIndex: 'modelCode',
      key: 'modelCode',
      width: 140,
      fixed: 'left',
    },
    {
      title: '产品名称',
      dataIndex: 'modelName',
      key: 'modelName',
      width: 150,
    },
    {
      title: '成型模具',
      key: 'formingMold',
      width: 180,
      render: (_, record) => {
        const sharedProducts = getProductsUsingMold(record.formingMold, 'forming')
          .filter(p => p.id !== record.id)
        return (
          <div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
              <span>{record.formingMold}</span>
              {sharedProducts.length > 0 && (
                <Tag color="blue">
                  共享 {sharedProducts.length}
                </Tag>
              )}
            </div>
            <div style={{ fontSize: '12px', color: '#9ca3af' }}>{record.formingMoldQuantity}个/模</div>
            {sharedProducts.length > 0 && (
              <div style={{ fontSize: '12px', color: '#1890ff', marginTop: '4px' }}>
                与 {sharedProducts.map(p => p.modelCode).join('、')} 共享
              </div>
            )}
          </div>
        )
      }
    },
    {
      title: '热压模具',
      key: 'hotPressMold',
      width: 150,
      render: (_, record) => (
        <div>
          <div>{record.hotPressMold}</div>
          <div style={{ fontSize: '12px', color: '#9ca3af' }}>{record.hotPressMoldQuantity}个/模</div>
        </div>
      )
    },
    {
      title: '成型单价',
      dataIndex: 'formingPiecePrice',
      key: 'formingPiecePrice',
      width: 120,
      render: (price: number) => `¥${price.toFixed(2)}/模`
    },
    {
      title: '热压单价',
      dataIndex: 'hotPressPiecePrice',
      key: 'hotPressPiecePrice',
      width: 120,
      render: (price: number) => `¥${price.toFixed(2)}/模`
    },
    {
      title: '产品价格',
      dataIndex: 'productPrice',
      key: 'productPrice',
      width: 120,
      render: (price: number) => `¥${price.toFixed(3)}`,
      sorter: (a, b) => a.productPrice - b.productPrice
    },
    {
      title: '产品重量',
      dataIndex: 'productWeight',
      key: 'productWeight',
      width: 120,
      render: (weight: number) => `${weight.toFixed(2)}克`,
      sorter: (a, b) => a.productWeight - b.productWeight
    },
    {
      title: '箱规',
      dataIndex: 'boxSpecification',
      key: 'boxSpecification',
      width: 150,
      ellipsis: true
    },
    {
      title: '装箱数',
      dataIndex: 'packingQuantity',
      key: 'packingQuantity',
      width: 100,
      render: (quantity: number) => `${quantity}个/箱`,
      sorter: (a, b) => a.packingQuantity - b.packingQuantity
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: keyof typeof statusMap) => {
        const statusInfo = statusMap[status]
        return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>
      }
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button 
            type="text" 
            icon={<EyeOutlined />} 
            size="small"
            onClick={() => handleView(record)}
          >
            查看
          </Button>
          <Button 
            type="text" 
            icon={<EditOutlined />} 
            size="small"
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个产品吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="text" danger icon={<DeleteOutlined />} size="small">
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ]

  const handleCreate = () => {
    setEditingModel(null)
    setIsModalVisible(true)
    setCurrentFormingMold('')
    form.resetFields()

    // 自动生成产品物料编码
    const nextCode = generateNextProductCode()
    if (nextCode) {
      form.setFieldsValue({ modelCode: nextCode })
    }
  }

  const handleEdit = (model: ProductModel) => {
    setEditingModel(model)
    setIsModalVisible(true)
    setCurrentFormingMold(model.formingMold)
    form.setFieldsValue(model)
  }

  const handleView = (model: ProductModel) => {
    setViewingModel(model)
    setIsDetailModalVisible(true)
  }

  const handleDelete = async (id: string) => {
    try {
      setLoading(true)
      const response = await dataAccessManager.products.delete(id)

      if (response.status === 'success') {
        // 刷新产品列表
        await refreshProductModels()
        message.success('产品删除成功')
      } else {
        message.error(response.message || '删除产品失败')
      }
    } catch (error) {
      message.error('删除产品失败，请稍后重试')
      console.error('删除产品失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 验证模具编号格式（移除唯一性约束，允许多个产品共享模具）
  const validateMoldFormat = (moldNumber: string) => {
    // 只验证格式，不验证唯一性
    const moldPattern = /^M-[A-Z]{2}-\d{2}$/
    return moldPattern.test(moldNumber)
  }

  // 获取使用指定模具的产品列表
  const getProductsUsingMold = (moldNumber: string, moldType: 'forming' | 'hotPress') => {
    return productModelsData.filter(model =>
      moldType === 'forming'
        ? model.formingMold === moldNumber
        : model.hotPressMold === moldNumber
    )
  }

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields()
      setLoading(true)

      if (editingModel) {
        // 更新产品
        const response = await dataAccessManager.products.update(editingModel.id, values)

        if (response.status === 'success') {
          await refreshProductModels()
          message.success('产品更新成功')
        } else {
          message.error(response.message || '更新产品失败')
          return
        }
      } else {
        // 创建新产品
        const response = await dataAccessManager.products.create(values)

        if (response.status === 'success') {
          await refreshProductModels()
          message.success('产品创建成功')
        } else {
          message.error(response.message || '创建产品失败')
          return
        }
      }

      setIsModalVisible(false)
      form.resetFields()
    } catch (error) {
      message.error('操作失败，请稍后重试')
      console.error('产品操作失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleModalCancel = () => {
    setIsModalVisible(false)
    form.resetFields()
  }

  const handleImport = () => {
    message.info('导入功能开发中...')
  }

  const handleExport = () => {
    message.success('数据导出成功')
  }

  // 计算统计数据 - 只在客户端渲染后计算，避免SSR水合错误
  const totalModels = isClient ? productModelsData.length : 0
  const activeModels = isClient ? productModelsData.filter(m => m.status === 'active').length : 0
  const avgFormingPrice = isClient && totalModels > 0 ? productModelsData.reduce((sum, m) => sum + m.formingPiecePrice, 0) / totalModels : 0
  const avgHotPressPrice = isClient && totalModels > 0 ? productModelsData.reduce((sum, m) => sum + m.hotPressPiecePrice, 0) / totalModels : 0
  const avgProductPrice = isClient && totalModels > 0 ? productModelsData.reduce((sum, m) => sum + m.productPrice, 0) / totalModels : 0
  const avgProductWeight = isClient && totalModels > 0 ? productModelsData.reduce((sum, m) => sum + m.productWeight, 0) / totalModels : 0

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: styleHelpers.spacing.lg }}>
      {/* 页面标题 */}
      <div className="page-header">
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <BarcodeOutlined style={{ fontSize: '24px', color: '#722ed1', marginRight: '12px' }} />
          <div>
            <h1 className="page-title">产品数据管理</h1>
            <p className="page-description">管理产品信息、模具关联和计件单价信息</p>
          </div>
        </div>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="型号总数"
              value={totalModels}
              suffix="个"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="启用型号"
              value={activeModels}
              suffix="个"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="平均产品价格"
              value={avgProductPrice}
              precision={3}
              prefix="¥"
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="平均产品重量"
              value={avgProductWeight}
              precision={2}
              suffix="克"
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 操作区域 */}
      <Card>
        <Row gutter={[16, 16]} align="middle" justify="space-between">
          <Col xs={24} lg={18}>
            <Space wrap size="middle">
              <Input
                placeholder="搜索产品编码或名称"
                prefix={<SearchOutlined />}
                style={{ width: '256px' }}
              />
              <Select
                placeholder="状态"
                style={{ width: '128px' }}
                value={statusFilter}
                onChange={setStatusFilter}
                allowClear
              >
                <Option value="active">启用</Option>
                <Option value="inactive">停用</Option>
              </Select>
            </Space>
          </Col>
          <Col xs={24} lg={6}>
            <Space>
              <Button icon={<ImportOutlined />} onClick={handleImport}>
                导入
              </Button>
              <Button icon={<ExportOutlined />} onClick={handleExport}>
                导出
              </Button>
              <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
                新增产品
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 产品数据列表 */}
      <Card title="产品数据列表">
        <Table
          columns={columns}
          dataSource={productModelsData}
          rowKey="id"
          loading={loading}
          pagination={{
            total: productModelsData.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            pageSizeOptions: ['10', '20', '50', '100'],
          }}
          scroll={{ x: 1400 }}
        />
      </Card>

      {/* 新增/编辑产品模态框 */}
      <Modal
        title={editingModel ? '编辑产品' : '新增产品'}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={900}
        okText="确认"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            status: 'active'
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="产品编码"
                name="modelCode"
                rules={[
                  { required: true, message: '请输入产品编码' },
                  { pattern: /^P\d{5}$/, message: '格式：P + 5位数字（如：P00001）' },
                  {
                    validator: (_, value) => {
                      if (!value) return Promise.resolve()
                      if (!checkCodeUniqueness(value, editingModel?.id)) {
                        return Promise.reject(new Error('产品编码已存在，请使用其他编码'))
                      }
                      return Promise.resolve()
                    }
                  }
                ]}
              >
                <Input placeholder="如：P00001（自动生成）" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="产品名称"
                name="modelName"
                rules={[{ required: true, message: '请输入产品名称' }]}
              >
                <Input placeholder="请输入产品名称" />
              </Form.Item>
            </Col>
          </Row>

          <Tabs
            defaultActiveKey="mold"
            items={[
              {
                key: 'mold',
                label: <span><ToolOutlined />模具信息</span>,
                children: (
                  <>
                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          label="成型模具编号"
                          name="formingMold"
                          rules={[
                            { required: true, message: '请输入成型模具编号' },
                            {
                              validator: (_, value) => {
                                if (!value) return Promise.resolve()
                                if (validateMoldFormat(value)) {
                                  return Promise.resolve()
                                }
                                return Promise.reject(new Error('模具编号格式错误，请使用格式：M-XX-XX（如：M-JX-05）'))
                              }
                            }
                          ]}
                          extra={
                            currentFormingMold &&
                            getProductsUsingMold(currentFormingMold, 'forming').length > 0 && (
                              <div style={{ marginTop: '4px' }}>
                                <span style={{ color: '#1890ff', fontSize: '12px' }}>
                                  💡 此模具已被 {getProductsUsingMold(currentFormingMold, 'forming').length} 个产品使用：
                                </span>
                                <div style={{ fontSize: '11px', color: '#666', marginTop: '2px' }}>
                                  {getProductsUsingMold(currentFormingMold, 'forming')
                                    .filter(p => p.id !== editingModel?.id)
                                    .map(p => p.modelName)
                                    .join('、')}
                                </div>
                              </div>
                            )
                          }
                        >
                          <Input
                            placeholder="如：M-JX-05（支持多产品共享）"
                            onChange={(e) => setCurrentFormingMold(e.target.value)}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          label="成型模具单模数量"
                          name="formingMoldQuantity"
                          rules={[{ required: true, message: '请输入单模数量' }]}
                        >
                          <InputNumber
                            style={{ width: '100%' }}
                            placeholder="请输入数量"
                            suffix="个/模"
                            min={1}
                          />
                        </Form.Item>
                      </Col>
                    </Row>
                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          label="热压模具编号"
                          name="hotPressMold"
                          rules={[
                            { required: true, message: '请输入热压模具编号' },
                            {
                              validator: (_, value) => {
                                if (!value) return Promise.resolve()
                                if (!validateMoldFormat(value)) {
                                  return Promise.reject(new Error('模具编号格式错误，请使用格式：M-XX-XX（如：M-RY-12）'))
                                }
                                // 热压模具保持唯一性约束
                                const existingModels = editingModel
                                  ? productModelsData.filter(model => model.id !== editingModel.id)
                                  : productModelsData
                                const isDuplicate = existingModels.some(model => model.hotPressMold === value)
                                if (isDuplicate) {
                                  return Promise.reject(new Error('热压模具编号已存在，请使用其他编号'))
                                }
                                return Promise.resolve()
                              }
                            }
                          ]}
                        >
                          <Input placeholder="如：M-RY-12" />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          label="热压模具单模数量"
                          name="hotPressMoldQuantity"
                          rules={[{ required: true, message: '请输入单模数量' }]}
                        >
                          <InputNumber
                            style={{ width: '100%' }}
                            placeholder="请输入数量"
                            suffix="个/模"
                            min={1}
                          />
                        </Form.Item>
                      </Col>
                    </Row>
                  </>
                )
              },

              {
                key: 'price',
                label: <span><DollarOutlined />计件单价</span>,
                children: (
                  <>
                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          label="成型计件单价"
                          name="formingPiecePrice"
                          rules={[{ required: true, message: '请输入成型计件单价' }]}
                        >
                          <InputNumber
                            style={{ width: '100%' }}
                            placeholder="请输入单价"
                            prefix="¥"
                            suffix="/模"
                            min={0}
                            precision={2}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          label="热压计件单价"
                          name="hotPressPiecePrice"
                          rules={[{ required: true, message: '请输入热压计件单价' }]}
                        >
                          <InputNumber
                            style={{ width: '100%' }}
                            placeholder="请输入单价"
                            prefix="¥"
                            suffix="/模"
                            min={0}
                            precision={2}
                          />
                        </Form.Item>
                      </Col>
                    </Row>

                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          label="产品价格"
                          name="productPrice"
                          rules={[
                            { required: true, message: '请输入产品价格' },
                            { type: 'number', min: 0.01, message: '产品价格必须大于0' }
                          ]}
                        >
                          <InputNumber
                            style={{ width: '100%' }}
                            placeholder="请输入产品价格"
                            prefix="¥"
                            min={0}
                            precision={3}
                            step={0.001}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          label="产品重量"
                          name="productWeight"
                          rules={[
                            { required: true, message: '请输入产品重量' },
                            { type: 'number', min: 0.01, message: '产品重量必须大于0' }
                          ]}
                        >
                          <InputNumber
                            style={{ width: '100%' }}
                            placeholder="请输入产品重量"
                            suffix="克"
                            min={0}
                            precision={2}
                            step={0.01}
                          />
                        </Form.Item>
                      </Col>
                    </Row>

                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          label="箱规"
                          name="boxSpecification"
                          rules={[{ required: true, message: '请输入箱规' }]}
                        >
                          <Input
                            placeholder="如：30×20×15 cm"
                          />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          label="装箱数"
                          name="packingQuantity"
                          rules={[
                            { required: true, message: '请输入装箱数' },
                            { type: 'number', min: 1, message: '装箱数必须大于0' }
                          ]}
                        >
                          <InputNumber
                            style={{ width: '100%' }}
                            placeholder="请输入装箱数"
                            suffix="个/箱"
                            min={1}
                            precision={0}
                            step={1}
                          />
                        </Form.Item>
                      </Col>
                    </Row>

                    <Form.Item
                      label="状态"
                      name="status"
                      rules={[{ required: true, message: '请选择状态' }]}
                    >
                      <Select placeholder="请选择状态">
                        <Option value="active">启用</Option>
                        <Option value="inactive">停用</Option>
                      </Select>
                    </Form.Item>
                  </>
                )
              }
            ]}
          />
        </Form>
      </Modal>

      {/* 查看详情模态框 */}
      <Modal
        title="产品型号详情"
        open={isDetailModalVisible}
        onCancel={() => setIsDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setIsDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={900}
      >
        {viewingModel && viewingModel.modelCode && viewingModel.modelName && (
          <div style={{ display: 'flex', flexDirection: 'column', gap: styleHelpers.spacing.lg }}>
            <Descriptions bordered column={2}>
              <Descriptions.Item label="产品编码">{viewingModel.modelCode}</Descriptions.Item>
              <Descriptions.Item label="产品名称">{viewingModel.modelName}</Descriptions.Item>
              <Descriptions.Item label="成型模具编号">{viewingModel.formingMold}</Descriptions.Item>
              <Descriptions.Item label="成型模具单模数量">{viewingModel.formingMoldQuantity}个/模</Descriptions.Item>
              <Descriptions.Item label="热压模具编号">{viewingModel.hotPressMold}</Descriptions.Item>
              <Descriptions.Item label="热压模具单模数量">{viewingModel.hotPressMoldQuantity}个/模</Descriptions.Item>
              <Descriptions.Item label="成型计件单价">¥{viewingModel.formingPiecePrice.toFixed(2)}/模</Descriptions.Item>
              <Descriptions.Item label="热压计件单价">¥{viewingModel.hotPressPiecePrice.toFixed(2)}/模</Descriptions.Item>
              <Descriptions.Item label="产品价格">¥{viewingModel.productPrice.toFixed(3)}</Descriptions.Item>
              <Descriptions.Item label="产品重量">{viewingModel.productWeight.toFixed(2)}克</Descriptions.Item>
              <Descriptions.Item label="箱规">{viewingModel.boxSpecification}</Descriptions.Item>
              <Descriptions.Item label="装箱数">{viewingModel.packingQuantity}个/箱</Descriptions.Item>
              <Descriptions.Item label="状态">
                <Tag color={statusMap[viewingModel.status].color}>
                  {statusMap[viewingModel.status].text}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="创建时间" span={2}>{viewingModel.createdAt}</Descriptions.Item>
              <Descriptions.Item label="更新时间" span={2}>{viewingModel.updatedAt}</Descriptions.Item>
            </Descriptions>

            {/* 共享模具信息 */}
            {(() => {
              const sharedFormingProducts = getProductsUsingMold(viewingModel.formingMold, 'forming')
                .filter(p => p.id !== viewingModel.id)
              const sharedHotPressProducts = getProductsUsingMold(viewingModel.hotPressMold, 'hotPress')
                .filter(p => p.id !== viewingModel.id)

              return (sharedFormingProducts.length > 0 || sharedHotPressProducts.length > 0) && (
                <div>
                  <Divider orientation="left">模具共享信息</Divider>
                  {sharedFormingProducts.length > 0 && (
                    <div style={{ marginBottom: styleHelpers.spacing.md }}>
                      <h4 style={{ fontSize: '14px', fontWeight: 500, color: '#374151', marginBottom: styleHelpers.spacing.sm }}>
                        🔧 共享成型模具 {viewingModel.formingMold} 的其他产品：
                      </h4>
                      <div style={{ display: 'flex', flexWrap: 'wrap', gap: styleHelpers.spacing.sm }}>
                        {sharedFormingProducts.map(product => (
                          <Tag key={product.id} color="blue" style={{ marginBottom: '4px' }}>
                            {product.modelCode} - {product.modelName}
                          </Tag>
                        ))}
                      </div>
                    </div>
                  )}
                  {sharedHotPressProducts.length > 0 && (
                    <div>
                      <h4 style={{ fontSize: '14px', fontWeight: 500, color: '#374151', marginBottom: styleHelpers.spacing.sm }}>
                        🔥 共享热压模具 {viewingModel.hotPressMold} 的其他产品：
                      </h4>
                      <div style={{ display: 'flex', flexWrap: 'wrap', gap: styleHelpers.spacing.sm }}>
                        {sharedHotPressProducts.map(product => (
                          <Tag key={product.id} color="orange" style={{ marginBottom: '4px' }}>
                            {product.modelCode} - {product.modelName}
                          </Tag>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )
            })()}
          </div>
        )}
      </Modal>
    </div>
  )
}

// 用App组件包裹以提供message等上下文
export default function ProductModelsManagementPage() {
  return (
    <App>
      <ProductModelsManagement />
    </App>
  )
}
