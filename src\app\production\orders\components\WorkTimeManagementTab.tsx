'use client'

import React, { useState, useEffect, useCallback } from 'react'
import {
  Card,
  Form,
  TimePicker,
  Switch,
  Button,
  Space,
  Row,
  Col,
  Divider,
  Typography,
  Alert,
  App,
  Tooltip,
  Popconfirm,
  Statistic,
  Modal
} from 'antd'
import {
  ClockCircleOutlined,
  PlusOutlined,
  DeleteOutlined,
  SaveOutlined,
  ReloadOutlined,
  InfoCircleOutlined
} from '@ant-design/icons'
import dayjs from 'dayjs'
import { dataAccessManager } from '@/services/dataAccess/DataAccessManager'
import { handleApiResponse } from '@/utils/dataAccessErrorHandler'
import { dataChangeNotifier } from '@/services/dataAccess/DataChangeNotifier'
import { WorkTimeStatusTag } from '@/components/common/UnifiedTagRenderer'
import {
  WorkTimeConfiguration,
  WorkTimeSlot
} from '@/types'

const { Title, Text } = Typography

// ✅ 定义严格的表单值类型接口，替换any类型
interface WorkTimeFormValues {
  [key: `work_start_${string}`]: dayjs.Dayjs
  [key: `work_end_${string}`]: dayjs.Dayjs
  [key: `work_active_${string}`]: boolean
}

interface WorkTimeManagementTabProps {
  loading?: boolean
}

const WorkTimeManagementTab: React.FC<WorkTimeManagementTabProps> = ({ loading = false }) => {
  const { message, modal } = App.useApp()
  const [form] = Form.useForm()

  // 使用dataAccessManager进行数据管理 - 遵循数据调用规范
  const [workTimeConfigurations, setWorkTimeConfigurations] = useState<WorkTimeConfiguration[]>([])
  const [currentConfiguration, setCurrentConfiguration] = useState<WorkTimeConfiguration | null>(null)
  const [dataLoading, setDataLoading] = useState(false)

  // ✅ 移除重复的时间计算逻辑，使用服务层统一方法
  // 时间计算现在通过 dataAccessManager.workTime.calculateWorkingMinutes 进行

  // 数据加载函数 - 使用dataAccessManager
  const loadWorkTimeConfigurations = useCallback(async () => {
    setDataLoading(true)

    try {
      const response = await dataAccessManager.workTime.getConfigurations()
      if (response.status === 'success' && response.data) {
        setWorkTimeConfigurations(response.data)

        // 设置当前配置
        if (!currentConfiguration && response.data.length > 0) {
          const defaultConfig = response.data.find(config => config.isDefault) || response.data[0]
          setCurrentConfiguration(defaultConfig)
        }
      } else {
        message.error(response.message || '获取工作时间配置失败')
      }
    } catch (error) {
      message.error('获取工作时间配置失败')
    } finally {
      setDataLoading(false)
    }
  }, [currentConfiguration, message])

  // 初始化数据 - 遵循数据调用规范
  useEffect(() => {
    loadWorkTimeConfigurations()
  }, [loadWorkTimeConfigurations])



  // 当配置更新时同步表单字段
  useEffect(() => {
    if (currentConfiguration) {
      const formValues = {
        // 设置工作时间段字段
        ...(currentConfiguration.workTimeSlots || []).reduce((acc, slot) => ({
          ...acc,
          [`work_start_${slot.id}`]: dayjs(slot.startTime, 'HH:mm'),
          [`work_end_${slot.id}`]: dayjs(slot.endTime, 'HH:mm'),
          [`work_active_${slot.id}`]: slot.isActive
        }), {})
      }

      form.setFieldsValue(formValues)
    }
  }, [currentConfiguration, form])

  // ✅ 处理工作时间段保存 - 使用严格类型
  const handleWorkTimeSave = async (values: WorkTimeFormValues) => {
    if (!currentConfiguration) return

    try {
      // 更新工作时间段
      const updatedWorkSlots = (currentConfiguration.workTimeSlots || []).map(slot => ({
        ...slot,
        startTime: values[`work_start_${slot.id}`]?.format('HH:mm') || slot.startTime,
        endTime: values[`work_end_${slot.id}`]?.format('HH:mm') || slot.endTime,
        isActive: values[`work_active_${slot.id}`] ?? slot.isActive
      }))

      // ✅ 使用服务层统一的时间计算方法
      const timeCalculation = await dataAccessManager.workTime.calculateWorkingMinutes(updatedWorkSlots)

      // 创建更新后的配置对象
      const updatedConfiguration = {
        ...currentConfiguration,
        workTimeSlots: updatedWorkSlots,
        breakTimeSlots: [],
        ...timeCalculation,
        updatedAt: new Date().toISOString()
      }

      // 遵循数据调用规范：通过 dataAccessManager 更新配置
      const updateResponse = await dataAccessManager.workTime.update(
        currentConfiguration.id,
        {
          workTimeSlots: updatedWorkSlots,
          breakTimeSlots: [],
          ...timeCalculation
        }
      )

      if (updateResponse.status === 'success') {
        // 更新当前配置状态
        setCurrentConfiguration(updatedConfiguration)

        // ✅ 使用架构合规的数据变更通知，替代DOM操作
        dataChangeNotifier.notifyDataChange({
          type: 'work_time_configuration',
          action: 'update',
          data: updatedConfiguration,
          affectedIds: [currentConfiguration.id]
        })

        message.success('工作时间配置已保存')
      } else {
        message.error(updateResponse.message || '保存工作时间配置失败')
      }
    } catch (error) {
      message.error('保存工作时间配置失败')
    }
  }

  // 添加工作时间段 - 智能命名逻辑
  const handleAddWorkTimeSlot = async () => {
    if (!currentConfiguration) return

    const existingSlots = currentConfiguration.workTimeSlots || []
    let slotName = '工作时间段'
    let startTime = '09:00'
    let endTime = '17:00'
    let description = ''

    // 智能命名和时间设置
    if (existingSlots.length === 0) {
      slotName = '上午工作时间段'
      startTime = '06:30'
      endTime = '11:00'
      description = '上午工作时间'
    } else if (existingSlots.length === 1) {
      slotName = '下午工作时间段'
      startTime = '11:30'
      endTime = '17:00'
      description = '下午工作时间'
    } else {
      slotName = `工作时间段${existingSlots.length + 1}`
      description = `第${existingSlots.length + 1}个工作时间段`
    }

    const newSlot: WorkTimeSlot = {
      id: `work_slot_${Date.now()}`,
      name: slotName,
      startTime,
      endTime,
      isActive: true,
      description
    }

    const updatedSlots = [...existingSlots, newSlot]
    // ✅ 使用服务层统一的时间计算方法
    const timeCalculation = await dataAccessManager.workTime.calculateWorkingMinutes(updatedSlots)

    const response = await handleApiResponse(
      () => dataAccessManager.workTime.update(currentConfiguration.id, {
        workTimeSlots: updatedSlots,
        ...timeCalculation
      }),
      '添加工作时间段'
    )

    if (response) {
      setCurrentConfiguration({
        ...currentConfiguration,
        workTimeSlots: updatedSlots,
        ...timeCalculation
      })
      message.success(`已添加${slotName}`)
    }
  }



  // 删除工作时间段
  const handleDeleteWorkTimeSlot = async (slotId: string) => {
    if (!currentConfiguration) return

    const updatedSlots = currentConfiguration.workTimeSlots.filter(slot => slot.id !== slotId)
    // ✅ 使用服务层统一的时间计算方法
    const timeCalculation = await dataAccessManager.workTime.calculateWorkingMinutes(updatedSlots)

    const response = await handleApiResponse(
      () => dataAccessManager.workTime.update(currentConfiguration.id, {
        workTimeSlots: updatedSlots,
        ...timeCalculation
      }),
      '删除工作时间段'
    )

    if (response) {
      setCurrentConfiguration({
        ...currentConfiguration,
        workTimeSlots: updatedSlots,
        ...timeCalculation
      })
      message.success('工作时间段已删除')
    }
  }



  // 重置表单
  const handleReset = () => {
    if (!currentConfiguration) {
      message.warning('没有可重置的配置')
      return
    }

    // 重置表单字段到当前配置的原始值
    const resetValues = {
      // 重置工作时间段
      ...(currentConfiguration.workTimeSlots || []).reduce((acc, slot) => ({
        ...acc,
        [`work_start_${slot.id}`]: dayjs(slot.startTime, 'HH:mm'),
        [`work_end_${slot.id}`]: dayjs(slot.endTime, 'HH:mm'),
        [`work_active_${slot.id}`]: slot.isActive
      }), {})
    }

    // 设置表单字段值
    form.setFieldsValue(resetValues)

    message.success('表单已重置到当前配置')
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      {/* 页面标题和操作 */}
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <Title level={4} style={{ marginBottom: '8px' }}>
            <ClockCircleOutlined style={{ marginRight: '8px' }} />
            工作时间配置
          </Title>
          <Text type="secondary">
            配置生产工位的工作时间安排
          </Text>
        </div>

      </div>

      {/* 当前配置信息 */}
      {currentConfiguration && (
        <Alert
          description={
            <div style={{ fontSize: '14px', color: '#666' }}>
              {currentConfiguration.workTimeSlots && currentConfiguration.workTimeSlots.length > 0 && (
                <span>
                  工作时间: {currentConfiguration.workTimeSlots
                    .filter(slot => slot.isActive)
                    .map(slot => `${slot.startTime}-${slot.endTime}`)
                    .join(', ')}
                </span>
              )}
              {currentConfiguration.effectiveWorkingMinutes && (
                <span style={{ marginLeft: '16px' }}>
                  总工作时间: {Math.floor(currentConfiguration.effectiveWorkingMinutes / 60)}小时{currentConfiguration.effectiveWorkingMinutes % 60}分钟
                </span>
              )}
            </div>
          }
          type="info"
          showIcon
          icon={<InfoCircleOutlined />}
        />
      )}

      {/* 主要内容区域 */}
      <Card>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
          {/* 时间配置表单 */}
          {currentConfiguration && (
            <Form
              form={form}
              layout="vertical"
              onFinish={handleWorkTimeSave}
              initialValues={{
                ...(currentConfiguration?.workTimeSlots || []).reduce((acc, slot) => ({
                  ...acc,
                  [`work_start_${slot.id}`]: dayjs(slot.startTime, 'HH:mm'),
                  [`work_end_${slot.id}`]: dayjs(slot.endTime, 'HH:mm'),
                  [`work_active_${slot.id}`]: slot.isActive
                }), {}),

              }}
            >
              <Card 
                size="small" 
                title="工作时间配置"
              >
                <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                  {/* 工作时间段 */}
                  <div>
                    <div style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      marginBottom: '12px'
                    }}>
                      <div>
                        <Title level={5}>工作时间段（上午/下午）</Title>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          建议设置上午和下午两个工作时间段
                        </Text>
                      </div>
                      <Button
                        type="dashed"
                        size="small"
                        icon={<PlusOutlined />}
                        onClick={handleAddWorkTimeSlot}
                      >
                        添加时间段
                      </Button>
                    </div>
                    
                    {(currentConfiguration.workTimeSlots || []).map((slot) => (
                      <Card key={slot.id} size="small" style={{ marginBottom: '8px' }}>
                        <Row gutter={16} align="middle">
                          <Col span={4}>
                            <Form.Item
                              label="开始时间"
                              name={`work_start_${slot.id}`}
                              rules={[
                                { required: true, message: '请选择开始时间' },
                                {
                                  validator: (_, value) => {
                                    if (!value) return Promise.resolve()
                                    const endTimeValue = form.getFieldValue(`work_end_${slot.id}`)
                                    if (endTimeValue && value.isAfter(endTimeValue)) {
                                      return Promise.reject(new Error('开始时间不能晚于结束时间'))
                                    }
                                    // 时间范围验证：6:00-23:59
                                    const hour = value.hour()
                                    if (hour < 6 || hour > 23) {
                                      return Promise.reject(new Error('工作时间应在6:00-23:59之间'))
                                    }
                                    return Promise.resolve()
                                  }
                                }
                              ]}
                            >
                              <TimePicker
                                format="HH:mm"
                                placeholder="开始时间"
                                size="small"
                                onChange={() => {
                                  // 触发表单验证更新
                                  form.validateFields([`work_end_${slot.id}`])
                                }}
                              />
                            </Form.Item>
                          </Col>
                          <Col span={4}>
                            <Form.Item
                              label="结束时间"
                              name={`work_end_${slot.id}`}
                              rules={[
                                { required: true, message: '请选择结束时间' },
                                {
                                  validator: (_, value) => {
                                    if (!value) return Promise.resolve()
                                    const startTimeValue = form.getFieldValue(`work_start_${slot.id}`)
                                    if (startTimeValue && value.isBefore(startTimeValue)) {
                                      return Promise.reject(new Error('结束时间不能早于开始时间'))
                                    }
                                    // 时间范围验证：6:00-23:59
                                    const hour = value.hour()
                                    if (hour < 6 || hour > 23) {
                                      return Promise.reject(new Error('工作时间应在6:00-23:59之间'))
                                    }
                                    // 最小工作时长验证（30分钟）
                                    if (startTimeValue && value.diff(startTimeValue, 'minutes') < 30) {
                                      return Promise.reject(new Error('工作时长不能少于30分钟'))
                                    }
                                    return Promise.resolve()
                                  }
                                }
                              ]}
                            >
                              <TimePicker
                                format="HH:mm"
                                placeholder="结束时间"
                                size="small"
                                onChange={() => {
                                  // 触发表单验证更新
                                  form.validateFields([`work_start_${slot.id}`])
                                }}
                              />
                            </Form.Item>
                          </Col>
                          <Col span={3}>
                            <Form.Item
                              label="启用"
                              name={`work_active_${slot.id}`}
                              valuePropName="checked"
                            >
                              <Switch size="small" />
                            </Form.Item>
                          </Col>
                          <Col span={8}>
                            <Space>
                              <Text type="secondary">{slot.name}</Text>
                              <WorkTimeStatusTag 
                                status={slot.isActive ? 'configured' : 'inactive'}
                                showIcon={true}
                              />
                            </Space>
                          </Col>
                          <Col span={5}>
                            <Space>
                              <Tooltip title="删除时间段">
                                <Popconfirm
                                  title="确定删除此工作时间段吗？"
                                  onConfirm={() => handleDeleteWorkTimeSlot(slot.id)}
                                  okText="确定"
                                  cancelText="取消"
                                >
                                  <Button
                                    type="text"
                                    danger
                                    size="small"
                                    icon={<DeleteOutlined />}
                                  />
                                </Popconfirm>
                              </Tooltip>
                            </Space>
                          </Col>
                        </Row>
                      </Card>
                    ))}
                  </div>

                  {/* 休息时间段区域已隐藏 - 新格式使用上午/下午分段工作时间 */}

                  <Divider />

                  {/* 时间统计 */}
                  <Row gutter={16}>
                    <Col span={12}>
                      <Card size="small">
                        <Statistic
                          title="总工作时间"
                          value={Math.round((currentConfiguration?.totalWorkingMinutes || 0) / 60 * 10) / 10}
                          suffix="小时"
                          precision={1}
                          valueStyle={{ color: '#1890ff' }}
                        />
                      </Card>
                    </Col>
                    <Col span={12}>
                      <Card size="small">
                        <Statistic
                          title="工作时间段数量"
                          value={currentConfiguration?.workTimeSlots?.filter(slot => slot.isActive).length || 0}
                          suffix="个"
                          valueStyle={{ color: '#52c41a' }}
                        />
                      </Card>
                    </Col>
                  </Row>

                  {/* 保存按钮 */}
                  <div style={{ textAlign: 'center' }}>
                    <Space>
                      <Button
                        type="primary"
                        htmlType="submit"
                        icon={<SaveOutlined />}
                        loading={loading}
                      >
                        保存配置
                      </Button>
                      <Button
                        onClick={handleReset}
                        icon={<ReloadOutlined />}
                      >
                        重置
                      </Button>
                    </Space>
                  </div>
                </div>
              </Card>
            </Form>
          )}
        </div>
      </Card>
    </div>
  )
}

export default WorkTimeManagementTab
