/**
 * 动画效果CSS Modules样式
 * 提供常用的动画效果和过渡动画
 */

/* 基础过渡动画 */
.transition {
  transition: all 0.2s ease-in-out;
}

.transitionFast {
  transition: all 0.15s ease-in-out;
}

.transitionSlow {
  transition: all 0.3s ease-in-out;
}

.transitionColors {
  transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
}

.transitionOpacity {
  transition: opacity 0.2s ease-in-out;
}

.transitionTransform {
  transition: transform 0.2s ease-in-out;
}

.transitionShadow {
  transition: box-shadow 0.2s ease-in-out;
}

/* 悬停效果 */
.hoverScale:hover {
  transform: scale(1.05);
}

.hoverScaleSm:hover {
  transform: scale(1.02);
}

.hoverScaleLg:hover {
  transform: scale(1.1);
}

.hoverLift:hover {
  transform: translateY(-2px);
}

.hoverLiftSm:hover {
  transform: translateY(-1px);
}

.hoverLiftLg:hover {
  transform: translateY(-4px);
}

.hoverShadow:hover {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.hoverShadowSm:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.hoverShadowLg:hover {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.hoverOpacity:hover {
  opacity: 0.8;
}

.hoverBrightness:hover {
  filter: brightness(1.1);
}

/* 焦点效果 */
.focusRing:focus {
  outline: 2px solid #0ea5e9;
  outline-offset: 2px;
}

.focusRingInset:focus {
  outline: 2px solid #0ea5e9;
  outline-offset: -2px;
}

.focusVisible:focus-visible {
  outline: 2px solid #0ea5e9;
  outline-offset: 2px;
}

/* 淡入淡出动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 滑动动画 */
@keyframes slideInUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

/* 缩放动画 */
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes scaleOut {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.8);
  }
}

/* 旋转动画 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    transform: translate3d(0, -30px, 0);
  }
  70% {
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

/* 动画类 */
.animateFadeIn {
  animation: fadeIn 0.3s ease-in-out;
}

.animateFadeOut {
  animation: fadeOut 0.3s ease-in-out;
}

.animateFadeInUp {
  animation: fadeInUp 0.3s ease-in-out;
}

.animateFadeInDown {
  animation: fadeInDown 0.3s ease-in-out;
}

.animateFadeInLeft {
  animation: fadeInLeft 0.3s ease-in-out;
}

.animateFadeInRight {
  animation: fadeInRight 0.3s ease-in-out;
}

.animateSlideInUp {
  animation: slideInUp 0.3s ease-in-out;
}

.animateSlideInDown {
  animation: slideInDown 0.3s ease-in-out;
}

.animateSlideInLeft {
  animation: slideInLeft 0.3s ease-in-out;
}

.animateSlideInRight {
  animation: slideInRight 0.3s ease-in-out;
}

.animateScaleIn {
  animation: scaleIn 0.2s ease-in-out;
}

.animateScaleOut {
  animation: scaleOut 0.2s ease-in-out;
}

.animateSpin {
  animation: spin 1s linear infinite;
}

.animatePulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animateBounce {
  animation: bounce 1s infinite;
}

/* 动画延迟 */
.animateDelay75 {
  animation-delay: 75ms;
}

.animateDelay100 {
  animation-delay: 100ms;
}

.animateDelay150 {
  animation-delay: 150ms;
}

.animateDelay200 {
  animation-delay: 200ms;
}

.animateDelay300 {
  animation-delay: 300ms;
}

.animateDelay500 {
  animation-delay: 500ms;
}

.animateDelay700 {
  animation-delay: 700ms;
}

.animateDelay1000 {
  animation-delay: 1000ms;
}

/* 动画持续时间 */
.animateDuration75 {
  animation-duration: 75ms;
}

.animateDuration100 {
  animation-duration: 100ms;
}

.animateDuration150 {
  animation-duration: 150ms;
}

.animateDuration200 {
  animation-duration: 200ms;
}

.animateDuration300 {
  animation-duration: 300ms;
}

.animateDuration500 {
  animation-duration: 500ms;
}

.animateDuration700 {
  animation-duration: 700ms;
}

.animateDuration1000 {
  animation-duration: 1000ms;
}

/* 加载动画 */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* 骨架屏动画 */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
