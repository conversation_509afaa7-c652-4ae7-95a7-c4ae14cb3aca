/**
 * 布局组件专用CSS Modules样式
 * 为MainLayout、Sidebar、Header组件提供特殊样式支持
 */

/* 主布局容器 */
.mainLayout {
  min-height: 100vh;
  background: #f9fafb;
}

.contentLayout {
  transition: margin-left 0.3s ease;
}

.contentArea {
  background: #f9fafb;
  overflow: auto;
  min-height: calc(100vh - 64px);
}

/* 侧边栏样式 */
.sidebar {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 50;
  background: #ffffff;
  border-right: 1px solid #f0f0f0;
  box-shadow: 2px 0 8px 0 rgba(29, 35, 41, 0.05);
}

.sidebarHidden {
  display: none;
}

.sidebarCollapsed {
  width: 80px;
}

.sidebarExpanded {
  width: 256px;
}

/* Logo区域 */
.logoArea {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #f0f0f0;
  padding: 0 16px;
}

.logoText {
  font-size: 20px;
  font-weight: bold;
  color: #0284c7;
  transition: font-size 0.3s ease;
}

.logoTextCollapsed {
  font-size: 24px;
}

/* 折叠按钮 */
.toggleButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  background-color: transparent;
  border: none;
  color: #6b7280;
}

.toggleButton:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.toggleButtonArea {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

/* 菜单样式 */
.menuContainer {
  border: none;
  background: transparent;
}

/* 移动端抽屉 */
.mobileDrawer {
  z-index: 1000;
}

/* 头部样式 */
.header {
  background: #ffffff;
  border-bottom: 1px solid #f0f0f0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
  position: sticky;
  top: 0;
  z-index: 10;
}

.headerLeft {
  display: flex;
  align-items: center;
  gap: 12px;
}

.headerRight {
  display: flex;
  align-items: center;
}

.headerTitle {
  font-weight: 600;
  color: #111827;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.headerTitleMobile {
  font-size: 16px;
}

.headerTitleDesktop {
  font-size: 18px;
}

/* 移动端菜单按钮 */
.mobileMenuButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  background: transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.mobileMenuButton:hover {
  background-color: #f3f4f6;
}

/* 用户操作区 */
.userActions {
  display: flex;
  align-items: center;
}

.userActionsMobile {
  gap: 8px;
}

.userActionsDesktop {
  gap: 16px;
}

.notificationButton {
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  background: transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.notificationButtonMobile {
  width: 32px;
  height: 32px;
}

.notificationButtonDesktop {
  width: 40px;
  height: 40px;
}

.notificationButton:hover {
  background-color: #f3f4f6;
}

/* 用户信息下拉 */
.userDropdown {
  display: flex;
  align-items: center;
  cursor: pointer;
  border-radius: 12px;
  transition: background-color 0.2s ease;
  background-color: transparent;
}

.userDropdown:hover {
  background-color: #f9fafb;
}

.userDropdownMobile {
  gap: 4px;
  padding: 4px;
}

.userDropdownDesktop {
  gap: 8px;
  padding: 8px 12px;
}

.userName {
  color: #374151;
  font-weight: 500;
  font-size: 14px;
}

.userDropdownIcon {
  color: #9ca3af;
  font-size: 12px;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .sidebar {
    display: none;
  }
  
  .contentLayout {
    margin-left: 0 !important;
  }
  
  .headerLeft {
    gap: 8px;
  }
  
  .userActions {
    gap: 8px;
  }
}

@media (min-width: 769px) {
  .mobileMenuButton {
    display: none;
  }
}

/* 动画效果 */
.slideIn {
  animation: slideIn 0.3s ease-out;
}

.slideOut {
  animation: slideOut 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-100%);
  }
}

/* 阴影效果 */
.shadowSm {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.shadowMd {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.shadowLg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* 过渡效果 */
.transition {
  transition: all 0.2s ease-in-out;
}

.transitionSlow {
  transition: all 0.3s ease-in-out;
}
