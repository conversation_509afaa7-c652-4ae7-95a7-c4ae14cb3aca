'use client'

import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Input,
  Select,
  Modal,
  Form,
  InputNumber,
  Row,
  Col,
  Statistic,
  Progress,
  DatePicker,
  Tooltip,
  Badge,
  Alert,
  App
} from 'antd'
import {
  PlusOutlined,
  SearchOutlined,
  EyeOutlined,
  ExportOutlined,
  InboxOutlined,
  WarningOutlined,
  SwapOutlined,
  SyncOutlined,
  Bar<PERSON><PERSON>Outlined,
  AlertOutlined,
  ShoppingCartOutlined,
  ToolOutlined,
  BellOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import dayjs from 'dayjs'

const { Option } = Select
const { RangePicker } = DatePicker

// 原料库存接口定义
interface MaterialInventory {
  id: string
  materialCode: string
  materialName: string
  specification: string
  category: string
  unit: string
  currentStock: number
  minStock: number
  maxStock: number
  unitPrice: number
  totalValue: number
  location: string
  batchNumber: string
  receivedDate: string
  expiryDate?: string
  supplierCode: string
  supplierName: string
  supplierContact: string
  status: 'normal' | 'low' | 'high' | 'expired' | 'damaged' | 'reserved'
  lastUpdated: string
  qualityStatus: 'qualified' | 'unqualified' | 'pending'
  storageCondition: string
}

// 补货提醒接口
interface ReplenishmentAlert {
  id: string
  materialCode: string
  materialName: string
  currentStock: number
  minStock: number
  shortage: number
  urgencyLevel: 'high' | 'medium' | 'low'
  suggestedQuantity: number
  estimatedCost: number
  supplierName: string
  leadTime: number
  lastOrderDate?: string
}

const MaterialInventoryPageComponent: React.FC = () => {
  const { message, modal } = App.useApp()
  const [loading, setLoading] = useState(false)
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [isTransferModalVisible, setIsTransferModalVisible] = useState(false)
  const [isReplenishModalVisible, setIsReplenishModalVisible] = useState(false)
  const [editingRecord, setEditingRecord] = useState<MaterialInventory | null>(null)
  const [transferRecord, setTransferRecord] = useState<MaterialInventory | null>(null)
  const [searchText, setSearchText] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [categoryFilter, setCategoryFilter] = useState<string>('')
  const [supplierFilter, setSupplierFilter] = useState<string>('')
  
  const [form] = Form.useForm()
  const [transferForm] = Form.useForm()

  // 模拟原料库存数据
  const [materialInventory, setMaterialInventory] = useState<MaterialInventory[]>([
    {
      id: '1',
      materialCode: 'RM001',
      materialName: '45号钢',
      specification: 'Φ50×3000mm',
      category: '金属材料',
      unit: '根',
      currentStock: 150,
      minStock: 200,
      maxStock: 500,
      unitPrice: 280.50,
      totalValue: 42075.00,
      location: 'M区-01-001',
      batchNumber: 'RM20240110001',
      receivedDate: '2024-01-10',
      supplierCode: 'SUP001',
      supplierName: '钢材供应商A',
      supplierContact: '张经理 13800138001',
      status: 'low',
      lastUpdated: '2024-01-15 10:30:00',
      qualityStatus: 'qualified',
      storageCondition: '常温干燥'
    },
    {
      id: '2',
      materialCode: 'RM002',
      materialName: '铝合金板',
      specification: '6061-T6 10×1000×2000mm',
      category: '金属材料',
      unit: '张',
      currentStock: 85,
      minStock: 50,
      maxStock: 200,
      unitPrice: 450.00,
      totalValue: 38250.00,
      location: 'M区-02-015',
      batchNumber: 'RM20240112002',
      receivedDate: '2024-01-12',
      supplierCode: 'SUP002',
      supplierName: '有色金属公司',
      supplierContact: '李经理 13900139002',
      status: 'normal',
      lastUpdated: '2024-01-14 14:20:00',
      qualityStatus: 'qualified',
      storageCondition: '常温干燥'
    },
    {
      id: '3',
      materialCode: 'RM003',
      materialName: '液压油',
      specification: '46# 抗磨液压油',
      category: '化工原料',
      unit: '升',
      currentStock: 2800,
      minStock: 1000,
      maxStock: 5000,
      unitPrice: 12.50,
      totalValue: 35000.00,
      location: 'M区-03-008',
      batchNumber: 'RM20240108003',
      receivedDate: '2024-01-08',
      expiryDate: '2025-01-08',
      supplierCode: 'SUP003',
      supplierName: '化工材料厂',
      supplierContact: '王经理 13700137003',
      status: 'normal',
      lastUpdated: '2024-01-13 09:15:00',
      qualityStatus: 'qualified',
      storageCondition: '阴凉通风'
    },
    {
      id: '4',
      materialCode: 'RM004',
      materialName: '橡胶密封圈',
      specification: 'NBR Φ50×3mm',
      category: '橡胶制品',
      unit: '个',
      currentStock: 25,
      minStock: 100,
      maxStock: 500,
      unitPrice: 8.50,
      totalValue: 212.50,
      location: 'M区-04-012',
      batchNumber: 'RM20240105004',
      receivedDate: '2024-01-05',
      expiryDate: '2026-01-05',
      supplierCode: 'SUP004',
      supplierName: '橡胶制品厂',
      supplierContact: '赵经理 13600136004',
      status: 'low',
      lastUpdated: '2024-01-12 16:45:00',
      qualityStatus: 'qualified',
      storageCondition: '常温避光'
    }
  ])

  // 补货提醒数据
  const replenishmentAlerts: ReplenishmentAlert[] = [
    {
      id: '1',
      materialCode: 'RM001',
      materialName: '45号钢',
      currentStock: 150,
      minStock: 200,
      shortage: 50,
      urgencyLevel: 'high',
      suggestedQuantity: 100,
      estimatedCost: 28050.00,
      supplierName: '钢材供应商A',
      leadTime: 7,
      lastOrderDate: '2023-12-15'
    },
    {
      id: '2',
      materialCode: 'RM004',
      materialName: '橡胶密封圈',
      currentStock: 25,
      minStock: 100,
      shortage: 75,
      urgencyLevel: 'high',
      suggestedQuantity: 200,
      estimatedCost: 1700.00,
      supplierName: '橡胶制品厂',
      leadTime: 5,
      lastOrderDate: '2023-12-20'
    }
  ]

  // 获取库存状态信息
  const getStockStatus = (record: MaterialInventory) => {
    const { currentStock, minStock, maxStock, status } = record
    
    switch (status) {
      case 'low':
        return { 
          color: 'red', 
          text: '库存不足', 
          icon: <WarningOutlined />,
          percentage: (currentStock / maxStock) * 100
        }
      case 'high':
        return { 
          color: 'orange', 
          text: '库存过多', 
          icon: <AlertOutlined />,
          percentage: (currentStock / maxStock) * 100
        }
      case 'expired':
        return { 
          color: 'purple', 
          text: '已过期', 
          icon: <AlertOutlined />,
          percentage: (currentStock / maxStock) * 100
        }
      case 'damaged':
        return { 
          color: 'volcano', 
          text: '损坏', 
          icon: <AlertOutlined />,
          percentage: (currentStock / maxStock) * 100
        }
      case 'reserved':
        return { 
          color: 'blue', 
          text: '已预留', 
          icon: <AlertOutlined />,
          percentage: (currentStock / maxStock) * 100
        }
      default:
        return { 
          color: 'green', 
          text: '正常', 
          icon: null,
          percentage: (currentStock / maxStock) * 100
        }
    }
  }

  // 获取质量状态标签
  const getQualityStatusTag = (status: string) => {
    switch (status) {
      case 'qualified':
        return <Tag color="green">合格</Tag>
      case 'unqualified':
        return <Tag color="red">不合格</Tag>
      case 'pending':
        return <Tag color="orange">待检</Tag>
      default:
        return <Tag>未知</Tag>
    }
  }

  // 表格列定义
  const columns: ColumnsType<MaterialInventory> = [
    {
      title: '原料编码',
      dataIndex: 'materialCode',
      key: 'materialCode',
      width: 120,
      fixed: 'left',
      render: (text: string) => (
        <span style={{ fontFamily: 'monospace', color: '#1890ff' }}>{text}</span>
      )
    },
    {
      title: '原料信息',
      key: 'materialInfo',
      width: 200,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>{record.materialName}</div>
          <div style={{ color: '#666', fontSize: '14px' }}>
            规格: {record.specification}
          </div>
          <div style={{ color: '#999', fontSize: '12px' }}>
            分类: {record.category}
          </div>
        </div>
      )
    },
    {
      title: '质量状态',
      dataIndex: 'qualityStatus',
      key: 'qualityStatus',
      width: 80,
      render: (status: string) => getQualityStatusTag(status)
    },
    {
      title: '当前库存',
      key: 'currentStock',
      width: 120,
      render: (_, record) => {
        const status = getStockStatus(record)
        return (
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '18px', fontWeight: 'bold' }}>
              {record.currentStock.toLocaleString()} {record.unit}
            </div>
            <Badge 
              status={status.color as any} 
              text={status.text}
            />
          </div>
        )
      }
    },
    {
      title: '库存水位',
      key: 'stockLevel',
      width: 150,
      render: (_, record) => {
        const status = getStockStatus(record)
        return (
          <div>
            <Progress 
              percent={status.percentage} 
              size="small" 
              status={record.status === 'normal' ? 'success' : 'exception'}
              format={() => `${record.currentStock}/${record.maxStock}`}
            />
            <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
              最小库存: {record.minStock} {record.unit}
            </div>
          </div>
        )
      }
    },
    {
      title: '库存价值',
      key: 'value',
      width: 120,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>
            ¥{record.totalValue.toLocaleString()}
          </div>
          <div style={{ color: '#666', fontSize: '14px' }}>
            单价: ¥{record.unitPrice}/{record.unit}
          </div>
        </div>
      )
    },
    {
      title: '供应商信息',
      key: 'supplierInfo',
      width: 150,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>{record.supplierName}</div>
          <div style={{ color: '#666', fontSize: '14px' }}>
            {record.supplierContact}
          </div>
        </div>
      )
    },
    {
      title: '库位信息',
      key: 'locationInfo',
      width: 120,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>{record.location}</div>
          <div style={{ color: '#666', fontSize: '14px' }}>
            批次: {record.batchNumber}
          </div>
        </div>
      )
    },
    {
      title: '存储条件',
      dataIndex: 'storageCondition',
      key: 'storageCondition',
      width: 100,
      render: (condition: string) => (
        <Tag color="cyan">{condition}</Tag>
      )
    },
    {
      title: '到货日期',
      dataIndex: 'receivedDate',
      key: 'receivedDate',
      width: 100,
      render: (date: string) => (
        <span>{dayjs(date).format('YYYY-MM-DD')}</span>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="库存盘点">
            <Button 
              type="text" 
              icon={<ToolOutlined />} 
              size="small"
              onClick={() => handleInventoryCheck(record)}
            >
              盘点
            </Button>
          </Tooltip>
          <Tooltip title="库存转移">
            <Button 
              type="text" 
              icon={<SwapOutlined />} 
              size="small"
              onClick={() => handleTransfer(record)}
            >
              转移
            </Button>
          </Tooltip>
          <Tooltip title="补货提醒">
            <Button 
              type="text" 
              icon={<BellOutlined />} 
              size="small"
              onClick={() => handleReplenishment(record)}
              disabled={record.status !== 'low'}
            >
              补货
            </Button>
          </Tooltip>
          <Tooltip title="查看详情">
            <Button 
              type="text" 
              icon={<EyeOutlined />} 
              size="small"
              onClick={() => handleView(record)}
            >
              详情
            </Button>
          </Tooltip>
        </Space>
      ),
    },
  ]

  // 处理库存盘点
  const handleInventoryCheck = (record: MaterialInventory) => {
    modal.confirm({
      title: '库存盘点确认',
      content: `确认对 ${record.materialName} 进行库存盘点吗？`,
      onOk() {
        message.success('库存盘点任务已创建')
      }
    })
  }

  // 处理库存转移
  const handleTransfer = (record: MaterialInventory) => {
    setTransferRecord(record)
    transferForm.setFieldsValue({
      materialCode: record.materialCode,
      materialName: record.materialName,
      currentLocation: record.location,
      currentStock: record.currentStock,
      unit: record.unit
    })
    setIsTransferModalVisible(true)
  }

  // 处理补货提醒
  const handleReplenishment = (record: MaterialInventory) => {
    setIsReplenishModalVisible(true)
  }

  // 处理查看详情
  const handleView = (record: MaterialInventory) => {
    modal.info({
      title: '原料库存详情',
      width: 700,
      content: (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <div><strong>原料编码:</strong> {record.materialCode}</div>
            </Col>
            <Col span={12}>
              <div><strong>原料名称:</strong> {record.materialName}</div>
            </Col>
            <Col span={12}>
              <div><strong>规格型号:</strong> {record.specification}</div>
            </Col>
            <Col span={12}>
              <div><strong>分类:</strong> {record.category}</div>
            </Col>
            <Col span={12}>
              <div><strong>当前库存:</strong> {record.currentStock} {record.unit}</div>
            </Col>
            <Col span={12}>
              <div><strong>最小库存:</strong> {record.minStock} {record.unit}</div>
            </Col>
            <Col span={12}>
              <div><strong>库位:</strong> {record.location}</div>
            </Col>
            <Col span={12}>
              <div><strong>批次号:</strong> {record.batchNumber}</div>
            </Col>
            <Col span={12}>
              <div><strong>到货日期:</strong> {record.receivedDate}</div>
            </Col>
            <Col span={12}>
              <div><strong>供应商:</strong> {record.supplierName}</div>
            </Col>
            <Col span={12}>
              <div><strong>存储条件:</strong> {record.storageCondition}</div>
            </Col>
            <Col span={12}>
              <div><strong>质量状态:</strong> {getQualityStatusTag(record.qualityStatus)}</div>
            </Col>
          </Row>
        </div>
      ),
    })
  }

  // 统计数据
  const statistics = {
    totalMaterials: materialInventory.length,
    totalValue: materialInventory.reduce((sum, item) => sum + item.totalValue, 0),
    lowStockCount: materialInventory.filter(item => item.status === 'low').length,
    normalStockCount: materialInventory.filter(item => item.status === 'normal').length,
    alertCount: replenishmentAlerts.length
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: '24px' }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <InboxOutlined style={{ fontSize: '24px', color: '#1890ff', marginRight: '12px' }} />
          <div>
            <h1 style={{ fontSize: '24px', fontWeight: 'bold', margin: '0 0 8px 0' }}>原料库存管理</h1>
            <p style={{ color: '#666', margin: 0 }}>管理原材料库存，包括原料入库、出库、库存查询、采购预警等</p>
          </div>
        </div>
      </div>

      {/* 补货提醒 */}
      {replenishmentAlerts.length > 0 && (
        <Alert
          message="补货提醒"
          description={
            <div>
              <p>以下原料库存不足，建议及时补货：</p>
              <ul style={{ marginTop: '8px' }}>
                {replenishmentAlerts.map(alert => (
                  <li key={alert.id} style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '4px 0' }}>
                    <span>
                      {alert.materialName} - 当前库存: {alert.currentStock}，
                      缺口: {alert.shortage}，建议采购: {alert.suggestedQuantity}
                    </span>
                    <Button 
                      size="small" 
                      type="link"
                      icon={<ShoppingCartOutlined />}
                      onClick={() => setIsReplenishModalVisible(true)}
                    >
                      立即采购
                    </Button>
                  </li>
                ))}
              </ul>
            </div>
          }
          type="warning"
          showIcon
          closable
        />
      )}

      {/* 统计卡片 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="原料总数"
              value={statistics.totalMaterials}
              suffix="种"
              prefix={<InboxOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="库存总值"
              value={statistics.totalValue}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="库存不足"
              value={statistics.lowStockCount}
              suffix="种"
              valueStyle={{ color: '#ff4d4f' }}
              prefix={<WarningOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="补货提醒"
              value={statistics.alertCount}
              suffix="项"
              valueStyle={{ color: '#fa8c16' }}
              prefix={<BellOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容区域 */}
      <Card>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
          {/* 操作区域 */}
          <Row justify="space-between" align="middle" gutter={[16, 16]}>
            <Col xs={24} lg={18}>
              <Row gutter={[16, 16]}>
              <Input
                placeholder="搜索原料编码、名称或规格"
                prefix={<SearchOutlined />}
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                style={{ width: '100%' }}
              />
              <Select
                placeholder="原料分类"
                value={categoryFilter}
                onChange={setCategoryFilter}
                style={{ width: '100%' }}
                allowClear
              >
                <Option value="">全部分类</Option>
                <Option value="金属材料">金属材料</Option>
                <Option value="化工原料">化工原料</Option>
                <Option value="橡胶制品">橡胶制品</Option>
                <Option value="电子元件">电子元件</Option>
              </Select>
              <Select
                placeholder="库存状态"
                value={statusFilter}
                onChange={setStatusFilter}
                style={{ width: '100%' }}
                allowClear
              >
                <Option value="">全部状态</Option>
                <Option value="normal">正常</Option>
                <Option value="low">库存不足</Option>
                <Option value="high">库存过多</Option>
                <Option value="expired">已过期</Option>
                <Option value="damaged">损坏</Option>
                <Option value="reserved">已预留</Option>
              </Select>
              <Select
                placeholder="供应商"
                value={supplierFilter}
                onChange={setSupplierFilter}
                style={{ width: '100%' }}
                allowClear
              >
                <Option value="">全部供应商</Option>
                <Option value="钢材供应商A">钢材供应商A</Option>
                <Option value="有色金属公司">有色金属公司</Option>
                <Option value="化工材料厂">化工材料厂</Option>
                <Option value="橡胶制品厂">橡胶制品厂</Option>
              </Select>
              </Row>
            </Col>
            <Col xs={24} lg={6}>
              <Row gutter={[8, 8]} justify="end">
                <Col>
                  <Button icon={<SyncOutlined />} onClick={() => setLoading(true)}>
                    刷新
                  </Button>
                </Col>
                <Col>
                  <Button icon={<BarChartOutlined />}>
                    库存报表
                  </Button>
                </Col>
                <Col>
                  <Button icon={<ExportOutlined />}>
                    导出数据
                  </Button>
                </Col>
                <Col>
                  <Button icon={<ShoppingCartOutlined />}>
                    采购申请
                  </Button>
                </Col>
                <Col>
                  <Button type="primary" icon={<PlusOutlined />}>
                    原料入库
                  </Button>
                </Col>
              </Row>
            </Col>
          </Row>

          {/* 原料库存列表 */}
          <Table
            columns={columns}
            dataSource={materialInventory.filter(item => {
              const matchesSearch = !searchText ||
                item.materialCode.toLowerCase().includes(searchText.toLowerCase()) ||
                item.materialName.toLowerCase().includes(searchText.toLowerCase()) ||
                item.specification.toLowerCase().includes(searchText.toLowerCase())
              const matchesCategory = !categoryFilter || item.category === categoryFilter
              const matchesStatus = !statusFilter || item.status === statusFilter
              const matchesSupplier = !supplierFilter || item.supplierName === supplierFilter
              return matchesSearch && matchesCategory && matchesStatus && matchesSupplier
            })}
            rowKey="id"
            loading={loading}
            rowSelection={{
              selectedRowKeys,
              onChange: setSelectedRowKeys,
              getCheckboxProps: (record) => ({
                disabled: record.status === 'damaged',
              }),
            }}
            pagination={{
              total: materialInventory.length,
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            }}
            scroll={{ x: 1600 }}
            size="small"
          />
        </div>
      </Card>

      {/* 库存转移弹窗 */}
      <Modal
        title="库存转移"
        open={isTransferModalVisible}
        onCancel={() => {
          setIsTransferModalVisible(false)
          setTransferRecord(null)
          transferForm.resetFields()
        }}
        footer={[
          <Button key="cancel" onClick={() => setIsTransferModalVisible(false)}>
            取消
          </Button>,
          <Button key="submit" type="primary" onClick={() => {
            transferForm.validateFields().then(values => {
              message.success('库存转移成功')
              setIsTransferModalVisible(false)
              transferForm.resetFields()
            })
          }}>
            确认转移
          </Button>
        ]}
        width={600}
      >
        <Form
          form={transferForm}
          layout="vertical"
          style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="原料编码" name="materialCode">
                <Input disabled />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="原料名称" name="materialName">
                <Input disabled />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="当前库位" name="currentLocation">
                <Input disabled />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="当前库存" name="currentStock">
                <InputNumber disabled style={{ width: '100%' }} addonAfter={transferRecord?.unit} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="目标库位"
                name="targetLocation"
                rules={[{ required: true, message: '请选择目标库位' }]}
              >
                <Select placeholder="请选择目标库位">
                  <Option value="M区-01-002">M区-01-002</Option>
                  <Option value="M区-01-003">M区-01-003</Option>
                  <Option value="M区-02-001">M区-02-001</Option>
                  <Option value="M区-02-002">M区-02-002</Option>
                  <Option value="M区-03-001">M区-03-001</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="转移数量"
                name="transferQuantity"
                rules={[
                  { required: true, message: '请输入转移数量' },
                  { type: 'number', min: 1, message: '数量必须大于0' }
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="请输入转移数量"
                  min={1}
                  max={transferRecord?.currentStock}
                  addonAfter={transferRecord?.unit}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="转移原因"
            name="transferReason"
            rules={[{ required: true, message: '请选择转移原因' }]}
          >
            <Select placeholder="请选择转移原因">
              <Option value="库位优化">库位优化</Option>
              <Option value="生产需要">生产需要</Option>
              <Option value="库位维护">库位维护</Option>
              <Option value="安全考虑">安全考虑</Option>
              <Option value="其他">其他</Option>
            </Select>
          </Form.Item>

          <Form.Item label="备注" name="remark">
            <Input.TextArea
              rows={3}
              placeholder="请输入转移备注信息"
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 补货提醒弹窗 */}
      <Modal
        title="补货提醒管理"
        open={isReplenishModalVisible}
        onCancel={() => setIsReplenishModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setIsReplenishModalVisible(false)}>
            关闭
          </Button>,
          <Button key="purchase" type="primary" icon={<ShoppingCartOutlined />}>
            创建采购申请
          </Button>
        ]}
        width={800}
      >
        <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
          <Alert
            message="系统检测到以下原料需要补货"
            type="info"
            showIcon
          />

          <Table
            dataSource={replenishmentAlerts}
            rowKey="id"
            pagination={false}
            size="small"
            columns={[
              {
                title: '原料编码',
                dataIndex: 'materialCode',
                width: 100
              },
              {
                title: '原料名称',
                dataIndex: 'materialName',
                width: 120
              },
              {
                title: '当前库存',
                dataIndex: 'currentStock',
                width: 80,
                render: (stock: number) => (
                  <span style={{ color: '#ff4d4f', fontWeight: 'bold' }}>{stock}</span>
                )
              },
              {
                title: '最小库存',
                dataIndex: 'minStock',
                width: 80
              },
              {
                title: '缺口数量',
                dataIndex: 'shortage',
                width: 80,
                render: (shortage: number) => (
                  <span style={{ color: '#ff4d4f' }}>{shortage}</span>
                )
              },
              {
                title: '建议采购',
                dataIndex: 'suggestedQuantity',
                width: 80,
                render: (quantity: number) => (
                  <span style={{ color: '#1890ff', fontWeight: 'bold' }}>{quantity}</span>
                )
              },
              {
                title: '预估成本',
                dataIndex: 'estimatedCost',
                width: 100,
                render: (cost: number) => `¥${cost.toLocaleString()}`
              },
              {
                title: '供应商',
                dataIndex: 'supplierName',
                width: 120
              },
              {
                title: '交期',
                dataIndex: 'leadTime',
                width: 60,
                render: (days: number) => `${days}天`
              },
              {
                title: '紧急程度',
                dataIndex: 'urgencyLevel',
                width: 80,
                render: (level: string) => {
                  const color = level === 'high' ? 'red' : level === 'medium' ? 'orange' : 'green'
                  const text = level === 'high' ? '紧急' : level === 'medium' ? '一般' : '不急'
                  return <Tag color={color}>{text}</Tag>
                }
              }
            ]}
          />
        </div>
      </Modal>
    </div>
  )
}

// 用App组件包裹以提供message和modal等上下文
const MaterialInventoryPage: React.FC = () => {
  return (
    <App>
      <MaterialInventoryPageComponent />
    </App>
  )
}

export default MaterialInventoryPage
