'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { Card, Table, Button, Space, Tag, Input, Select, Modal, Form, Row, Col, Statistic, App, Popconfirm, Tooltip, Switch, Tabs, Alert, Typography } from 'antd'
import { PlusOutlined, EditOutlined, DeleteOutlined, SettingOutlined, ToolOutlined, ReloadOutlined, TableOutlined, AppstoreOutlined, Bar<PERSON>hartOutlined, DownloadOutlined } from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import { getFormValidationRules } from '@/utils/validation/workstationValidation'
import { Workstation } from '@/types'
import WorkstationCard from './WorkstationCard'
import { handleApiResponse } from '@/utils/dataAccessErrorHandler'
import { workstationDataValidator, ConsistencyReport } from '@/services/validation/WorkstationDataConsistencyValidator'
import { workstationUpdateService, UpdateContext } from '@/services/workstation/WorkstationUpdateService'
import { useWorkstationSync } from '@/hooks/useWorkstationRealtime'
import dayjs from 'dayjs'

const { Search } = Input
const { Option } = Select
const { Text } = Typography

interface WorkstationManagementTabProps {
  loading?: boolean
}

const WorkstationManagementTab: React.FC<WorkstationManagementTabProps> = ({ loading = false }) => {
  const { message } = App.useApp()
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [editingWorkstation, setEditingWorkstation] = useState<Workstation | null>(null)
  const [form] = Form.useForm()
  const [searchText, setSearchText] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [viewMode, setViewMode] = useState<'table' | 'card'>('table')
  const [activeTab, setActiveTab] = useState('list')

  // 使用dataAccessManager进行数据管理 - 遵循数据调用规范
  const [workstations, setWorkstations] = useState<Workstation[]>([])
  const [dataLoading, setDataLoading] = useState(false)

  // 数据验证相关状态
  const [validationLoading, setValidationLoading] = useState(false)
  const [consistencyReport, setConsistencyReport] = useState<ConsistencyReport | null>(null)
  const [validationModalVisible, setValidationModalVisible] = useState(false)

  // 实时同步功能 - 自动同步工位状态变更
  const { connectionStatus, isConnected } = useWorkstationSync(
    workstations,
    setWorkstations,
    {
      autoConnect: true,
      onConnectionError: (error) => {
        console.error('工位实时同步连接错误:', error)
        message.warning('实时同步连接异常，数据可能不是最新的')
      },
      onConnectionStatusChange: (status) => {
        console.log(`工位实时同步状态: ${status}`)
        if (status === 'connected') {
          message.success('实时同步已连接', 2)
        } else if (status === 'disconnected') {
          message.warning('实时同步已断开', 2)
        }
      }
    }
  )

  // 数据加载函数 - 使用dataAccessManager
  const loadWorkstations = useCallback(async () => {
    setDataLoading(true)

    try {
      const response = await dataAccessManager.workstations.getAll()

      if (response.status === 'success' && response.data && response.data.items) {
        setWorkstations(response.data.items)
        console.log({
          count: response.data.items.length,
          workstations: response.data.items.map(ws => ({ id: ws.id, code: ws.code, name: ws.name }))
        })
      } else {
        message.error(response.message || '加载工位数据失败')
      }
    } catch (error) {
      message.error('系统错误，请稍后重试')
    } finally {
      setDataLoading(false)
    }
  }, [message])

  // 初始化数据加载
  useEffect(() => {
    loadWorkstations()
  }, [loadWorkstations])

  // 生成工位编码
  const generateWorkstationCode = (): string => {
    const existingCodes = workstations.map(ws => ws.code)
    // 内联工位编码生成逻辑
    const letters = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H']
    for (const letter of letters) {
      for (let i = 1; i <= 99; i++) {
        const code = `${letter}${i}`
        if (!existingCodes.includes(code)) {
          return code
        }
      }
    }
    return `A${existingCodes.length + 1}`
  }

  // 导出工位数据
  const handleExport = () => {
    try {
      // 内联CSV导出逻辑
      const headers = ['工位编码', '工位名称', '描述', '状态', '创建时间', '更新时间']
      const csvRows = [
        headers.join(','),
        ...filteredData.map(ws => [
          ws.code,
          ws.name,
          ws.description || '',
          ws.status === 'active' ? '启用' : '停用',
          new Date(ws.createdAt).toLocaleDateString(),
          new Date(ws.updatedAt).toLocaleDateString()
        ].join(','))
      ]
      const csvContent = csvRows.join('\n')

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', `工位数据_${new Date().toISOString().split('T')[0]}.csv`)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      message.success('工位数据导出成功')
    } catch (error) {
      message.error('导出失败，请稍后重试')
    }
  }

  // 处理新增工位
  const handleCreate = () => {
    setEditingWorkstation(null)
    form.resetFields()
    form.setFieldsValue({
      code: generateWorkstationCode(),
      status: 'active'
    })
    setIsModalVisible(true)
  }

  // 处理编辑工位
  const handleEdit = (record: Workstation) => {
    setEditingWorkstation(record)
    form.setFieldsValue({
      ...record
    })
    setIsModalVisible(true)
  }

  // 处理删除工位 - 使用dataAccessManager
  const handleDelete = async (id: string) => {
    const success = await handleApiResponse(
      () => dataAccessManager.workstations.delete(id),
      '删除工位'
    )

    if (success) {
      message.success('工位删除成功')

      // 🔧 修复：强制清理工位缓存，确保UI获取最新数据
      dataAccessManager.clearServiceCache('WorkstationService')

      await loadWorkstations() // 重新加载数据
    }
  }

  // 处理状态切换 - 使用统一更新服务
  const handleStatusToggle = async (record: Workstation) => {
    try {
      const newStatus = record.status === 'active' ? 'inactive' : 'active'

      console.log(`🔄 开始切换工位 ${record.code} 状态: ${record.status} → ${newStatus}`)

      const updateContext: UpdateContext = {
        source: 'user',
        operation: 'status_change',
        userId: 'current_user', // TODO: 从用户上下文获取
        reason: `用户手动${newStatus === 'active' ? '启用' : '停用'}工位`
      }

      const result = await workstationUpdateService.updateWorkstation(
        record.id,
        { status: newStatus },
        updateContext,
        record.version // 使用当前版本号进行乐观锁控制
      )

      if (result.success) {
        message.success(`工位已${newStatus === 'active' ? '启用' : '停用'}`)

        // 🔧 修复：强制清理工位缓存，确保UI获取最新数据
        dataAccessManager.clearServiceCache('WorkstationService')

        await loadWorkstations() // 重新加载数据
        console.log(`✅ 工位 ${record.code} 状态切换成功`)
      } else {
        if (result.conflictInfo) {
          message.error(`状态切换失败：数据已被其他用户修改，请刷新后重试`)

          // 🔧 修复：冲突时也清理缓存，确保获取最新状态
          dataAccessManager.clearServiceCache('WorkstationService')

          await loadWorkstations() // 刷新数据以获取最新状态
        } else {
          message.error(`状态切换失败：${result.error}`)
        }
        console.error(`❌ 工位 ${record.code} 状态切换失败:`, result.error)
      }

      // 显示警告信息（如果有）
      if (result.warnings && result.warnings.length > 0) {
        result.warnings.forEach(warning => {
          message.warning(warning)
        })
      }
    } catch (error) {
      console.error(`❌ 工位状态切换异常:`, error)
      message.error('系统错误，请稍后重试')
    }
  }

  // 处理工位状态重置 - 将工位重置为空闲状态
  const handleResetWorkstation = async (record: Workstation) => {
    try {
      console.log(`🔄 开始重置工位 ${record.code} 状态`)

      const updateContext: UpdateContext = {
        source: 'user',
        operation: 'reset',
        userId: 'current_user', // TODO: 从用户上下文获取
        reason: '用户手动重置工位为空闲状态'
      }

      // 重置工位到空闲状态的数据
      const resetData = {
        currentMoldNumber: null,
        currentBatchNumber: null,
        batchNumberQueue: [],
        lastEndTime: null
      }

      const result = await workstationUpdateService.updateWorkstation(
        record.id,
        resetData,
        updateContext,
        record.version // 使用当前版本号进行乐观锁控制
      )

      if (result.success) {
        message.success(`工位 ${record.code} 已重置为空闲状态`)

        // 🔧 修复：强制清理工位缓存，确保UI获取最新数据
        dataAccessManager.clearServiceCache('WorkstationService')

        await loadWorkstations() // 重新加载数据以更新界面
        console.log(`✅ 工位 ${record.code} 重置完成`)
      } else {
        if (result.conflictInfo) {
          message.error(`重置失败：数据已被其他用户修改，请刷新后重试`)

          // 🔧 修复：冲突时也清理缓存，确保获取最新状态
          dataAccessManager.clearServiceCache('WorkstationService')

          await loadWorkstations() // 刷新数据以获取最新状态
        } else {
          message.error(`重置失败：${result.error}`)
        }
        console.error(`❌ 工位 ${record.code} 重置失败:`, result.error)
      }

      // 显示警告信息（如果有）
      if (result.warnings && result.warnings.length > 0) {
        result.warnings.forEach(warning => {
          message.warning(warning)
        })
      }
    } catch (error) {
      console.error(`❌ 重置工位 ${record.code} 状态异常:`, error)
      message.error('系统错误，请稍后重试')
    }
  }

  // 处理数据一致性验证
  const handleValidateConsistency = async () => {
    try {
      setValidationLoading(true)
      console.log('🔍 开始工位数据一致性验证')

      const report = await workstationDataValidator.validateAllWorkstations()
      setConsistencyReport(report)
      setValidationModalVisible(true)

      if (report.invalidWorkstations === 0) {
        message.success('所有工位数据一致性验证通过')
      } else {
        message.warning(`发现 ${report.invalidWorkstations} 个工位存在数据一致性问题`)
      }

      console.log('✅ 数据一致性验证完成:', report)
    } catch (error) {
      console.error('❌ 数据一致性验证失败:', error)
      message.error('数据一致性验证失败，请稍后重试')
    } finally {
      setValidationLoading(false)
    }
  }

  // 处理自动修复数据一致性问题
  const handleAutoFix = async () => {
    if (!consistencyReport || consistencyReport.issues.length === 0) {
      message.info('没有需要修复的问题')
      return
    }

    try {
      console.log('🔧 开始自动修复数据一致性问题')

      const fixResult = await workstationDataValidator.autoFixInconsistencies(consistencyReport.issues)

      if (fixResult.fixed > 0) {
        message.success(`成功修复 ${fixResult.fixed} 个工位的数据问题`)
        await loadWorkstations() // 重新加载数据

        // 重新验证
        const newReport = await workstationDataValidator.validateAllWorkstations()
        setConsistencyReport(newReport)
      }

      if (fixResult.failed > 0) {
        message.warning(`${fixResult.failed} 个工位的问题需要手动处理`)
      }

      console.log('✅ 自动修复完成:', fixResult)
    } catch (error) {
      console.error('❌ 自动修复失败:', error)
      message.error('自动修复失败，请稍后重试')
    }
  }

  // 处理表单提交 - 使用dataAccessManager
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields()

      if (editingWorkstation) {
        // 编辑模式 - 使用统一更新服务
        console.log(`🔄 开始更新工位 ${editingWorkstation.code}`)

        const updateContext: UpdateContext = {
          source: 'user',
          operation: 'general',
          userId: 'current_user', // TODO: 从用户上下文获取
          reason: '用户编辑工位信息'
        }

        const result = await workstationUpdateService.updateWorkstation(
          editingWorkstation.id,
          values,
          updateContext,
          editingWorkstation.version // 使用当前版本号进行乐观锁控制
        )

        if (result.success) {
          message.success('工位更新成功')

          // 🔧 修复：强制清理工位缓存，确保UI获取最新数据
          dataAccessManager.clearServiceCache('WorkstationService')

          await loadWorkstations() // 重新加载数据
          console.log(`✅ 工位 ${editingWorkstation.code} 更新成功`)
        } else {
          if (result.conflictInfo) {
            message.error(`更新失败：数据已被其他用户修改，请刷新后重试`)

            // 🔧 修复：冲突时也清理缓存，确保获取最新状态
            dataAccessManager.clearServiceCache('WorkstationService')

            await loadWorkstations() // 刷新数据以获取最新状态
          } else {
            message.error(`更新失败：${result.error}`)
          }
          console.error(`❌ 工位 ${editingWorkstation.code} 更新失败:`, result.error)
          return // 不关闭对话框，让用户重试
        }

        // 显示警告信息（如果有）
        if (result.warnings && result.warnings.length > 0) {
          result.warnings.forEach(warning => {
            message.warning(warning)
          })
        }
      } else {
        // 新增模式 - 直接使用 dataAccessManager（新建不需要版本控制）
        const newWorkstation: Workstation = {
          id: `ws_${Date.now()}`,
          ...values,
          // 版本控制字段将在 WorkstationDataAccessService.create 中自动设置
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        } as Workstation

        const createdWorkstation = await handleApiResponse(
          () => dataAccessManager.workstations.create(newWorkstation),
          '创建工位'
        )

        if (createdWorkstation) {
          message.success('工位创建成功')

          // 🔧 修复：强制清理工位缓存，确保UI获取最新数据
          dataAccessManager.clearServiceCache('WorkstationService')

          await loadWorkstations() // 重新加载数据
          console.log(`✅ 工位 ${values.code} 创建成功`)
        } else {
          return // 不关闭对话框，让用户重试
        }
      }

      setIsModalVisible(false)
      form.resetFields()
      setEditingWorkstation(null)
    } catch (error) {
      console.error('工位操作失败:', error)
      message.error('操作失败，请重试')
    }
  }

  // 数据筛选
  const filteredData = workstations.filter(workstation => {
    const matchesSearch = !searchText ||
      workstation.name.toLowerCase().includes(searchText.toLowerCase()) ||
      workstation.code.toLowerCase().includes(searchText.toLowerCase()) ||
      (workstation.description && workstation.description.toLowerCase().includes(searchText.toLowerCase()))

    const matchesStatus = !statusFilter || workstation.status === statusFilter

    return matchesSearch && matchesStatus
  })

  // 统计数据
  const statistics = {
    total: workstations.length,
    active: workstations.filter(ws => ws.status === 'active').length,
    inactive: workstations.filter(ws => ws.status === 'inactive').length
  }

  // 表格列定义
  const columns: ColumnsType<Workstation> = [
    {
      title: '工位编码',
      dataIndex: 'code',
      key: 'code',
      width: 100,
      fixed: 'left',
      sorter: (a, b) => a.code.localeCompare(b.code)
    },
    {
      title: '工位名称',
      dataIndex: 'name',
      key: 'name',
      width: 80,
      ellipsis: true
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={status === 'active' ? 'success' : 'default'}>
          {status === 'active' ? '启用' : '停用'}
        </Tag>
      ),
      filters: [
        { text: '启用', value: 'active' },
        { text: '停用', value: 'inactive' }
      ],
      onFilter: (value, record) => record.status === value
    },

    {
      title: '当前模具',
      dataIndex: 'currentMoldNumber',
      key: 'currentMoldNumber',
      width: 120,
      render: (moldNumber: string | null, record: Workstation) => {
        if (!moldNumber) {
          return (
            <Tooltip title="工位空闲，无当前模具">
              <Tag color="default" style={{ color: '#999' }}>
                空闲
              </Tag>
            </Tooltip>
          )
        }
        return (
          <Tooltip title={`当前使用模具: ${moldNumber}`}>
            <Tag color="blue">{moldNumber}</Tag>
          </Tooltip>
        )
      }
    },
    {
      title: '当前批次',
      dataIndex: 'currentBatchNumber',
      key: 'currentBatchNumber',
      width: 140,
      render: (batchNumber: string | null, record: Workstation) => {
        if (!batchNumber) {
          return (
            <Tooltip title="工位空闲，无生产批次">
              <Tag color="default" style={{ color: '#999' }}>
                无任务
              </Tag>
            </Tooltip>
          )
        }
        return (
          <Tooltip title={`当前生产批次: ${batchNumber}`}>
            <Tag color="green">{batchNumber}</Tag>
          </Tooltip>
        )
      }
    },
    {
      title: '队列任务',
      dataIndex: 'batchNumberQueue',
      key: 'batchNumberQueue',
      width: 120,
      render: (queue: string[] = [], record: Workstation) => {
        const hasQueue = queue && queue.length > 0
        const isIdle = !record.currentMoldNumber && !record.currentBatchNumber

        if (isIdle && !hasQueue) {
          return (
            <Tooltip title="工位空闲，无排队任务">
              <Tag color="default" style={{ color: '#999' }}>
                空闲中
              </Tag>
            </Tooltip>
          )
        }

        return (
          <Tooltip title={hasQueue ? `排队任务: ${queue.join(', ')}` : '无排队任务'}>
            <Tag color={hasQueue ? 'orange' : 'default'}>
              {queue.length} 个任务
            </Tag>
          </Tooltip>
        )
      }
    },
    {
      title: '预计结束',
      dataIndex: 'lastEndTime',
      key: 'lastEndTime',
      width: 140,
      render: (endTime: string | null, record: Workstation) => {
        if (!endTime) {
          const isIdle = !record.currentMoldNumber && !record.currentBatchNumber
          return (
            <Tooltip title={isIdle ? "工位空闲，无预计结束时间" : "暂无预计结束时间"}>
              <Text type="secondary" style={{ color: '#ccc' }}>
                {isIdle ? '空闲中' : '-'}
              </Text>
            </Tooltip>
          )
        }

        return (
          <Tooltip title={`预计结束时间: ${endTime}`}>
            <Text type="secondary">
              {dayjs(endTime).format('MM-DD HH:mm')}
            </Text>
          </Tooltip>
        )
      }
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
              size="small"
            />
          </Tooltip>
          <Tooltip title={record.status === 'active' ? '停用' : '启用'}>
            <Switch
              size="small"
              checked={record.status === 'active'}
              onChange={() => handleStatusToggle(record)}
            />
          </Tooltip>

          {/* 重置工位状态按钮 - 只有在工位有生产任务时才显示 */}
          {(record.currentMoldNumber || record.currentBatchNumber || (record.batchNumberQueue && record.batchNumberQueue.length > 0)) && (
            <Popconfirm
              title="重置工位状态"
              description="确定要将此工位重置为空闲状态吗？这将清空当前模具、批次和队列信息。"
              onConfirm={() => handleResetWorkstation(record)}
              okText="确定重置"
              cancelText="取消"
            >
              <Tooltip title="重置为空闲状态">
                <Button
                  type="text"
                  icon={<ReloadOutlined />}
                  size="small"
                  style={{ color: '#faad14' }}
                />
              </Tooltip>
            </Popconfirm>
          )}

          <Popconfirm
            title="确定要删除这个工位吗？"
            description="删除后将无法恢复，请谨慎操作。"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="text"
                icon={<DeleteOutlined />}
                danger
                size="small"
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ]

  return (
    <div style={{ padding: 0 }}>
      {/* 操作栏 */}
      <Card size="small" style={{ marginBottom: '16px' }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Search
                placeholder="搜索工位编码、名称或描述"
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                style={{ width: 250 }}
                allowClear
              />
              <Select
                placeholder="筛选状态"
                value={statusFilter}
                onChange={setStatusFilter}
                style={{ width: 120 }}
                allowClear
              >
                <Option value="active">启用</Option>
                <Option value="inactive">停用</Option>
              </Select>
              {/* 实时同步状态指示器 */}
              <Tooltip
                title={
                  isConnected
                    ? '实时同步已连接，数据将自动更新'
                    : `实时同步状态: ${connectionStatus}`
                }
              >
                <Tag
                  color={isConnected ? 'green' : connectionStatus === 'connecting' ? 'orange' : 'red'}
                  style={{ cursor: 'help' }}
                >
                  {isConnected ? '🟢 实时同步' : connectionStatus === 'connecting' ? '🟡 连接中' : '🔴 离线'}
                </Tag>
              </Tooltip>
            </Space>
          </Col>
          <Col>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={loadWorkstations}
                loading={dataLoading}
              >
                刷新
              </Button>
              <Button
                icon={<DownloadOutlined />}
                onClick={handleExport}
              >
                导出
              </Button>
              <Button
                icon={<BarChartOutlined />}
                onClick={handleValidateConsistency}
                loading={validationLoading}
              >
                数据验证
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleCreate}
              >
                新增工位
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: '16px' }}>
        <Col xs={24} sm={12} md={8}>
          <Card size="small">
            <Statistic
              title="工位总数"
              value={statistics.total}
              prefix={<SettingOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8}>
          <Card size="small">
            <Statistic
              title="启用工位"
              value={statistics.active}
              prefix={<ToolOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8}>
          <Card size="small">
            <Statistic
              title="停用工位"
              value={statistics.inactive}
              prefix={<ToolOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容区域 */}
      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          tabBarExtraContent={
            activeTab === 'list' && (
              <Space.Compact size="small">
                <Button
                  type={viewMode === 'table' ? 'primary' : 'default'}
                  icon={<TableOutlined />}
                  onClick={() => setViewMode('table')}
                  size="small"
                >
                  表格
                </Button>
                <Button
                  type={viewMode === 'card' ? 'primary' : 'default'}
                  icon={<AppstoreOutlined />}
                  onClick={() => setViewMode('card')}
                  size="small"
                >
                  卡片
                </Button>
              </Space.Compact>
            )
          }
          items={[
            {
              key: 'list',
              label: (
                <span>
                  <SettingOutlined />
                  工位列表 ({filteredData.length})
                </span>
              ),
              children: viewMode === 'table' ? (
                <Table
                  columns={columns}
                  dataSource={filteredData}
                  rowKey="id"
                  loading={dataLoading}
                  pagination={{
                    total: filteredData.length,
                    pageSize: 10,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
                  }}
                  scroll={{ x: 1200 }}
                />
              ) : (
                <Row gutter={[16, 16]}>
                  {filteredData.map(workstation => (
                    <Col xs={24} sm={12} lg={8} xl={6} key={workstation.id}>
                      <WorkstationCard
                        workstation={workstation}
                        onEdit={handleEdit}
                        onDelete={handleDelete}
                        onStatusToggle={handleStatusToggle}
                      />
                    </Col>
                  ))}
                </Row>
              )
            },

            {
              key: 'report',
              label: '统计报告',
              icon: <BarChartOutlined />,
              children: (
                <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
                  {/* 总体统计 */}
                  <Card title="总体统计" size="small">
                    <Row gutter={[16, 16]}>
                      <Col xs={24} sm={12} md={8}>
                        <Statistic
                          title="工位利用率"
                          value={statistics.total > 0 ? Math.round((statistics.active / statistics.total) * 100) : 0}
                          suffix="%"
                          valueStyle={{ color: '#52c41a' }}
                        />
                      </Col>
                      <Col xs={24} sm={12} md={8}>
                        <Alert
                          message="功能开发中"
                          description="产能统计功能正在开发中，敬请期待"
                          type="info"
                          showIcon
                          style={{ height: '100%' }}
                        />
                      </Col>
                    </Row>
                  </Card>

                  {/* 改进建议 */}
                  <Card title="改进建议" size="small">
                    <Alert
                      message="系统建议"
                      description={
                        <ul style={{
                          listStyleType: 'disc',
                          listStylePosition: 'inside',
                          display: 'flex',
                          flexDirection: 'column',
                          gap: '4px',
                          margin: 0,
                          padding: 0
                        }}>
                          {statistics.inactive > 0 && (
                            <li>有 {statistics.inactive} 个工位处于停用状态，建议检查设备状况</li>
                          )}
                          {statistics.total < 5 && (
                            <li>工位数量偏少，建议适当增加工位配置</li>
                          )}
                          {statistics.active === statistics.total && statistics.total > 0 && (
                            <li>所有工位均已启用，工位状态管理良好</li>
                          )}
                        </ul>
                      }
                      type="info"
                      showIcon
                    />
                  </Card>
                </div>
              )
            }
          ]}
        />
      </Card>

      {/* 新增/编辑工位弹窗 */}
      <Modal
        title={editingWorkstation ? '编辑工位' : '新增工位'}
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={() => {
          setIsModalVisible(false)
          form.resetFields()
        }}
        width={600}
        okText="保存"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            status: 'active'
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="工位编码"
                name="code"
                rules={getFormValidationRules('code')}
              >
                <Input 
                  placeholder="如：A1, B2" 
                  disabled={!!editingWorkstation}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="工位名称"
                name="name"
                rules={getFormValidationRules('name')}
              >
                <Input placeholder="请输入工位名称" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="工位描述"
            name="description"
            rules={getFormValidationRules('description')}
          >
            <Input placeholder="请输入工位描述" />
          </Form.Item>



          <Form.Item
            label="状态"
            name="status"
            rules={[{ required: true, message: '请选择状态' }]}
          >
            <Select>
              <Option value="active">启用</Option>
              <Option value="inactive">停用</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 数据一致性验证结果弹窗 */}
      <Modal
        title="工位数据一致性验证报告"
        open={validationModalVisible}
        onCancel={() => setValidationModalVisible(false)}
        width={800}
        footer={[
          <Button key="close" onClick={() => setValidationModalVisible(false)}>
            关闭
          </Button>,
          consistencyReport && consistencyReport.invalidWorkstations > 0 && (
            <Button key="autofix" type="primary" onClick={handleAutoFix}>
              自动修复
            </Button>
          )
        ]}
      >
        {consistencyReport && (
          <div>
            {/* 验证概览 */}
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={6}>
                <Statistic
                  title="总工位数"
                  value={consistencyReport.totalWorkstations}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="验证通过"
                  value={consistencyReport.validWorkstations}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="存在问题"
                  value={consistencyReport.invalidWorkstations}
                  valueStyle={{ color: '#ff4d4f' }}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="验证时间"
                  value={dayjs(consistencyReport.generatedAt).format('HH:mm:ss')}
                  valueStyle={{ fontSize: 14 }}
                />
              </Col>
            </Row>

            {/* 问题统计 */}
            {consistencyReport.invalidWorkstations > 0 && (
              <>
                <Alert
                  message="发现数据一致性问题"
                  description={`共发现 ${consistencyReport.invalidWorkstations} 个工位存在数据一致性问题，建议及时处理。`}
                  type="warning"
                  showIcon
                  style={{ marginBottom: 16 }}
                />

                {/* 问题详情 */}
                <div style={{ maxHeight: 400, overflowY: 'auto' }}>
                  {consistencyReport.issues.map((issue, index) => (
                    <Card key={index} size="small" style={{ marginBottom: 8 }}>
                      <div>
                        <Text strong>工位 {issue.workstationCode}</Text>
                        <div style={{ marginTop: 8 }}>
                          {issue.issues.map((problem, problemIndex) => (
                            <div key={problemIndex} style={{ marginBottom: 4 }}>
                              <Tag color={problem.severity === 'error' ? 'red' : problem.severity === 'warning' ? 'orange' : 'blue'}>
                                {problem.severity === 'error' ? '错误' : problem.severity === 'warning' ? '警告' : '信息'}
                              </Tag>
                              <Text>{problem.description}</Text>
                            </div>
                          ))}
                        </div>
                        {issue.recommendations.length > 0 && (
                          <div style={{ marginTop: 8 }}>
                            <Text type="secondary">建议：</Text>
                            <ul style={{ margin: '4px 0', paddingLeft: 20 }}>
                              {issue.recommendations.map((rec, recIndex) => (
                                <li key={recIndex}>
                                  <Text type="secondary" style={{ fontSize: 12 }}>{rec}</Text>
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                    </Card>
                  ))}
                </div>
              </>
            )}

            {consistencyReport.invalidWorkstations === 0 && (
              <Alert
                message="数据一致性验证通过"
                description="所有工位的数据都与生产工单保持一致，无需处理。"
                type="success"
                showIcon
              />
            )}
          </div>
        )}
      </Modal>
    </div>
  )
}

export default WorkstationManagementTab
