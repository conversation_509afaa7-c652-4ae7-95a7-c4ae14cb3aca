/**
 * 通用组件CSS Modules样式
 * 为项目中的通用组件提供样式支持
 */

/* 卡片组件样式 */
.card {
  border-radius: 12px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  background: #ffffff;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease-in-out;
}

.cardHover:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.cardShadowSm {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.cardShadowMd {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.cardShadowLg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* 按钮组件样式 */
.button {
  border-radius: 8px;
  transition: all 0.2s ease-in-out;
  font-weight: 500;
}

.buttonPrimary {
  background-color: #0ea5e9;
  border-color: #0ea5e9;
  color: #ffffff;
}

.buttonPrimary:hover {
  background-color: #0284c7;
  border-color: #0284c7;
}

.buttonSecondary {
  background-color: #ffffff;
  border-color: #e5e7eb;
  color: #374151;
}

.buttonSecondary:hover {
  background-color: #f9fafb;
  border-color: #d1d5db;
}

.buttonDanger {
  background-color: #ef4444;
  border-color: #ef4444;
  color: #ffffff;
}

.buttonDanger:hover {
  background-color: #dc2626;
  border-color: #dc2626;
}

/* 表单组件样式 */
.formItem {
  margin-bottom: 16px;
}

.formLabel {
  font-weight: 500;
  color: #374151;
  margin-bottom: 4px;
}

.formInput {
  border-radius: 8px;
  border: 1px solid #d1d5db;
  transition: border-color 0.2s ease;
}

.formInput:focus {
  border-color: #0ea5e9;
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

.formError {
  color: #ef4444;
  font-size: 14px;
  margin-top: 4px;
}

/* 表格组件样式 */
.table {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.tableHeader {
  background-color: #f9fafb;
  font-weight: 600;
  color: #374151;
}

.tableRow:hover {
  background-color: #f9fafb;
}

.tableCell {
  padding: 12px 16px;
  border-bottom: 1px solid #e5e7eb;
}

/* 列表组件样式 */
.list {
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  background: #ffffff;
}

.listItem {
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
  transition: background-color 0.2s ease;
}

.listItem:last-child {
  border-bottom: none;
}

.listItem:hover {
  background-color: #f9fafb;
}

.listItemTitle {
  font-weight: 500;
  color: #111827;
  margin-bottom: 4px;
}

.listItemDescription {
  color: #6b7280;
  font-size: 14px;
}

/* 统计组件样式 */
.statistic {
  text-align: center;
  padding: 24px;
}

.statisticValue {
  font-size: 32px;
  font-weight: bold;
  color: #111827;
  margin-bottom: 8px;
}

.statisticTitle {
  color: #6b7280;
  font-size: 14px;
  margin-bottom: 16px;
}

.statisticTrend {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 14px;
}

.statisticTrendUp {
  color: #10b981;
}

.statisticTrendDown {
  color: #ef4444;
}

/* 状态标签样式 */
.statusTag {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
}

.statusSuccess {
  background-color: #d1fae5;
  color: #065f46;
}

.statusWarning {
  background-color: #fef3c7;
  color: #92400e;
}

.statusError {
  background-color: #fee2e2;
  color: #991b1b;
}

.statusInfo {
  background-color: #dbeafe;
  color: #1e40af;
}

/* 加载状态样式 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 48px;
  color: #6b7280;
}

.loadingSpinner {
  width: 24px;
  height: 24px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #0ea5e9;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态样式 */
.empty {
  text-align: center;
  padding: 48px 24px;
  color: #6b7280;
}

.emptyIcon {
  font-size: 48px;
  color: #d1d5db;
  margin-bottom: 16px;
}

.emptyTitle {
  font-size: 16px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.emptyDescription {
  font-size: 14px;
  color: #6b7280;
}

/* 操作按钮组样式 */
.actionGroup {
  display: flex;
  gap: 8px;
  align-items: center;
}

.actionButton {
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
  background: #ffffff;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
}

.actionButton:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

.actionButtonPrimary {
  background: #0ea5e9;
  border-color: #0ea5e9;
  color: #ffffff;
}

.actionButtonPrimary:hover {
  background: #0284c7;
  border-color: #0284c7;
}

.actionButtonDanger {
  background: #ef4444;
  border-color: #ef4444;
  color: #ffffff;
}

.actionButtonDanger:hover {
  background: #dc2626;
  border-color: #dc2626;
}

/* 响应式工具类 */
.hiddenMobile {
  display: block;
}

.hiddenDesktop {
  display: none;
}

@media (max-width: 768px) {
  .hiddenMobile {
    display: none;
  }
  
  .hiddenDesktop {
    display: block;
  }
  
  .actionGroup {
    flex-direction: column;
    align-items: stretch;
  }
  
  .statistic {
    padding: 16px;
  }
  
  .statisticValue {
    font-size: 24px;
  }
}

/* 过渡动画 */
.fadeIn {
  animation: fadeIn 0.3s ease-in-out;
}

.slideIn {
  animation: slideIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { transform: translateY(-10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}
