'use client'

import React, { useState, useEffect } from 'react'
import { Card, Table, Button, Space, Tag, Input, Select, Modal, Row, Col, Statistic, Alert, Progress, Badge, List, Avatar, Popconfirm, App } from 'antd'
import { DashboardOutlined, UserOutlined, ScanOutlined, PlayCircleOutlined, <PERSON><PERSON>ircleOutlined, ReloadOutlined, TeamOutlined, TrophyOutlined, ClockCircleOutlined, DeleteOutlined, BulbOutlined } from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import {
  HotPressTask,
  EmployeeBinding,
  EquipmentStatus
} from '@/types'
import { HotPressEngine } from './utils/hotPressEngine'
import { styleHelpers } from '@/utils/styles/antdHelpers'

const { Option } = Select
const { Search } = Input

const HotPressBoardPageComponent: React.FC = () => {
  const { message, modal } = App.useApp()
  const [loading, setLoading] = useState(false)
  const [bindingModalVisible, setBindingModalVisible] = useState(false)
  const [selectedTask, setSelectedTask] = useState<HotPressTask | null>(null)
  const [employeeCode, setEmployeeCode] = useState('')
  const [bindingMolds, setBindingMolds] = useState(0)

  // 模拟数据
  const [hotPressTasks, setHotPressTasks] = useState<HotPressTask[]>([
    {
      id: 'task-001',
      taskNumber: 'HP-2024-001',
      productModelCode: 'CP-202',
      productName: '精密机械组件A',
      batchNumber: 'BATCH-001',
      formingCompletionRate: 85.5,
      hotPressQuantity: 1200,
      remainingQuantity: 800,
      deliveryDate: '2024-01-20',

      status: 'waiting',
      assignedEmployees: [],
      createdAt: '2024-01-15T08:00:00Z',
      updatedAt: '2024-01-15T08:00:00Z'
    },
    {
      id: 'task-002',
      taskNumber: 'HP-2024-002',
      productModelCode: 'CP-305',
      productName: '电子控制器B',
      batchNumber: 'BATCH-002',
      formingCompletionRate: 92.0,
      hotPressQuantity: 800,
      remainingQuantity: 600,
      deliveryDate: '2024-01-18',

      status: 'in_progress',
      assignedEmployees: ['EMP001', 'EMP002'],
      startTime: '2024-01-15T09:00:00Z',
      createdAt: '2024-01-15T09:00:00Z',
      updatedAt: '2024-01-15T09:00:00Z'
    }
  ])

  const [employeeBindings, setEmployeeBindings] = useState<EmployeeBinding[]>([
    {
      id: 'binding-001',
      employeeId: 'EMP001',
      employeeName: '王师傅',
      employeeCode: 'W001',
      taskId: 'task-002',
      batchNumber: 'BATCH-002',
      bindingTime: '2024-01-15T09:00:00Z',
      scanMethod: 'qr_code',
      bindingMolds: 400,
      isActive: true,
      createdAt: '2024-01-15T09:00:00Z'
    },
    {
      id: 'binding-002',
      employeeId: 'EMP002',
      employeeName: '李师傅',
      employeeCode: 'L002',
      taskId: 'task-002',
      batchNumber: 'BATCH-002',
      bindingTime: '2024-01-15T10:00:00Z',
      scanMethod: 'qr_code',
      bindingMolds: 400,
      isActive: true,
      createdAt: '2024-01-15T10:00:00Z'
    }
  ])

  const [equipmentStatus] = useState<EquipmentStatus[]>([
    {
      id: 'eq-001',
      equipmentCode: 'HP-001',
      equipmentName: '热压机A',
      equipmentType: 'hot_press_machine',
      status: 'running',
      currentLoad: 80,
      maxCapacity: 1000,
      efficiency: 95,
      lastMaintenanceDate: '2024-01-01',
      nextMaintenanceDate: '2024-02-01',
      operatorId: 'EMP001',
      operatorName: '王师傅'
    },
    {
      id: 'eq-002',
      equipmentCode: 'HP-002',
      equipmentName: '热压机B',
      equipmentType: 'hot_press_machine',
      status: 'running',
      currentLoad: 60,
      maxCapacity: 1200,
      efficiency: 88,
      lastMaintenanceDate: '2024-01-05',
      nextMaintenanceDate: '2024-02-05'
    }
  ])

  // 计算基础统计信息（替代实时产能计算）
  const basicStatistics = HotPressEngine.calculateBasicStatistics(
    hotPressTasks,
    employeeBindings
  )

  // 获取优先任务列表（替代任务建议）
  const priorityTasks = HotPressEngine.getPriorityTasks(hotPressTasks)

  // 处理员工绑定
  const handleEmployeeBinding = async () => {
    if (!selectedTask || !employeeCode) return

    setLoading(true)

    try {
      // 防错检查
      const errorCheck = HotPressEngine.performBindingErrorCheck(
        employeeCode,
        selectedTask.id,
        employeeBindings
      )

      if (errorCheck.hasError) {
        modal.error({
          title: '绑定失败',
          content: errorCheck.errorMessage,
          footer: (
            <div>
              <p>建议：</p>
              <ul>
                {errorCheck.suggestions?.map((suggestion, index) => (
                  <li key={index}>• {suggestion}</li>
                ))}
              </ul>
            </div>
          )
        })
        return
      }

      // 验证绑定
      const validation = HotPressEngine.validateEmployeeBinding(
        employeeCode,
        selectedTask.id,
        employeeBindings
      )

      if (!validation.isValid) {
        modal.error({
          title: '绑定验证失败',
          content: validation.message
        })
        return
      }

      // 创建绑定记录
      const newBinding = HotPressEngine.createEmployeeBinding(
        employeeCode,
        `员工${employeeCode}`, // 实际应该从员工数据库获取
        employeeCode,
        selectedTask.id,
        selectedTask.batchNumber,
        bindingMolds || selectedTask.remainingQuantity
      )

      // 更新状态
      setEmployeeBindings(prev => [...prev, newBinding])

      // 更新任务状态
      setHotPressTasks(prev => prev.map(task => {
        if (task.id === selectedTask.id) {
          const updatedTask = { ...task }
          if (!updatedTask.assignedEmployees.includes(employeeCode)) {
            updatedTask.assignedEmployees.push(employeeCode)
          }
          if (updatedTask.status === 'waiting') {
            updatedTask.status = 'in_progress'
            updatedTask.startTime = new Date().toISOString()
          }
          updatedTask.updatedAt = new Date().toISOString()
          return updatedTask
        }
        return task
      }))

      setBindingModalVisible(false)
      setEmployeeCode('')
      setBindingMolds(0)
      setSelectedTask(null)

    } catch (error) {
      modal.error({
        title: '绑定失败',
        content: '系统错误，请重试'
      })
    } finally {
      setLoading(false)
    }
  }

  // 解除员工绑定
  const handleUnbindEmployee = (bindingId: string) => {
    setEmployeeBindings(prev => prev.map(binding =>
      binding.id === bindingId
        ? { ...binding, isActive: false, unbindingTime: new Date().toISOString() }
        : binding
    ))
  }

  // 扫码绑定（模拟）
  const handleScanBinding = () => {
    // 模拟扫码结果
    const mockEmployeeCodes = ['W001', 'L002', 'Z003', 'S004']
    const randomCode = mockEmployeeCodes[Math.floor(Math.random() * mockEmployeeCodes.length)]
    setEmployeeCode(randomCode)
  }

  // 任务表格列定义
  const taskColumns: ColumnsType<HotPressTask> = [
    {
      title: '任务信息',
      key: 'taskInfo',
      width: 200,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>{record.taskNumber}</div>
          <div style={{ fontSize: '14px', color: '#9ca3af' }}>{record.productName}</div>
          <div style={{ fontSize: '12px', color: '#d1d5db' }}>批次: {record.batchNumber}</div>
        </div>
      )
    },
    {
      title: '成型完成率',
      dataIndex: 'formingCompletionRate',
      key: 'formingCompletionRate',
      width: 120,
      render: (rate) => (
        <div>
          <Progress
            percent={rate}
            size="small"
            format={() => `${rate}%`}
            strokeColor={rate >= 90 ? '#52c41a' : rate >= 70 ? '#faad14' : '#ff4d4f'}
          />
        </div>
      ),
      sorter: (a, b) => b.formingCompletionRate - a.formingCompletionRate
    },
    {
      title: '数量信息',
      key: 'quantity',
      width: 120,
      render: (_, record) => (
        <div style={{ fontSize: '14px' }}>
          <div>已热压: {record.hotPressQuantity}</div>
          <div>剩余: {record.remainingQuantity}</div>
        </div>
      )
    },
    {
      title: '交货日期',
      dataIndex: 'deliveryDate',
      key: 'deliveryDate',
      width: 100,
      render: (date) => {
        const deliveryDate = new Date(date)
        const today = new Date()
        const daysLeft = Math.ceil((deliveryDate.getTime() - today.getTime()) / (1000 * 3600 * 24))

        return (
          <div>
            <div>{date}</div>
            <div style={{
              fontSize: '12px',
              color: daysLeft <= 1 ? '#ff4d4f' :
                     daysLeft <= 3 ? '#fa8c16' :
                     '#9ca3af'
            }}>
              {daysLeft <= 0 ? '已逾期' : `${daysLeft}天后`}
            </div>
          </div>
        )
      },
      sorter: (a, b) => new Date(a.deliveryDate).getTime() - new Date(b.deliveryDate).getTime()
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => {
        const statusMap = {
          waiting: { color: 'default', text: '等待中', icon: <ClockCircleOutlined /> },
          in_progress: { color: 'blue', text: '进行中', icon: <PlayCircleOutlined /> },
          completed: { color: 'green', text: '已完成', icon: <CheckCircleOutlined /> }
        }
        const statusInfo = statusMap[status as keyof typeof statusMap]
        return (
          <Tag color={statusInfo.color} icon={statusInfo.icon}>
            {statusInfo.text}
          </Tag>
        )
      }
    },
    {
      title: '绑定员工',
      dataIndex: 'assignedEmployees',
      key: 'assignedEmployees',
      width: 120,
      render: (employees) => (
        <div>
          <Badge count={employees.length} showZero>
            <TeamOutlined style={{ fontSize: '18px' }} />
          </Badge>
          <div style={{ fontSize: '12px', color: '#9ca3af', marginTop: '4px' }}>
            {employees.length}/3 人
          </div>
        </div>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="primary"
            size="small"
            icon={<UserOutlined />}
            onClick={() => {
              setSelectedTask(record)
              setBindingModalVisible(true)
            }}
            disabled={record.assignedEmployees.length >= 3}
          >
            绑定
          </Button>
        </Space>
      ),
    },
  ]

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: styleHelpers.spacing.lg }}>
      {/* 页面标题 */}
      <div className="page-header">
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <DashboardOutlined style={{ fontSize: '24px', color: '#fa8c16', marginRight: '12px' }} />
            <div>
              <h1 className="page-title">热压机生产看板</h1>
              <p className="page-description">热压机生产任务看板和员工绑定系统</p>
            </div>
          </div>
          <Button icon={<ReloadOutlined />} onClick={() => window.location.reload()}>
            刷新看板
          </Button>
        </div>
      </div>

      {/* 实时产能看板 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="任务完成率"
              value={basicStatistics.completionRate}
              suffix="%"
              valueStyle={{ color: '#52c41a' }}
              prefix={<TrophyOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="待处理任务"
              value={basicStatistics.totalTasks - basicStatistics.completedTasks}
              suffix="个"
              valueStyle={{ color: '#1890ff' }}
              prefix={<ClockCircleOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="已完成任务"
              value={basicStatistics.completedTasks}
              suffix="个"
              valueStyle={{ color: '#52c41a' }}
              prefix={<DashboardOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="活跃员工"
              value={basicStatistics.activeEmployees}
              suffix="人"
              valueStyle={{ color: '#722ed1' }}
              prefix={<TeamOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* 优先任务提示 */}
      {priorityTasks.length > 0 && (
        <Row gutter={[16, 16]}>
          <Col xs={24}>
            <Alert
              message="优先任务提醒"
              description={
                <div>
                  <p>当前有 {priorityTasks.length} 个优先任务需要处理：</p>
                  <ul className="mb-0">
                    {priorityTasks.slice(0, 3).map((task, index) => (
                      <li key={index}>• {task.batchNumber} - {task.productName} (交货日期: {new Date(task.deliveryDate).toLocaleDateString()})</li>
                    ))}
                    {priorityTasks.length > 3 && <li>• 还有 {priorityTasks.length - 3} 个任务...</li>}
                  </ul>
                </div>
              }
              type="info"
              showIcon
              icon={<BulbOutlined />}
            />
          </Col>
        </Row>
      )}

      {/* 设备状态 */}
      <Card title="设备状态">
        <Row gutter={[16, 16]}>
          {equipmentStatus.map(equipment => (
            <Col xs={24} sm={12} lg={8} key={equipment.id}>
              <Card size="small" style={{ borderLeft: '4px solid #1890ff' }}>
                <div style={{ display: 'flex', flexDirection: 'column', gap: styleHelpers.spacing.sm }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <h4 style={{ fontWeight: 500 }}>{equipment.equipmentName}</h4>
                    <Tag color={
                      equipment.status === 'running' ? 'green' :
                      equipment.status === 'idle' ? 'default' :
                      equipment.status === 'maintenance' ? 'orange' : 'red'
                    }>
                      {equipment.status === 'running' ? '运行中' :
                       equipment.status === 'idle' ? '空闲' :
                       equipment.status === 'maintenance' ? '维护中' : '故障'}
                    </Tag>
                  </div>
                  <div style={{ fontSize: '14px', color: '#6b7280' }}>
                    <div>负荷: {equipment.currentLoad}%</div>
                    <div>效率: {equipment.efficiency}%</div>
                    <div>产能: {equipment.maxCapacity} 模/天</div>
                  </div>
                  {equipment.operatorName && (
                    <div style={{ fontSize: '12px', color: '#9ca3af' }}>
                      操作员: {equipment.operatorName}
                    </div>
                  )}
                </div>
              </Card>
            </Col>
          ))}
        </Row>
      </Card>

      {/* 优先任务列表 */}
      <Card
        title="生产任务看板"
        extra={
          <div style={{ fontSize: '14px', color: '#9ca3af' }}>
            按交货日期排序 | 自动更新
          </div>
        }
      >
        <Table
          columns={taskColumns}
          dataSource={HotPressEngine.sortTasksByPriority(hotPressTasks)}
          rowKey="id"
          pagination={{ pageSize: 10 }}
          scroll={{ x: 1000 }}
          rowClassName={(record) => {
            const today = new Date()
            const deliveryDate = new Date(record.deliveryDate)
            const daysToDelivery = Math.ceil((deliveryDate.getTime() - today.getTime()) / (1000 * 3600 * 24))

            if (daysToDelivery <= 3) return 'urgent-row'
            if (daysToDelivery <= 7) return 'warning-row'
            return ''
          }}
        />
      </Card>

      {/* 员工绑定记录 */}
      <Card title="员工绑定记录">
        <List
          dataSource={employeeBindings.filter(binding => binding.isActive)}
          renderItem={(binding) => {
            const task = hotPressTasks.find(t => t.id === binding.taskId)
            return (
              <List.Item
                actions={[
                  <Popconfirm
                    key="unbind"
                    title="确认解除绑定？"
                    onConfirm={() => handleUnbindEmployee(binding.id)}
                    okText="确认"
                    cancelText="取消"
                  >
                    <Button
                      type="text"
                      danger
                      size="small"
                      icon={<DeleteOutlined />}
                    >
                      解绑
                    </Button>
                  </Popconfirm>
                ]}
              >
                <List.Item.Meta
                  avatar={<Avatar icon={<UserOutlined />} />}
                  title={
                    <div style={{ display: 'flex', alignItems: 'center', gap: styleHelpers.spacing.sm }}>
                      <span>{binding.employeeName}</span>
                      <Tag>{binding.employeeCode}</Tag>
                      <Badge
                        status={binding.scanMethod === 'qr_code' ? 'success' : 'default'}
                        text={binding.scanMethod === 'qr_code' ? '扫码绑定' : '手动绑定'}
                      />
                    </div>
                  }
                  description={
                    <div style={{ fontSize: '14px' }}>
                      <div>任务: {task?.taskNumber} | 批次: {binding.batchNumber}</div>
                      <div>绑定模数: {binding.bindingMolds} | 时间: {new Date(binding.bindingTime).toLocaleString()}</div>
                    </div>
                  }
                />
              </List.Item>
            )
          }}
        />
      </Card>

      {/* 员工绑定模态框 */}
      <Modal
        title="员工绑定"
        open={bindingModalVisible}
        onCancel={() => setBindingModalVisible(false)}
        onOk={handleEmployeeBinding}
        confirmLoading={loading}
        width={600}
      >
        {selectedTask && (
          <div style={{ display: 'flex', flexDirection: 'column', gap: styleHelpers.spacing.md }}>
            <Alert
              message="员工绑定确认"
              description="请扫描员工工牌或手动输入工牌号进行绑定"
              type="info"
              showIcon
            />

            <div style={{ backgroundColor: '#f9fafb', padding: styleHelpers.spacing.md, borderRadius: '6px' }}>
              <h4 style={{ fontWeight: 500, marginBottom: styleHelpers.spacing.sm }}>任务信息</h4>
              <Row gutter={16}>
                <Col span={12}>
                  <p><strong>任务号:</strong> {selectedTask.taskNumber}</p>
                  <p><strong>产品:</strong> {selectedTask.productName}</p>
                  <p><strong>批次:</strong> {selectedTask.batchNumber}</p>
                </Col>
                <Col span={12}>
                  <p><strong>剩余数量:</strong> {selectedTask.remainingQuantity} 模</p>
                  <p><strong>交货日期:</strong> {selectedTask.deliveryDate}</p>
                  <p><strong>已绑定员工:</strong> {selectedTask.assignedEmployees.length}/3 人</p>
                </Col>
              </Row>
            </div>

            <div>
              <h4 style={{ fontWeight: 500, marginBottom: styleHelpers.spacing.sm }}>员工工牌</h4>
              <div style={{ display: 'flex', gap: styleHelpers.spacing.sm }}>
                <Input
                  value={employeeCode}
                  onChange={(e) => setEmployeeCode(e.target.value)}
                  placeholder="请输入或扫描员工工牌号"
                  style={{ flex: 1 }}
                />
                <Button
                  icon={<ScanOutlined />}
                  onClick={handleScanBinding}
                  type="primary"
                >
                  扫码
                </Button>
              </div>
            </div>

            <div>
              <h4 style={{ fontWeight: 500, marginBottom: styleHelpers.spacing.sm }}>绑定模数</h4>
              <Input
                type="number"
                value={bindingMolds}
                onChange={(e) => setBindingMolds(Number(e.target.value))}
                placeholder="请输入绑定模数（可选，默认为剩余数量）"
                addonAfter="模"
              />
              <div style={{ marginTop: '4px', fontSize: '14px', color: '#9ca3af' }}>
                留空将自动设置为剩余数量 ({selectedTask.remainingQuantity} 模)
              </div>
            </div>

            {/* 绑定预览 */}
            {employeeCode && (
              <div style={{ backgroundColor: '#e6f7ff', padding: '12px', borderRadius: '6px' }}>
                <h4 style={{ fontWeight: 500, marginBottom: styleHelpers.spacing.sm, color: '#1890ff' }}>绑定预览</h4>
                <div style={{ fontSize: '14px', color: '#1890ff' }}>
                  <p>员工工牌: {employeeCode}</p>
                  <p>绑定模数: {bindingMolds || selectedTask.remainingQuantity} 模</p>
                  <p>绑定时间: {new Date().toLocaleString()}</p>
                </div>
              </div>
            )}

            {/* 当前绑定员工列表 */}
            {selectedTask.assignedEmployees.length > 0 && (
              <div>
                <h4 style={{ fontWeight: 500, marginBottom: styleHelpers.spacing.sm }}>当前绑定员工</h4>
                <div style={{ display: 'flex', flexDirection: 'column', gap: styleHelpers.spacing.sm }}>
                  {employeeBindings
                    .filter(binding => binding.taskId === selectedTask.id && binding.isActive)
                    .map(binding => (
                      <div key={binding.id} style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: styleHelpers.spacing.sm, backgroundColor: '#f9fafb', borderRadius: '6px' }}>
                        <div>
                          <span style={{ fontWeight: 500 }}>{binding.employeeName}</span>
                          <span style={{ marginLeft: styleHelpers.spacing.sm, fontSize: '14px', color: '#9ca3af' }}>({binding.employeeCode})</span>
                        </div>
                        <div style={{ fontSize: '14px', color: '#6b7280' }}>
                          {binding.bindingMolds} 模
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  )
}

// 用App组件包裹以提供message和modal等上下文
const HotPressBoardPage: React.FC = () => {
  return (
    <App>
      <HotPressBoardPageComponent />
    </App>
  )
}

export default HotPressBoardPage
