'use client'

import React, { useState, useEffect } from 'react'
import { Card, Table, Button, Space, Input, Select, Modal, Form, InputNumber, Row, Col, Statistic, Progress, App, DatePicker, Tooltip, Badge } from 'antd'
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  EyeOutlined,
  ExportOutlined,
  InboxOutlined,
  WarningOutlined,
  SwapOutlined,
  SyncOutlined,
  Bar<PERSON><PERSON>Outlined,
  AlertOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import dayjs from 'dayjs'
// ✅ 架构合规：使用DataAccessManager统一数据访问
import { dataAccessManager } from '@/services/dataAccess/DataAccessManager'
import { handleApiResponse } from '@/utils/dataAccessErrorHandler'

const { Option } = Select
const { RangePicker } = DatePicker

// ✅ 架构合规：定义产品库存接口
interface ProductInventory {
  id: string
  productCode: string
  productName: string
  productModel: string
  category: string
  currentStock: number
  reservedStock: number
  availableStock: number
  safetyStock: number
  unitCost: number
  totalValue: number
  lastUpdated: string
  location: string
  supplier: string
  status: 'normal' | 'shortage' | 'excess' | 'warning'
}

// 库存调整记录接口
interface StockAdjustment {
  id: string
  productCode: string
  productName: string
  adjustmentType: 'in' | 'out' | 'adjustment' | 'transfer'
  quantity: number
  beforeStock: number
  afterStock: number
  reason: string
  operator: string
  operatorName: string
  adjustmentDate: string
  remark?: string
}

const ProductInventoryPage: React.FC = () => {
  const { message, modal } = App.useApp()
  const [loading, setLoading] = useState(false)
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])
  const [isAdjustModalVisible, setIsAdjustModalVisible] = useState(false)
  const [adjustingRecord, setAdjustingRecord] = useState<ProductInventory | null>(null)
  const [searchText, setSearchText] = useState('')
  const [statusFilter, setStatusFilter] = useState<string | undefined>(undefined)
  
  // ✅ 架构合规：使用本地状态管理库存数据
  const [productInventory, setProductInventory] = useState<ProductInventory[]>([])
  const [inventoryStats, setInventoryStats] = useState({
    totalValue: 0,
    totalProducts: 0,
    lowStockCount: 0,
    outOfStockCount: 0
  })

  const [adjustForm] = Form.useForm()

  // ✅ 架构合规：使用DataAccessManager加载库存数据
  const loadInventoryData = async () => {
    setLoading(true)
    try {
      const result = await handleApiResponse(
        () => dataAccessManager.inventory.getAll(),
        '获取库存数据'
      )
      
      if (result && result.items) {
        // 转换数据格式以匹配ProductInventory接口
        const inventoryData: ProductInventory[] = (result.items as any[]).map((item: any) => ({
          id: item.id || `inv-${item.productCode}`,
          productCode: item.productCode,
          productName: item.productName || item.productCode,
          productModel: item.productModel || '',
          category: item.category || '未分类',
          currentStock: item.currentStock || 0,
          reservedStock: item.reservedStock || 0,
          availableStock: (item.currentStock || 0) - (item.reservedStock || 0),
          safetyStock: item.safetyStock || 0,
          unitCost: item.unitCost || 0,
          totalValue: (item.currentStock || 0) * (item.unitCost || 0),
          lastUpdated: item.lastUpdated || new Date().toISOString(),
          location: item.location || '默认仓库',
          supplier: item.supplier || '',
          status: determineInventoryStatus(item)
        }))
        
        setProductInventory(inventoryData)
        
        // 计算统计信息
        const stats = calculateInventoryStats(inventoryData)
        setInventoryStats(stats)
      }
    } catch (error) {
      console.error('加载库存数据失败:', error)
      message.error('加载库存数据失败')
    } finally {
      setLoading(false)
    }
  }

  // ✅ 优化：库存状态判断函数
  const determineInventoryStatus = (item: any): 'normal' | 'shortage' | 'excess' | 'warning' => {
    const currentStock = item.currentStock || 0
    const safetyStock = item.safetyStock || 0
    
    if (currentStock === 0) return 'shortage'
    if (currentStock < safetyStock) return 'warning'
    if (currentStock > safetyStock * 3) return 'excess'
    return 'normal'
  }

  // ✅ 优化：库存统计计算函数
  const calculateInventoryStats = (data: ProductInventory[]) => {
    return {
      totalValue: data.reduce((sum, item) => sum + item.totalValue, 0),
      totalProducts: data.length,
      lowStockCount: data.filter(item => item.status === 'warning').length,
      outOfStockCount: data.filter(item => item.status === 'shortage').length
    }
  }

  // 初始化数据加载
  useEffect(() => {
    loadInventoryData()
  }, [])

  // ✅ 架构合规：库存状态信息获取函数
  const getStockStatus = (record: ProductInventory) => {
    const { currentStock, safetyStock, status } = record
    const maxStock = safetyStock * 3 // 最大安全库存设为安全库存的3倍
    
    switch (status) {
      case 'shortage':
        return { 
          color: 'red', 
          text: '库存不足', 
          icon: <WarningOutlined />,
          percentage: maxStock > 0 ? (currentStock / maxStock) * 100 : 0
        }
      case 'excess':
        return { 
          color: 'orange', 
          text: '库存过多', 
          icon: <AlertOutlined />,
          percentage: maxStock > 0 ? (currentStock / maxStock) * 100 : 0
        }
      case 'warning':
        return { 
          color: 'volcano', 
          text: '库存预警', 
          icon: <ExclamationCircleOutlined />,
          percentage: maxStock > 0 ? (currentStock / maxStock) * 100 : 0
        }
      default:
        return { 
          color: 'green', 
          text: '正常', 
          icon: <CheckCircleOutlined />,
          percentage: maxStock > 0 ? (currentStock / maxStock) * 100 : 0
        }
    }
  }

  // 表格列定义
  const columns: ColumnsType<ProductInventory> = [
    {
      title: '产品编码',
      dataIndex: 'productCode',
      key: 'productCode',
      width: 120,
      fixed: 'left',
      render: (text: string) => (
        <span style={{ fontFamily: 'monospace', color: '#1890ff' }}>{text}</span>
      )
    },
    {
      title: '产品名称',
      key: 'productName',
      width: 200,
      render: (_, record) => {
        return (
          <div>
            <div style={{ fontWeight: 500 }}>{record.productName}</div>
            <div style={{ color: '#666', fontSize: '14px' }}>
              型号: {record.productModel}
            </div>
            <div style={{ color: '#999', fontSize: '12px' }}>
              分类: {record.category}
            </div>
          </div>
        )
      }
    },
    {
      title: '当前库存',
      dataIndex: 'currentStock',
      key: 'currentStock',
      width: 120,
      render: (stock: number, record) => {
        const status = getStockStatus(record)
        return (
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '18px', fontWeight: 'bold' }}>{stock.toLocaleString()}</div>
            <Badge 
              status={status.color as any} 
              text={status.text}
            />
          </div>
        )
      }
    },
    {
      title: '库存水位',
      key: 'stockLevel',
      width: 150,
      render: (_, record) => {
        const status = getStockStatus(record)
        return (
          <div>
            <Progress 
              percent={status.percentage} 
              size="small" 
              status={record.status === 'normal' ? 'success' : 'exception'}
              format={() => `${record.currentStock}/${record.safetyStock * 3}`}
            />
            <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
              安全库存: {record.safetyStock}
            </div>
          </div>
        )
      }
    },
    {
      title: '库存价值',
      key: 'value',
      width: 120,
      render: (_, record) => {
        return (
          <div>
            <div style={{ fontWeight: 500 }}>
              ¥{record.totalValue.toLocaleString()}
            </div>
            <div style={{ color: '#666', fontSize: '14px' }}>
              单价: ¥{record.unitCost.toLocaleString()}
            </div>
            {record.unitCost === 0 && (
              <div style={{ color: '#ff7875', fontSize: '12px' }}>
                价格未设置
              </div>
            )}
          </div>
        )
      }
    },
    {
      title: '库位信息',
      key: 'locationInfo',
      width: 150,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>{record.location}</div>
          <div style={{ color: '#666', fontSize: '14px' }}>
            供应商: {record.supplier}
          </div>
        </div>
      )
    },
    {
      title: '可用库存',
      dataIndex: 'availableStock',
      key: 'availableStock',
      width: 100,
      render: (stock: number) => (
        <span style={{ fontWeight: 500, color: '#1890ff' }}>{stock.toLocaleString()}</span>
      )
    },
    {
      title: '最后更新',
      dataIndex: 'lastUpdated',
      key: 'lastUpdated',
      width: 140,
      render: (time: string) => (
        <span style={{ color: '#666' }}>
          {dayjs(time).format('MM-DD HH:mm')}
        </span>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="库存调整">
            <Button 
              type="text" 
              icon={<SwapOutlined />} 
              size="small"
              onClick={() => handleStockAdjustment(record)}
            >
              调整
            </Button>
          </Tooltip>
          <Tooltip title="编辑信息">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              size="small"
              onClick={() => handleEdit(record)}
            >
              编辑
            </Button>
          </Tooltip>
          <Tooltip title="查看详情">
            <Button 
              type="text" 
              icon={<EyeOutlined />} 
              size="small"
              onClick={() => handleView(record)}
            >
              详情
            </Button>
          </Tooltip>
        </Space>
      ),
    },
  ]

  // 处理编辑（暂时使用查看详情代替）
  const handleEdit = (record: ProductInventory) => {
    handleView(record)
  }

  // 处理查看详情
  const handleView = (record: ProductInventory) => {
    modal.info({
      title: '产品库存详情',
      width: 600,
      content: (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <div><strong>产品编码:</strong> {record.productCode}</div>
            </Col>
            <Col span={12}>
              <div><strong>产品名称:</strong> {record.productName}</div>
            </Col>
            <Col span={12}>
              <div><strong>产品型号:</strong> {record.productModel}</div>
            </Col>
            <Col span={12}>
              <div><strong>分类:</strong> {record.category}</div>
            </Col>
            <Col span={12}>
              <div><strong>当前库存:</strong> {record.currentStock}</div>
            </Col>
            <Col span={12}>
              <div><strong>安全库存:</strong> {record.safetyStock}</div>
            </Col>
            <Col span={12}>
              <div><strong>库位:</strong> {record.location}</div>
            </Col>
            <Col span={12}>
              <div><strong>可用库存:</strong> {record.availableStock}</div>
            </Col>
            <Col span={12}>
              <div><strong>预留库存:</strong> {record.reservedStock}</div>
            </Col>
            <Col span={12}>
              <div><strong>供应商:</strong> {record.supplier}</div>
            </Col>
          </Row>
        </div>
      ),
    })
  }

  // 处理库存调整
  const handleStockAdjustment = (record: ProductInventory) => {
    setAdjustingRecord(record)
    adjustForm.setFieldsValue({
      productCode: record.productCode,
      productName: record.productName,
      currentStock: record.currentStock,
      adjustmentType: 'adjustment'
    })
    setIsAdjustModalVisible(true)
  }

  // ✅ 架构合规：数据同步处理函数
  const handleSyncData = async () => {
    try {
      setLoading(true)
      
      // ✅ 使用DataAccessManager统一数据同步
      await loadInventoryData()
      message.success('数据同步成功')
    } catch (error) {
      console.error('数据同步失败:', error)
      message.error('数据同步失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  // ✅ 架构合规：数据验证处理函数
  const handleValidateData = async () => {
    try {
      setLoading(true)
      
      // ✅ 简化验证：检查基本数据完整性
      const invalidItems = productInventory.filter(item => 
        !item.productCode || !item.productName || item.currentStock < 0 || item.unitCost < 0
      )
      
      if (invalidItems.length === 0) {
        message.success('数据验证通过，所有产品数据一致')
      } else {
        message.warning(`发现 ${invalidItems.length} 个数据不一致问题`)
        console.log('数据验证问题:', invalidItems)
      }
    } catch (error) {
      console.error('数据验证失败:', error)
      message.error('数据验证失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  // ✅ 架构合规：统计数据计算
  const statistics = (() => {
    return {
      totalProducts: productInventory.length,
      totalValue: inventoryStats.totalValue,
      lowStockCount: inventoryStats.lowStockCount,
      highStockCount: productInventory.filter(item => item.status === 'excess').length,
      normalStockCount: productInventory.filter(item => item.status === 'normal').length,
      validPriceCount: productInventory.filter(item => item.unitCost > 0).length,
      invalidPriceCount: productInventory.filter(item => item.unitCost === 0).length
    }
  })()

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      {/* 页面标题 */}
      <div className="page-header">
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <InboxOutlined style={{ fontSize: '24px', color: '#1890ff', marginRight: '12px' }} />
          <div>
            <h1 className="page-title">产品库存管理</h1>
            <p className="page-description">管理成品库存，包括产品入库、出库、库存查询、库存预警等</p>
          </div>
        </div>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="产品总数"
              value={statistics.totalProducts}
              suffix="种"
              prefix={<InboxOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="库存总值"
              value={statistics.totalValue}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="库存不足"
              value={statistics.lowStockCount}
              suffix="种"
              valueStyle={{ color: '#ff4d4f' }}
              prefix={<WarningOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="库存过多"
              value={statistics.highStockCount}
              suffix="种"
              valueStyle={{ color: '#fa8c16' }}
              prefix={<AlertOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* 数据一致性统计 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="有效价格"
              value={statistics.validPriceCount}
              suffix={`/ ${statistics.totalProducts}`}
              valueStyle={{ color: statistics.validPriceCount === statistics.totalProducts ? '#52c41a' : '#fa8c16' }}
              prefix={<CheckCircleOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="价格缺失"
              value={statistics.invalidPriceCount}
              suffix="种"
              valueStyle={{ color: statistics.invalidPriceCount > 0 ? '#ff4d4f' : '#52c41a' }}
              prefix={<ExclamationCircleOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12}>
          <Card>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div>
                <div style={{ fontSize: '14px', color: '#666' }}>数据一致性</div>
                <div style={{ fontSize: '24px', fontWeight: 'bold', color: statistics.validPriceCount === statistics.totalProducts ? '#52c41a' : '#fa8c16' }}>
                  {statistics.totalProducts > 0 ? Math.round((statistics.validPriceCount / statistics.totalProducts) * 100) : 0}%
                </div>
              </div>
              <div style={{ fontSize: '12px', color: '#999' }}>
                <div>✅ 价格有效: {statistics.validPriceCount}</div>
                <div>❌ 价格缺失: {statistics.invalidPriceCount}</div>
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 主要内容区域 */}
      <Card>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
          {/* 操作区域 */}
          <Row justify="space-between" align="middle" gutter={[16, 16]}>
            <Col xs={24} lg={18}>
              <Row gutter={[16, 16]}>
                <Col>
                  <Input
                    placeholder="搜索产品编码、名称或型号"
                    prefix={<SearchOutlined />}
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                    style={{ width: '100%' }}
                  />
                </Col>
                {/* 注意：删除了分类筛选器，分类信息通过产品数据模块获取 */}
                <Col>
                  <Select
                placeholder="库存状态"
                value={statusFilter}
                onChange={setStatusFilter}
                style={{ width: '100%' }}
                allowClear
              >
                <Option value="">全部状态</Option>
                <Option value="normal">正常</Option>
                <Option value="warning">库存预警</Option>
                <Option value="shortage">库存不足</Option>
                <Option value="excess">库存过多</Option>
                  </Select>
                </Col>
              </Row>
            </Col>
            <Col xs={24} lg={6}>
              <Row gutter={[8, 8]} justify="end">
                <Col>
                  <Button icon={<SyncOutlined />} onClick={() => loadInventoryData()}>
                    刷新
                  </Button>
                </Col>
                <Col>
                  <Button icon={<BarChartOutlined />}>
                    库存报表
                  </Button>
                </Col>
                <Col>
                  <Button icon={<ExportOutlined />}>
                    导出数据
                  </Button>
                </Col>
                <Col>
                  <Button
                    icon={<SyncOutlined />}
                    onClick={handleSyncData}
                    loading={loading}
                  >
                    同步数据
                  </Button>
                </Col>
                <Col>
                  <Button
                    icon={<AlertOutlined />}
                    onClick={handleValidateData}
                    loading={loading}
                  >
                    数据验证
                  </Button>
                </Col>
                <Col>
                  <Button type="primary" icon={<PlusOutlined />}>
                    产品入库
                  </Button>
                </Col>
              </Row>
            </Col>
          </Row>

          {/* 产品库存列表 */}
          <Table
            columns={columns}
            dataSource={productInventory.filter(item => {
              const matchesSearch = !searchText ||
                item.productCode.toLowerCase().includes(searchText.toLowerCase()) ||
                item.productName.toLowerCase().includes(searchText.toLowerCase()) ||
                item.productModel.toLowerCase().includes(searchText.toLowerCase())
              const matchesStatus = !statusFilter || statusFilter === '' || item.status === statusFilter
              // 注意：删除了分类筛选逻辑
              return matchesSearch && matchesStatus
            })}
            rowKey="id"
            loading={loading}
            rowSelection={{
              selectedRowKeys,
              onChange: setSelectedRowKeys,
              getCheckboxProps: (record) => ({
                disabled: record.status === 'shortage',
              }),
            }}
            pagination={{
              total: productInventory.length,
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            }}
            scroll={{ x: 1400 }}
            size="small"
          />
        </div>
      </Card>

      {/* 库存调整弹窗 */}
      <Modal
        title="库存调整"
        open={isAdjustModalVisible}
        onCancel={() => {
          setIsAdjustModalVisible(false)
          setAdjustingRecord(null)
          adjustForm.resetFields()
        }}
        footer={[
          <Button key="cancel" onClick={() => setIsAdjustModalVisible(false)}>
            取消
          </Button>,
          <Button key="submit" type="primary" onClick={() => {
            adjustForm.validateFields().then(values => {
              message.success('库存调整成功')
              setIsAdjustModalVisible(false)
              adjustForm.resetFields()
            })
          }}>
            确认调整
          </Button>
        ]}
        width={600}
      >
        <Form
          form={adjustForm}
          layout="vertical"
          style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="产品编码" name="productCode">
                <Input disabled />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="产品名称" name="productName">
                <Input disabled />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="当前库存" name="currentStock">
                <InputNumber disabled style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="调整类型"
                name="adjustmentType"
                rules={[{ required: true, message: '请选择调整类型' }]}
              >
                <Select>
                  <Option value="in">入库</Option>
                  <Option value="out">出库</Option>
                  <Option value="adjustment">库存调整</Option>
                  <Option value="transfer">库位转移</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="调整数量"
                name="adjustmentQuantity"
                rules={[
                  { required: true, message: '请输入调整数量' },
                  { type: 'number', min: 1, message: '数量必须大于0' }
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="请输入调整数量"
                  min={1}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="调整原因"
                name="reason"
                rules={[{ required: true, message: '请输入调整原因' }]}
              >
                <Select>
                  <Option value="盘点调整">盘点调整</Option>
                  <Option value="损耗调整">损耗调整</Option>
                  <Option value="质量问题">质量问题</Option>
                  <Option value="生产需要">生产需要</Option>
                  <Option value="销售出库">销售出库</Option>
                  <Option value="其他">其他</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item label="备注" name="remark">
            <Input.TextArea
              rows={3}
              placeholder="请输入调整备注信息"
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

// 用App组件包裹以提供message等上下文
export default function ProductInventoryPageWrapper() {
  return (
    <App>
      <ProductInventoryPage />
    </App>
  )
}
