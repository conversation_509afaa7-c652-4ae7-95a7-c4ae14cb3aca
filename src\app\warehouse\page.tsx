'use client'

import React, { useState } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Input,
  Select,
  Form,
  Row,
  Col,
  Statistic,
  Progress,
  Tabs
} from 'antd'
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  ExportOutlined,
  InboxOutlined,
  WarningOutlined,
  SwapOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import { InventoryItem, StockMovement } from '@/types'
import { styleHelpers } from '@/utils/styles/antdHelpers'

const { Option } = Select

const WarehouseManagement: React.FC = () => {
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [activeTab, setActiveTab] = useState('inventory')
  const [form] = Form.useForm()

  // 模拟库存数据
  const inventoryData: InventoryItem[] = [
    {
      id: '1',
      productCode: 'P001',
      productName: '高强度钢材',
      category: '原材料',
      currentStock: 1250,
      minStock: 500,
      maxStock: 2000,
      unitPrice: 45.50,
      location: 'A区-01',
      lastUpdated: '2024-01-15'
    },
    {
      id: '2',
      productCode: 'P002',
      productName: '精密轴承',
      category: '零部件',
      currentStock: 320,
      minStock: 200,
      maxStock: 800,
      unitPrice: 125.00,
      location: 'B区-05',
      lastUpdated: '2024-01-14'
    },
    {
      id: '3',
      productCode: 'P003',
      productName: '电机组件',
      category: '成品',
      currentStock: 85,
      minStock: 100,
      maxStock: 300,
      unitPrice: 850.00,
      location: 'C区-12',
      lastUpdated: '2024-01-13'
    }
  ]

  // 模拟库存变动数据
  const movementData: StockMovement[] = [
    {
      id: '1',
      productCode: 'P001',
      productName: '高强度钢材',
      type: 'in',
      quantity: 500,
      toLocation: 'A区-01',
      date: '2024-01-15',
      operator: '张三',
      reason: '采购入库'
    },
    {
      id: '2',
      productCode: 'P002',
      productName: '精密轴承',
      type: 'out',
      quantity: 50,
      fromLocation: 'B区-05',
      date: '2024-01-14',
      operator: '李四',
      reason: '生产领料'
    }
  ]

  const getStockStatus = (current: number, min: number, max: number) => {
    if (current < min) {
      return { status: 'exception', text: '库存不足', color: 'red' }
    } else if (current > max) {
      return { status: 'active', text: '库存过多', color: 'orange' }
    } else {
      return { status: 'success', text: '正常', color: 'green' }
    }
  }

  const inventoryColumns: ColumnsType<InventoryItem> = [
    {
      title: '产品编码',
      dataIndex: 'productCode',
      key: 'productCode',
      width: 100,
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      key: 'productName',
      width: 150,
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      width: 100,
    },
    {
      title: '当前库存',
      dataIndex: 'currentStock',
      key: 'currentStock',
      width: 100,
      render: (stock: number, record) => {
        const status = getStockStatus(record.currentStock, record.minStock, record.maxStock)
        return (
          <div>
            <div>{stock}</div>
            <Tag color={status.color}>{status.text}</Tag>
          </div>
        )
      }
    },
    {
      title: '库存水位',
      key: 'stockLevel',
      width: 150,
      render: (_, record) => {
        const percentage = (record.currentStock / record.maxStock) * 100
        const status = getStockStatus(record.currentStock, record.minStock, record.maxStock)
        return (
          <Progress 
            percent={percentage} 
            size="small" 
            status={status.status as any}
            format={() => `${record.currentStock}/${record.maxStock}`}
          />
        )
      }
    },
    {
      title: '单价',
      dataIndex: 'unitPrice',
      key: 'unitPrice',
      width: 100,
      render: (price: number) => `¥${price.toFixed(2)}`
    },
    {
      title: '库位',
      dataIndex: 'location',
      key: 'location',
      width: 100,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Button type="text" icon={<SwapOutlined />} size="small">
            调拨
          </Button>
          <Button type="text" icon={<EditOutlined />} size="small">
            编辑
          </Button>
        </Space>
      ),
    },
  ]

  const movementColumns: ColumnsType<StockMovement> = [
    {
      title: '产品编码',
      dataIndex: 'productCode',
      key: 'productCode',
      width: 100,
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      key: 'productName',
      width: 150,
    },
    {
      title: '变动类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type: string) => {
        const typeMap = {
          in: { color: 'green', text: '入库' },
          out: { color: 'red', text: '出库' },
          transfer: { color: 'blue', text: '调拨' }
        }
        const typeInfo = typeMap[type as keyof typeof typeMap]
        return <Tag color={typeInfo.color}>{typeInfo.text}</Tag>
      }
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 80,
    },
    {
      title: '库位',
      key: 'location',
      width: 120,
      render: (_, record) => {
        if (record.type === 'in') return record.toLocation
        if (record.type === 'out') return record.fromLocation
        return `${record.fromLocation} → ${record.toLocation}`
      }
    },
    {
      title: '操作人',
      dataIndex: 'operator',
      key: 'operator',
      width: 100,
    },
    {
      title: '日期',
      dataIndex: 'date',
      key: 'date',
      width: 100,
    },
    {
      title: '原因',
      dataIndex: 'reason',
      key: 'reason',
      width: 120,
    }
  ]

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: styleHelpers.spacing.lg }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: styleHelpers.spacing.md }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <InboxOutlined style={{
            fontSize: '24px',
            color: '#10b981',
            marginRight: styleHelpers.spacing.sm
          }} />
          <div>
            <h1 style={{
              fontSize: '24px',
              fontWeight: 'bold',
              color: styleHelpers.colors.gray[900],
              margin: 0,
              marginBottom: styleHelpers.spacing.xs
            }}>仓库管理</h1>
            <p style={{
              color: styleHelpers.colors.gray[600],
              margin: 0,
              fontSize: '14px'
            }}>管理库存、库位和出入库操作</p>
          </div>
        </div>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="库存总值"
              value={2456789}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="库存品种"
              value={1256}
              suffix="种"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="库存不足"
              value={23}
              suffix="种"
              valueStyle={{ color: '#ff4d4f' }}
              prefix={<WarningOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="今日出入库"
              value={156}
              suffix="次"
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容区域 */}
      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'inventory',
              label: '库存管理',
              children: (
                <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                  {/* 操作区域 */}
                  <Row justify="space-between" align="middle" gutter={[16, 16]}>
                    <Col xs={24} lg={18}>
                      <Row gutter={[16, 16]}>
                      <Input
                        placeholder="搜索产品编码或名称"
                        prefix={<SearchOutlined />}
                        style={{ width: '100%' }}
                      />
                      <Select placeholder="产品分类" style={{ width: '100%' }}>
                        <Option value="">全部</Option>
                        <Option value="原材料">原材料</Option>
                        <Option value="零部件">零部件</Option>
                        <Option value="成品">成品</Option>
                      </Select>
                      <Select placeholder="库存状态" style={{ width: '100%' }}>
                        <Option value="">全部</Option>
                        <Option value="normal">正常</Option>
                        <Option value="low">库存不足</Option>
                        <Option value="high">库存过多</Option>
                      </Select>
                      </Row>
                    </Col>
                    <Col xs={24} lg={6}>
                      <Row gutter={[8, 8]} justify="end">
                        <Col>
                          <Button icon={<ExportOutlined />}>导出</Button>
                        </Col>
                        <Col>
                          <Button type="primary" icon={<PlusOutlined />}>
                            新增产品
                          </Button>
                        </Col>
                      </Row>
                    </Col>
                  </Row>

                  {/* 库存列表 */}
                  <Table
                    columns={inventoryColumns}
                    dataSource={inventoryData}
                    rowKey="id"
                    pagination={{
                      total: inventoryData.length,
                      pageSize: 10,
                      showSizeChanger: true,
                      showQuickJumper: true,
                      showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
                    }}
                    scroll={{ x: 1000 }}
                  />
                </div>
              )
            },
            {
              key: 'movement',
              label: '出入库记录',
              children: (
                <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                  {/* 操作区域 */}
                  <Row justify="space-between" align="middle" gutter={[16, 16]}>
                    <Col xs={24} lg={18}>
                      <Row gutter={[16, 16]}>
                      <Input
                        placeholder="搜索产品编码或名称"
                        prefix={<SearchOutlined />}
                        style={{ width: '100%' }}
                      />
                      <Select placeholder="变动类型" style={{ width: '100%' }}>
                        <Option value="">全部</Option>
                        <Option value="in">入库</Option>
                        <Option value="out">出库</Option>
                        <Option value="transfer">调拨</Option>
                      </Select>
                      </Row>
                    </Col>
                    <Col xs={24} lg={6}>
                      <Row gutter={[8, 8]} justify="end">
                        <Col>
                          <Button icon={<ExportOutlined />}>导出</Button>
                        </Col>
                        <Col>
                          <Button type="primary" icon={<PlusOutlined />}>
                            新增记录
                          </Button>
                        </Col>
                      </Row>
                    </Col>
                  </Row>

                  {/* 出入库记录列表 */}
                  <Table
                    columns={movementColumns}
                    dataSource={movementData}
                    rowKey="id"
                    pagination={{
                      total: movementData.length,
                      pageSize: 10,
                      showSizeChanger: true,
                      showQuickJumper: true,
                      showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
                    }}
                    scroll={{ x: 1000 }}
                  />
                </div>
              )
            }
          ]}
        />
      </Card>
    </div>
  )
}

export default WarehouseManagement
