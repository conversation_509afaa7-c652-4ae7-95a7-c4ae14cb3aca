/* 全局样式 */
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

/* Ant Design 主题定制 */
.ant-layout {
  min-height: 100vh;
}

.ant-layout-sider {
  box-shadow: 2px 0 8px 0 rgba(29, 35, 41, 0.05);
}

.ant-menu-item-selected {
  background-color: #e6f7ff !important;
}

.ant-menu-item:hover {
  background-color: #f0f9ff !important;
}

/* 自定义工具类 */
.page-container {
  padding: 12px;
  background-color: #f9fafb;
  min-height: 100vh;
}

@media (min-width: 768px) {
  .page-container {
    padding: 24px;
  }
}

.card-container {
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  padding: 12px;
}

@media (min-width: 768px) {
  .card-container {
    padding: 24px;
  }
}

.page-header {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e5e7eb;
}

@media (min-width: 768px) {
  .page-header {
    margin-bottom: 24px;
    padding-bottom: 16px;
  }
}

.page-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 8px;
}

@media (min-width: 768px) {
  .page-title {
    font-size: 1.5rem;
  }
}

.page-description {
  font-size: 0.875rem;
  color: #6b7280;
}

@media (min-width: 768px) {
  .page-description {
    font-size: 1rem;
  }
}

/* 移动端优化 */
@media (max-width: 768px) {
  .ant-table-wrapper {
    overflow-x: auto;
  }

  .ant-table {
    min-width: 600px;
  }

  .ant-card-body {
    padding: 12px;
  }

  .ant-form-item {
    margin-bottom: 16px;
  }

  .ant-modal {
    margin: 0;
    max-width: calc(100vw - 16px);
  }

  .ant-modal-content {
    border-radius: 8px;
  }
}

/* 移动端侧边栏抽屉样式 */
.mobile-sidebar-drawer .ant-drawer-body {
  padding: 0;
  background: #ffffff;
}
