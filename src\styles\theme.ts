/**
 * Ant Design主题配置
 * 统一的设计token和主题定制
 */

import type { ThemeConfig } from 'antd'

// 设计token定义
export const designTokens = {
  // 主色调
  colors: {
    primary: {
      50: '#f0f9ff',
      100: '#e0f2fe', 
      500: '#0ea5e9',
      600: '#0284c7',
      700: '#0369a1',
    },
    gray: {
      50: '#f9fafb',
      100: '#f3f4f6',
      200: '#e5e7eb',
      300: '#d1d5db',
      400: '#9ca3af',
      500: '#6b7280',
      600: '#4b5563',
      700: '#374151',
      800: '#1f2937',
      900: '#111827',
    },
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#3b82f6',
  },
  
  // 间距系统
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 48,
  },
  
  // 字体系统
  typography: {
    fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif',
    fontSize: {
      xs: 12,
      sm: 14,
      base: 16,
      lg: 18,
      xl: 20,
      '2xl': 24,
    },
    fontWeight: {
      normal: 400,
      medium: 500,
      semibold: 600,
      bold: 700,
    },
  },
  
  // 圆角系统
  borderRadius: {
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
  },
  
  // 阴影系统
  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
  },
}

// Ant Design主题配置
export const antdTheme: ThemeConfig = {
  token: {
    // 主色调
    colorPrimary: designTokens.colors.primary[500],
    colorSuccess: designTokens.colors.success,
    colorWarning: designTokens.colors.warning,
    colorError: designTokens.colors.error,
    colorInfo: designTokens.colors.info,
    
    // 字体
    fontFamily: designTokens.typography.fontFamily,
    fontSize: designTokens.typography.fontSize.base,
    
    // 圆角
    borderRadius: designTokens.borderRadius.md,
    
    // 间距
    padding: designTokens.spacing.md,
    margin: designTokens.spacing.md,
    
    // 阴影
    boxShadow: designTokens.shadows.md,
    boxShadowSecondary: designTokens.shadows.sm,
    
    // 边框
    colorBorder: designTokens.colors.gray[200],
    colorBorderSecondary: designTokens.colors.gray[100],
    
    // 背景色
    colorBgContainer: '#ffffff',
    colorBgElevated: '#ffffff',
    colorBgLayout: designTokens.colors.gray[50],
  },
  
  components: {
    // Layout组件
    Layout: {
      headerBg: '#ffffff',
      siderBg: '#ffffff',
      bodyBg: designTokens.colors.gray[50],
      headerHeight: 64,
      siderWidth: 256,
      triggerBg: designTokens.colors.gray[100],
    },
    
    // Menu组件
    Menu: {
      itemSelectedBg: designTokens.colors.primary[100],
      itemHoverBg: designTokens.colors.primary[50],
      itemSelectedColor: designTokens.colors.primary[600],
      itemColor: designTokens.colors.gray[700],
      iconSize: 16,
      fontSize: designTokens.typography.fontSize.sm,
    },
    
    // Button组件
    Button: {
      borderRadius: designTokens.borderRadius.md,
      fontWeight: designTokens.typography.fontWeight.medium,
      paddingInline: designTokens.spacing.md,
      paddingBlock: designTokens.spacing.sm,
    },
    
    // Card组件
    Card: {
      borderRadius: designTokens.borderRadius.lg,
      paddingLG: designTokens.spacing.lg,
      boxShadow: designTokens.shadows.sm,
    },
    
    // Table组件
    Table: {
      headerBg: designTokens.colors.gray[50],
      headerColor: designTokens.colors.gray[700],
      borderColor: designTokens.colors.gray[200],
      rowHoverBg: designTokens.colors.gray[50],
    },
    
    // Form组件
    Form: {
      labelColor: designTokens.colors.gray[700],
      labelFontSize: designTokens.typography.fontSize.sm,
      itemMarginBottom: designTokens.spacing.md,
    },
    
    // Input组件
    Input: {
      borderRadius: designTokens.borderRadius.md,
      paddingInline: designTokens.spacing.sm,
      paddingBlock: designTokens.spacing.sm,
    },
    
    // Modal组件
    Modal: {
      borderRadius: designTokens.borderRadius.lg,
      paddingLG: designTokens.spacing.lg,
    },
    
    // Drawer组件
    Drawer: {
      paddingLG: designTokens.spacing.lg,
    },
    
    // Notification组件
    Notification: {
      borderRadius: designTokens.borderRadius.md,
      paddingMD: designTokens.spacing.md,
    },
    
    // Message组件
    Message: {
      borderRadius: designTokens.borderRadius.md,
      paddingMD: designTokens.spacing.md,
    },
  },
}

// 暗色主题配置
export const darkTheme: ThemeConfig = {
  ...antdTheme,
  token: {
    ...antdTheme.token,
    colorBgContainer: '#1f2937',
    colorBgElevated: '#374151',
    colorBgLayout: '#111827',
    colorText: '#f9fafb',
    colorTextSecondary: '#d1d5db',
    colorBorder: '#4b5563',
    colorBorderSecondary: '#374151',
  },
  components: {
    ...antdTheme.components,
    Layout: {
      ...antdTheme.components?.Layout,
      headerBg: '#1f2937',
      siderBg: '#1f2937',
      bodyBg: '#111827',
    },
    Menu: {
      ...antdTheme.components?.Menu,
      itemColor: '#d1d5db',
      darkItemBg: '#374151',
      darkItemSelectedBg: '#4b5563',
    },
    Table: {
      ...antdTheme.components?.Table,
      headerBg: '#374151',
      headerColor: '#f9fafb',
      borderColor: '#4b5563',
      rowHoverBg: '#374151',
    },
  },
}

export default antdTheme
