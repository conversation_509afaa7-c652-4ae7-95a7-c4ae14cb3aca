/**
 * 统一的生产订单表格组件
 * 用于消除MRP功能中生产订单显示的代码重复
 */

import React from 'react'
import { Table, Tag, Card } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import { ProductionOrder, CustomerLevel } from '@/types'
import { styleHelpers } from '@/utils/styles/antdHelpers'
// 优先级映射工具已删除，使用本地实现

// 本地优先级转换函数
const convertPriorityToCreditLevel = (priority: number): CustomerLevel => {
  if (priority >= 90) return 'A'
  if (priority >= 70) return 'B'
  if (priority >= 50) return 'C'
  if (priority >= 30) return 'D'
  return 'E'
}

const getCreditLevelColor = (creditLevel: CustomerLevel): string => {
  const colorMap = {
    'A': 'green',
    'B': 'blue',
    'C': 'orange',
    'D': 'red',
    'E': 'volcano'
  }
  return colorMap[creditLevel] || 'default'
}

export interface ProductionOrderTableProps {
  /** 数据源 */
  dataSource: ProductionOrder[]
  /** 显示的列，默认显示所有列 */
  showColumns?: ('orderNumber' | 'productName' | 'quantity' | 'priority' | 'dates' | 'status')[]
  /** 紧凑模式 */
  compact?: boolean
  /** 表格标题 */
  title?: string
  /** 是否显示卡片包装 */
  showCard?: boolean
  /** 是否显示分页 */
  pagination?: boolean
  /** 表格大小 */
  size?: 'small' | 'middle' | 'large'
  /** 自定义样式 */
  style?: React.CSSProperties
  /** 数据源类型，用于调整显示逻辑 */
  sourceType?: 'historical' | 'mrp_result'
}

/**
 * 生产订单表格组件
 */
export const ProductionOrderTable: React.FC<ProductionOrderTableProps> = ({
  dataSource = [],
  showColumns = ['orderNumber', 'productName', 'quantity', 'priority', 'dates', 'status'],
  compact = false,
  title,
  showCard = true,
  pagination = false,
  size = 'small',
  style,
  sourceType = 'historical'
}) => {
  
  // 统一的列定义配置
  const getColumnConfig = () => {
    const allColumns: ColumnsType<ProductionOrder> = [
      {
        title: '生产订单号',
        dataIndex: 'orderNumber',
        key: 'orderNumber',
        width: compact ? 120 : 150,
        fixed: 'left'
      },
      {
        title: '产品信息',
        dataIndex: 'productName',
        key: 'productName',
        width: compact ? 150 : 200,
        ellipsis: true,
        render: (_, record: ProductionOrder) => {
          if (record.isSharedMold && record.productItems && record.productItems.length > 1) {
            // 共享模具多产品显示
            return (
              <div>
                <div style={{ fontWeight: 500, color: '#1890ff' }}>
                  🔧 {record.formingMoldNumber} (共享模具)
                </div>
                <div style={{ fontSize: '12px', color: '#9ca3af', marginTop: '4px' }}>
                  {record.productItems.map(item => item.productCode).join(' + ')}
                </div>
                <div style={{ fontSize: '12px', color: '#d1d5db' }}>
                  {record.productItems.length} 个产品
                </div>
              </div>
            )
          } else {
            // 传统单产品显示
            return (
              <div>
                <div style={{ fontWeight: 500 }}>{record.productName}</div>
                <div style={{ fontSize: '12px', color: '#9ca3af' }}>{record.productCode}</div>
                {record.formingMoldNumber && (
                  <div style={{ fontSize: '12px', color: '#d1d5db' }}>模具: {record.formingMoldNumber}</div>
                )}
              </div>
            )
          }
        }
      },
      {
        title: sourceType === 'mrp_result' ? '生产数量' : '计划数量',
        key: 'quantity',
        width: 120,
        render: (_, record: ProductionOrder) => {
          if (record.isSharedMold && record.productItems && record.productItems.length > 1) {
            // 共享模具显示最大生产量和各产品需求
            return (
              <div>
                <div style={{ fontWeight: 500, color: '#52c41a' }}>
                  {record.plannedQuantity.toLocaleString()} (生产量)
                </div>
                <div style={{ fontSize: '12px', color: '#9ca3af', marginTop: '4px' }}>
                  {record.productItems.map(item =>
                    `${item.productCode}: ${item.requiredQuantity}`
                  ).join(', ')}
                </div>
              </div>
            )
          } else {
            // 传统单产品显示
            const quantity = sourceType === 'mrp_result'
              ? (record as any).requiredQuantity || record.plannedQuantity
              : record.plannedQuantity
            return quantity ? quantity.toLocaleString() : '0'
          }
        }
      },
      {
        title: '信用等级',
        dataIndex: sourceType === 'mrp_result' ? 'urgencyLevel' : 'customerCreditLevel',
        key: 'customerCreditLevel',
        width: 100,
        render: (creditLevel: string | CustomerLevel) => {
          // 处理不同数据源的信用等级格式
          let color: string
          let text: string

          if (typeof creditLevel === 'string' && ['A', 'B', 'C', 'D', 'E'].includes(creditLevel)) {
            // 标准信用等级格式
            color = getCreditLevelColor(creditLevel as CustomerLevel)
            text = `${creditLevel}级`
          } else if (typeof creditLevel === 'string') {
            // MRP结果中的urgencyLevel格式
            const level = creditLevel
            color = level === 'high' ? 'red' : level === 'medium' ? 'orange' : 'blue'
            text = level === 'high' ? '高' : level === 'medium' ? '中' : '低'
          } else {
            // 未设置信用等级
            color = 'default'
            text = '未设置'
          }

          return <Tag color={color}>{text}</Tag>
        }
      },
      {
        title: compact ? '日期' : '开始日期',
        dataIndex: sourceType === 'mrp_result' ? 'estimatedStartDate' : 'startDate',
        key: 'startDate',
        width: compact ? 90 : 100,
        render: (date: string) => date || '-'
      },
      {
        title: '结束日期',
        dataIndex: sourceType === 'mrp_result' ? 'estimatedCompletionDate' : 'endDate',
        key: 'endDate',
        width: compact ? 90 : 100,
        render: (date: string) => date || '-'
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 80,
        render: (status: string) => {
          if (sourceType === 'mrp_result') {
            // MRP结果中的生产订单都是计划状态
            return <Tag color="blue">计划中</Tag>
          }
          
          // 历史数据中的状态
          const statusConfig = {
            planned: { color: 'blue', text: '计划中' },
            in_progress: { color: 'orange', text: '生产中' },
            completed: { color: 'green', text: '已完成' },
            cancelled: { color: 'red', text: '已取消' }
          }
          
          const config = statusConfig[status as keyof typeof statusConfig] || 
                        { color: 'default', text: '未知' }
          
          return <Tag color={config.color}>{config.text}</Tag>
        }
      }
    ]

    // 根据showColumns过滤列
    return allColumns.filter(column => {
      const key = column.key as string
      if (key === 'startDate' || key === 'endDate') {
        return showColumns.includes('dates')
      }
      return showColumns.includes(key as any)
    })
  }

  // 表格配置
  const tableProps = {
    dataSource,
    columns: getColumnConfig(),
    rowKey: 'id',
    pagination: pagination === true ? { pageSize: 10, showSizeChanger: true } : pagination,
    size,
    scroll: { x: 'max-content' },
    locale: {
      emptyText: '暂无生产订单数据'
    }
  }

  // 表格内容
  const tableContent = <Table {...tableProps} />

  // 根据showCard决定是否包装在Card中
  if (showCard) {
    return (
      <Card 
        title={title} 
        size={compact ? 'small' : 'default'}
        style={style}
      >
        {tableContent}
      </Card>
    )
  }

  return (
    <div style={style}>
      {title && (
        <h4 style={{ marginBottom: 16, fontSize: compact ? '14px' : '16px' }}>
          {title}
        </h4>
      )}
      {tableContent}
    </div>
  )
}

/**
 * 预设配置的生产订单表格组件
 */

// MRP执行信息区域使用的历史订单表格
export const HistoricalProductionOrderTable: React.FC<{
  dataSource: ProductionOrder[]
  style?: React.CSSProperties
}> = ({ dataSource, style }) => (
  <ProductionOrderTable
    dataSource={dataSource}
    title="生成的生产订单"
    sourceType="historical"
    showColumns={['orderNumber', 'productName', 'quantity', 'priority', 'dates', 'status']}
    showCard={false}
    style={style}
  />
)

// MRP执行结果区域使用的新生成订单表格
export const MRPResultProductionOrderTable: React.FC<{
  dataSource: ProductionOrder[]
  style?: React.CSSProperties
}> = ({ dataSource, style }) => (
  <ProductionOrderTable
    dataSource={dataSource}
    title="生成的生产订单"
    sourceType="mrp_result"
    showColumns={['orderNumber', 'productName', 'quantity', 'priority', 'dates', 'status']}
    showCard={true}
    size="small"
    style={style}
  />
)

// 紧凑模式的生产订单表格
export const CompactProductionOrderTable: React.FC<{
  dataSource: ProductionOrder[]
  title?: string
}> = ({ dataSource, title }) => (
  <ProductionOrderTable
    dataSource={dataSource}
    title={title}
    compact={true}
    showColumns={['orderNumber', 'productName', 'quantity', 'status']}
    showCard={false}
    size="small"
  />
)

export default ProductionOrderTable
