'use client'

import React from 'react'
import { Card, Row, Col, Statistic, Button } from 'antd'
import { 
  AppstoreOutlined,
  BarcodeOutlined,
  DatabaseOutlined,
  PlusOutlined
} from '@ant-design/icons'
import { useRouter } from 'next/navigation'
import { styleHelpers } from '@/utils/styles/antdHelpers'

const MasterDataOverview: React.FC = () => {
  const router = useRouter()

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: styleHelpers.spacing.lg }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: styleHelpers.spacing.md }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <DatabaseOutlined style={{
            fontSize: '24px',
            color: '#1890ff',
            marginRight: styleHelpers.spacing.sm
          }} />
          <div>
            <h1 style={{
              fontSize: '24px',
              fontWeight: 'bold',
              color: styleHelpers.colors.gray[900],
              margin: 0,
              marginBottom: styleHelpers.spacing.xs
            }}>基础数据管理</h1>
            <p style={{
              color: styleHelpers.colors.gray[600],
              margin: 0,
              fontSize: '14px'
            }}>管理物料主数据和产品型号库等基础信息</p>
          </div>
        </div>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="物料总数"
              value={156}
              suffix="种"
              valueStyle={{ color: '#1890ff' }}
              prefix={<AppstoreOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="产品型号"
              value={89}
              suffix="个"
              valueStyle={{ color: '#52c41a' }}
              prefix={<BarcodeOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="活跃物料"
              value={142}
              suffix="种"
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="活跃型号"
              value={76}
              suffix="个"
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 快速操作 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card
            title="物料主数据管理"
            style={{ height: 256 }}
            extra={
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => router.push('/master-data/materials')}
              >
                管理物料
              </Button>
            }
          >
            <div style={{ display: 'flex', flexDirection: 'column', gap: styleHelpers.spacing.md }}>
              <div style={{
                padding: styleHelpers.spacing.md,
                border: '1px solid #e5e7eb',
                borderRadius: styleHelpers.borderRadius.lg,
                cursor: 'pointer',
                transition: 'background-color 0.2s ease',
                backgroundColor: 'transparent'
              }}
              onClick={() => router.push('/master-data/materials')}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = styleHelpers.colors.gray[50]
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent'
              }}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <AppstoreOutlined style={{
                    fontSize: '20px',
                    color: '#3b82f6',
                    marginRight: styleHelpers.spacing.sm
                  }} />
                  <div>
                    <div style={{ fontWeight: 500 }}>物料信息管理</div>
                    <div style={{
                      fontSize: '14px',
                      color: styleHelpers.colors.gray[500]
                    }}>管理物料编码、名称、价格等基础信息</div>
                  </div>
                </div>
              </div>

              <div style={{
                padding: styleHelpers.spacing.md,
                border: '1px solid #e5e7eb',
                borderRadius: styleHelpers.borderRadius.lg,
                cursor: 'pointer',
                transition: 'background-color 0.2s ease',
                backgroundColor: 'transparent'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = styleHelpers.colors.gray[50]
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent'
              }}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <DatabaseOutlined style={{
                    fontSize: '20px',
                    color: '#10b981',
                    marginRight: styleHelpers.spacing.sm
                  }} />
                  <div>
                    <div style={{ fontWeight: 500 }}>批量导入导出</div>
                    <div style={{
                      fontSize: '14px',
                      color: styleHelpers.colors.gray[500]
                    }}>支持Excel批量导入导出物料数据</div>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card
            title="产品型号库管理"
            style={{ height: 256 }}
            extra={
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => router.push('/master-data/product-models')}
              >
                管理型号
              </Button>
            }
          >
            <div style={{ display: 'flex', flexDirection: 'column', gap: styleHelpers.spacing.md }}>
              <div style={{
                padding: styleHelpers.spacing.md,
                border: '1px solid #e5e7eb',
                borderRadius: styleHelpers.borderRadius.lg,
                cursor: 'pointer',
                transition: 'background-color 0.2s ease',
                backgroundColor: 'transparent'
              }}
              onClick={() => router.push('/master-data/product-models')}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = styleHelpers.colors.gray[50]
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent'
              }}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <BarcodeOutlined style={{
                    fontSize: '20px',
                    color: '#8b5cf6',
                    marginRight: styleHelpers.spacing.sm
                  }} />
                  <div>
                    <div style={{ fontWeight: 500 }}>型号信息管理</div>
                    <div style={{
                      fontSize: '14px',
                      color: styleHelpers.colors.gray[500]
                    }}>管理产品型号、模具关联和计件单价</div>
                  </div>
                </div>
              </div>

              <div style={{
                padding: styleHelpers.spacing.md,
                border: '1px solid #e5e7eb',
                borderRadius: styleHelpers.borderRadius.lg,
                cursor: 'pointer',
                transition: 'background-color 0.2s ease',
                backgroundColor: 'transparent'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = styleHelpers.colors.gray[50]
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent'
              }}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <DatabaseOutlined style={{
                    fontSize: '20px',
                    color: '#f59e0b',
                    marginRight: styleHelpers.spacing.sm
                  }} />
                  <div>
                    <div style={{ fontWeight: 500 }}>模具关联管理</div>
                    <div style={{
                      fontSize: '14px',
                      color: styleHelpers.colors.gray[500]
                    }}>管理成型模具和热压模具关联信息</div>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default MasterDataOverview
