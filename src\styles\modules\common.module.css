/**
 * 通用CSS Modules样式
 * 提供常用的样式类，替代Tailwind工具类
 */

/* 卡片样式 */
.card {
  border-radius: 12px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  background: #ffffff;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease-in-out;
}

.cardHover:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.cardShadowSm {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.cardShadowMd {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.cardShadowLg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* 布局样式 */
.flexCenter {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flexBetween {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flexStart {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.flexEnd {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.flexCol {
  display: flex;
  flex-direction: column;
}

.flexColCenter {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.flexWrap {
  flex-wrap: wrap;
}

.flexGrow {
  flex-grow: 1;
}

.flexShrink {
  flex-shrink: 1;
}

/* 网格布局 */
.grid {
  display: grid;
}

.gridCols1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.gridCols2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.gridCols3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.gridCols4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.gridGap2 {
  gap: 8px;
}

.gridGap4 {
  gap: 16px;
}

.gridGap6 {
  gap: 24px;
}

/* 间距样式 */
.p1 { padding: 4px; }
.p2 { padding: 8px; }
.p3 { padding: 12px; }
.p4 { padding: 16px; }
.p6 { padding: 24px; }
.p8 { padding: 32px; }

.px1 { padding-left: 4px; padding-right: 4px; }
.px2 { padding-left: 8px; padding-right: 8px; }
.px3 { padding-left: 12px; padding-right: 12px; }
.px4 { padding-left: 16px; padding-right: 16px; }
.px6 { padding-left: 24px; padding-right: 24px; }

.py1 { padding-top: 4px; padding-bottom: 4px; }
.py2 { padding-top: 8px; padding-bottom: 8px; }
.py3 { padding-top: 12px; padding-bottom: 12px; }
.py4 { padding-top: 16px; padding-bottom: 16px; }
.py6 { padding-top: 24px; padding-bottom: 24px; }

.m1 { margin: 4px; }
.m2 { margin: 8px; }
.m3 { margin: 12px; }
.m4 { margin: 16px; }
.m6 { margin: 24px; }
.m8 { margin: 32px; }

.mx1 { margin-left: 4px; margin-right: 4px; }
.mx2 { margin-left: 8px; margin-right: 8px; }
.mx4 { margin-left: 16px; margin-right: 16px; }
.mx6 { margin-left: 24px; margin-right: 24px; }

.my1 { margin-top: 4px; margin-bottom: 4px; }
.my2 { margin-top: 8px; margin-bottom: 8px; }
.my4 { margin-top: 16px; margin-bottom: 16px; }
.my6 { margin-top: 24px; margin-bottom: 24px; }

.mb2 { margin-bottom: 8px; }
.mb4 { margin-bottom: 16px; }
.mb6 { margin-bottom: 24px; }

.mt2 { margin-top: 8px; }
.mt4 { margin-top: 16px; }
.mt6 { margin-top: 24px; }

/* 尺寸样式 */
.wFull { width: 100%; }
.hFull { height: 100%; }
.minHScreen { min-height: 100vh; }
.maxWFull { max-width: 100%; }

/* 背景色 */
.bgWhite { background-color: #ffffff; }
.bgGray50 { background-color: #f9fafb; }
.bgGray100 { background-color: #f3f4f6; }
.bgGray200 { background-color: #e5e7eb; }
.bgPrimary50 { background-color: #f0f9ff; }
.bgPrimary100 { background-color: #e0f2fe; }
.bgPrimary500 { background-color: #0ea5e9; }

/* 文本颜色 */
.textGray600 { color: #4b5563; }
.textGray700 { color: #374151; }
.textGray900 { color: #111827; }
.textPrimary500 { color: #0ea5e9; }
.textPrimary600 { color: #0284c7; }
.textWhite { color: #ffffff; }

/* 字体大小 */
.textXs { font-size: 12px; }
.textSm { font-size: 14px; }
.textBase { font-size: 16px; }
.textLg { font-size: 18px; }
.textXl { font-size: 20px; }
.text2xl { font-size: 24px; }

/* 字体粗细 */
.fontNormal { font-weight: 400; }
.fontMedium { font-weight: 500; }
.fontSemibold { font-weight: 600; }
.fontBold { font-weight: 700; }

/* 文本对齐 */
.textLeft { text-align: left; }
.textCenter { text-align: center; }
.textRight { text-align: right; }

/* 边框 */
.border { border: 1px solid #e5e7eb; }
.borderGray200 { border-color: #e5e7eb; }
.borderGray300 { border-color: #d1d5db; }
.borderB { border-bottom: 1px solid #e5e7eb; }
.borderT { border-top: 1px solid #e5e7eb; }
.borderR { border-right: 1px solid #e5e7eb; }
.borderL { border-left: 1px solid #e5e7eb; }

/* 圆角 */
.rounded { border-radius: 4px; }
.roundedMd { border-radius: 8px; }
.roundedLg { border-radius: 12px; }
.roundedXl { border-radius: 16px; }
.roundedFull { border-radius: 9999px; }

/* 阴影 */
.shadowSm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }
.shadowMd { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); }
.shadowLg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); }
.shadowNone { box-shadow: none; }

/* 过渡动画 */
.transition { transition: all 0.2s ease-in-out; }
.transitionFast { transition: all 0.15s ease-in-out; }
.transitionSlow { transition: all 0.3s ease-in-out; }

/* 显示/隐藏 */
.hidden { display: none; }
.block { display: block; }
.inlineBlock { display: inline-block; }
.inline { display: inline; }

/* 定位 */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

/* 溢出 */
.overflowHidden { overflow: hidden; }
.overflowAuto { overflow: auto; }
.overflowScroll { overflow: scroll; }

/* 光标 */
.cursorPointer { cursor: pointer; }
.cursorDefault { cursor: default; }
.cursorNotAllowed { cursor: not-allowed; }

/* 选择状态 */
.selectNone { user-select: none; }
.selectText { user-select: text; }
.selectAll { user-select: all; }

/* 响应式工具类 */
@media (min-width: 768px) {
  .mdBlock { display: block; }
  .mdHidden { display: none; }
  .mdFlex { display: flex; }
  .mdGrid { display: grid; }
  .mdGridCols2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .mdGridCols3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .mdP6 { padding: 24px; }
  .mdTextLg { font-size: 18px; }
}
