# ERP系统用户认证和权限管理系统PRD - 阶段2：权限管理系统

**文档版本**: v1.0  
**创建日期**: 2025-07-31  
**产品经理**: AI Assistant  
**技术负责人**: 开发团队  
**项目代号**: ERP-Auth-System-Phase2  
**阶段周期**: 3周  
**依赖阶段**: 阶段1 - 基础认证系统

---

## 📋 **1. 阶段概述**

### 1.1 阶段目标

基于阶段1建立的认证基础，本阶段将实现完整的用户管理和角色权限管理系统，提供细粒度的权限控制能力，满足企业级权限管理需求。

### 1.2 核心价值

- **精细化管理**: 实现用户、角色、权限的精细化管理
- **业务支撑**: 为各业务模块提供权限控制支撑
- **管理效率**: 提供便捷的权限分配和管理界面
- **安全保障**: 建立完整的权限控制体系

### 1.3 阶段范围

**包含功能**:
- 用户管理系统（CRUD、状态管理、密码管理）
- 角色管理系统（角色CRUD、权限分配）
- 权限树组件和权限分配界面
- 权限继承机制
- 权限控制组件库

**不包含功能**:
- 审计日志系统
- 安全策略配置
- 性能优化
- 高级安全功能

---

## 🎯 **2. 功能需求详述**

### 2.1 用户管理系统

#### 2.1.1 用户管理界面

**用户故事**:
> 作为系统管理员，我希望能够方便地管理所有用户账户，包括创建、编辑、禁用用户，以及为用户分配角色。

**功能规格**:

| 功能模块 | 规格说明 | 验收标准 |
|----------|----------|----------|
| 用户列表 | 显示所有用户，支持搜索、筛选、分页 | 列表加载 < 1秒，支持1000+用户 |
| 用户创建 | 创建新用户，设置基本信息和初始角色 | 表单验证完整，创建成功率 > 99% |
| 用户编辑 | 修改用户信息、状态、角色分配 | 修改实时生效，数据一致性保证 |
| 用户状态管理 | 启用/禁用/锁定用户账户 | 状态变更立即生效，权限同步更新 |

**界面设计**:
```typescript
// src/app/admin/users/page.tsx
export default function UsersManagementPage() {
  return (
    <ProtectedRoute requiredPermission="admin:users:read">
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">用户管理</h1>
          <PermissionGuard permission="admin:users:create">
            <Button type="primary" icon={<PlusOutlined />}>
              新建用户
            </Button>
          </PermissionGuard>
        </div>
        <UsersList />
      </div>
    </ProtectedRoute>
  )
}
```

#### 2.1.2 用户CRUD操作

**API接口设计**:
```typescript
// 用户管理API
interface UserCreateRequest {
  username: string
  email: string
  fullName: string
  phone?: string
  departmentId?: string
  employeeId?: string
  roleIds: string[]
  initialPassword: string
}

interface UserUpdateRequest {
  fullName?: string
  email?: string
  phone?: string
  departmentId?: string
  employeeId?: string
  status?: 'active' | 'inactive' | 'locked'
}

// API路径
/api/users/
├── /              # GET - 获取用户列表, POST - 创建用户
├── /[id]          # GET - 获取用户详情, PUT - 更新用户, DELETE - 删除用户
├── /[id]/roles    # GET - 获取用户角色, PUT - 分配角色
└── /[id]/password # PUT - 重置密码
```

#### 2.1.3 密码管理功能

**密码策略**:
- 初始密码自动生成或管理员设置
- 支持密码重置功能
- 强制首次登录修改密码
- 密码历史记录（不能重复最近5次密码）

**实现规格**:
```typescript
// 密码管理组件
export const PasswordManagement: React.FC<{userId: string}> = ({ userId }) => {
  const handleResetPassword = async () => {
    const newPassword = generateSecurePassword()
    await dataAccessManager.users.resetPassword(userId, newPassword)
    // 发送密码重置通知
  }

  return (
    <Space>
      <Button onClick={handleResetPassword}>重置密码</Button>
      <Button onClick={() => setForcePasswordChange(userId, true)}>
        强制修改密码
      </Button>
    </Space>
  )
}
```

### 2.2 角色管理系统

#### 2.2.1 角色管理界面

**用户故事**:
> 作为系统管理员，我希望能够创建和管理不同的用户角色，为每个角色分配合适的权限，实现精细化的权限控制。

**功能规格**:

| 功能 | 描述 | 验收标准 |
|------|------|----------|
| 角色列表 | 显示所有角色及其基本信息 | 列表正确显示，支持搜索和分页 |
| 创建角色 | 创建新角色并分配权限 | 角色创建成功，权限分配正确 |
| 编辑角色 | 修改角色信息和权限 | 修改保存成功，权限变更生效 |
| 删除角色 | 删除不再使用的角色 | 删除成功，关联用户处理正确 |
| 权限分配 | 为角色分配具体权限 | 权限树正确显示，分配结果准确 |

#### 2.2.2 权限树组件

**组件设计**:
```typescript
// src/components/admin/PermissionTree.tsx
interface PermissionTreeProps {
  selectedPermissions: string[]
  onPermissionChange: (permissions: string[]) => void
  readonly?: boolean
}

export const PermissionTree: React.FC<PermissionTreeProps> = ({
  selectedPermissions,
  onPermissionChange,
  readonly = false
}) => {
  const permissionData = [
    {
      key: 'sales',
      title: '销售管理',
      children: [
        { key: 'sales:read', title: '查看销售数据' },
        { key: 'sales:create', title: '创建销售订单' },
        { key: 'sales:update', title: '修改销售订单' },
        { key: 'sales:delete', title: '删除销售订单' },
        { key: 'sales:export', title: '导出销售数据' }
      ]
    },
    // ... 其他模块权限
  ]

  return (
    <Tree
      checkable
      checkedKeys={selectedPermissions}
      onCheck={onPermissionChange}
      treeData={permissionData}
      disabled={readonly}
    />
  )
}
```

#### 2.2.3 角色权限分配

**分配流程**:
1. 选择角色
2. 展示权限树
3. 勾选/取消权限
4. 保存权限配置
5. 权限变更生效

**API设计**:
```typescript
// PUT /api/roles/[id]/permissions
interface RolePermissionRequest {
  permissionIds: string[]
  grantedBy: string
}

interface RolePermissionResponse {
  status: 'success' | 'error'
  data: {
    roleId: string
    permissions: Permission[]
    affectedUsers: number
  }
  message: string
}
```

### 2.3 权限继承机制

#### 2.3.1 继承规则设计

**继承层级**:
```
用户直接权限 > 角色权限 > 部门权限 > 默认权限
```

**继承逻辑**:
- 用户可以拥有多个角色
- 权限取并集（最大权限原则）
- 支持权限覆盖和撤销
- 临时权限授权（有时效性）

#### 2.3.2 权限计算引擎

```typescript
// src/utils/permissions/PermissionCalculator.ts
export class PermissionCalculator {
  static calculateUserPermissions(user: User): string[] {
    const directPermissions = user.directPermissions || []
    const rolePermissions = user.roles.flatMap(role => role.permissions)
    const departmentPermissions = user.department?.permissions || []
    
    // 合并所有权限（去重）
    const allPermissions = [
      ...directPermissions,
      ...rolePermissions,
      ...departmentPermissions
    ]
    
    return [...new Set(allPermissions)]
  }

  static hasPermission(user: User, permission: string): boolean {
    const userPermissions = this.calculateUserPermissions(user)
    return userPermissions.includes(permission) || 
           userPermissions.some(p => this.isWildcardMatch(p, permission))
  }

  private static isWildcardMatch(pattern: string, permission: string): boolean {
    // 支持通配符权限，如 "sales:*" 匹配 "sales:read"
    return pattern.endsWith('*') && 
           permission.startsWith(pattern.slice(0, -1))
  }
}
```

### 2.4 权限控制组件库

#### 2.4.1 页面级权限控制

```typescript
// src/components/auth/ProtectedRoute.tsx
interface ProtectedRouteProps {
  children: React.ReactNode
  requiredPermission?: string
  requiredRole?: string
  fallback?: React.ReactNode
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredPermission,
  requiredRole,
  fallback
}) => {
  const { user, isAuthenticated } = useAppStore()
  const { hasPermission, hasRole } = usePermissions()

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />
  }

  if (requiredPermission && !hasPermission(requiredPermission)) {
    return fallback || <NoPermissionPage />
  }

  if (requiredRole && !hasRole(requiredRole)) {
    return fallback || <NoPermissionPage />
  }

  return <>{children}</>
}
```

#### 2.4.2 组件级权限控制

```typescript
// src/components/auth/PermissionGuard.tsx
interface PermissionGuardProps {
  children: React.ReactNode
  permission: string
  fallback?: React.ReactNode
  hideWhenNoPermission?: boolean
}

export const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  permission,
  fallback,
  hideWhenNoPermission = false
}) => {
  const { hasPermission } = usePermissions()

  if (!hasPermission(permission)) {
    if (hideWhenNoPermission) {
      return null
    }
    return fallback || <span className="text-gray-400">无权限</span>
  }

  return <>{children}</>
}
```

#### 2.4.3 权限Hook

```typescript
// src/hooks/usePermissions.ts
export const usePermissions = () => {
  const { user } = useAppStore()

  const hasPermission = useCallback((permission: string): boolean => {
    if (!user) return false
    return PermissionCalculator.hasPermission(user, permission)
  }, [user])

  const hasRole = useCallback((role: string): boolean => {
    if (!user) return false
    return user.roles.some(r => r.roleCode === role)
  }, [user])

  const hasAnyPermission = useCallback((permissions: string[]): boolean => {
    return permissions.some(permission => hasPermission(permission))
  }, [hasPermission])

  const hasAllPermissions = useCallback((permissions: string[]): boolean => {
    return permissions.every(permission => hasPermission(permission))
  }, [hasPermission])

  return {
    hasPermission,
    hasRole,
    hasAnyPermission,
    hasAllPermissions,
    userPermissions: user ? PermissionCalculator.calculateUserPermissions(user) : []
  }
}
```

---

## 🏗️ **3. 技术实现要求**

### 3.1 数据库扩展

#### 3.1.1 新增表结构

```sql
-- 角色表
CREATE TABLE roles (
  id VARCHAR(36) PRIMARY KEY,
  role_code VARCHAR(50) UNIQUE NOT NULL,
  role_name VARCHAR(100) NOT NULL,
  description TEXT,
  is_system_role BOOLEAN DEFAULT FALSE,
  status ENUM('active', 'inactive') DEFAULT 'active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_by VARCHAR(36),
  updated_by VARCHAR(36)
);

-- 权限表
CREATE TABLE permissions (
  id VARCHAR(36) PRIMARY KEY,
  permission_code VARCHAR(100) UNIQUE NOT NULL,
  permission_name VARCHAR(100) NOT NULL,
  module VARCHAR(50) NOT NULL,
  action VARCHAR(50) NOT NULL,
  resource VARCHAR(100),
  description TEXT,
  is_system_permission BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户角色关联表
CREATE TABLE user_roles (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  role_id VARCHAR(36) NOT NULL,
  granted_by VARCHAR(36) NOT NULL,
  granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  expires_at TIMESTAMP NULL,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
  UNIQUE KEY unique_user_role (user_id, role_id)
);

-- 角色权限关联表
CREATE TABLE role_permissions (
  id VARCHAR(36) PRIMARY KEY,
  role_id VARCHAR(36) NOT NULL,
  permission_id VARCHAR(36) NOT NULL,
  granted_by VARCHAR(36) NOT NULL,
  granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
  FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
  UNIQUE KEY unique_role_permission (role_id, permission_id)
);
```

### 3.2 API接口扩展

#### 3.2.1 用户管理API

```
/api/users/
├── /              # GET - 获取用户列表, POST - 创建用户
├── /[id]          # GET - 获取用户详情, PUT - 更新用户, DELETE - 删除用户
├── /[id]/roles    # GET - 获取用户角色, PUT - 分配角色
├── /[id]/permissions # GET - 获取用户权限
└── /[id]/password # PUT - 重置密码
```

#### 3.2.2 角色管理API

```
/api/roles/
├── /              # GET - 获取角色列表, POST - 创建角色
├── /[id]          # GET - 获取角色详情, PUT - 更新角色, DELETE - 删除角色
└── /[id]/permissions # GET - 获取角色权限, PUT - 分配权限
```

---

## 📅 **4. 开发计划**

### 4.1 里程碑2.1: 用户管理 (第3周)

**开发任务**:
- [ ] 用户管理界面开发
- [ ] 用户CRUD API实现
- [ ] 用户状态管理功能
- [ ] 密码管理功能

**验收标准**:
- 用户管理界面完整可用
- 用户CRUD操作正常
- 状态管理功能正确
- 密码管理安全可靠

### 4.2 里程碑2.2: 角色权限管理 (第4-5周)

**开发任务**:
- [ ] 角色管理界面开发
- [ ] 权限树组件实现
- [ ] 角色权限分配功能
- [ ] 权限继承机制实现

**验收标准**:
- 角色管理功能完整
- 权限树组件可用
- 权限分配功能正常
- 权限继承逻辑正确

---

## ✅ **5. 验收标准**

### 5.1 功能测试

#### 5.1.1 用户管理测试

**测试用例1: 用户创建**
```
测试步骤:
1. 访问用户管理页面
2. 点击新建用户按钮
3. 填写用户信息并分配角色
4. 保存用户

预期结果:
- 用户创建成功
- 角色分配正确
- 权限继承正常

验收标准:
✅ 用户信息保存正确
✅ 角色权限生效
✅ 操作日志记录
```

### 5.2 性能指标

| 功能模块 | 响应时间要求 | 验收标准 |
|----------|--------------|----------|
| 用户列表加载 | < 1秒 | 支持1000+用户 |
| 权限树加载 | < 500ms | 支持100+权限节点 |
| 权限验证 | < 100ms | 99%请求满足要求 |

---

## 📦 **6. 交付物**

### 6.1 功能交付物

- 完整的用户管理系统
- 角色权限分配功能
- 权限控制组件库
- 权限继承机制

### 6.2 技术交付物

- 扩展的数据库结构
- 用户和角色管理API
- 权限计算引擎
- 权限控制组件

### 6.3 质量要求

- 集成测试通过
- 权限逻辑测试覆盖率 > 90%
- 性能测试达标
- 安全测试通过

---

**阶段2文档结束**

本文档为ERP系统用户认证和权限管理系统第二阶段的详细需求规格说明，专注于权限管理系统的实现，为企业级权限控制提供完整解决方案。
