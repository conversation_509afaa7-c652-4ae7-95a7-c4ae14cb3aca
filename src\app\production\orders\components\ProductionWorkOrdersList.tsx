'use client'

import React, { useState, useEffect, useMemo, useCallback } from 'react'
import type { Dayjs } from 'dayjs'
import { Table, Card, Button, Space, Input, Select, DatePicker, Tag, Badge, Modal, Row, Col, Statistic, Progress, Dropdown, Typography, App } from 'antd'
import { ReloadOutlined, ExportOutlined, FilterOutlined, EyeOutlined, EditOutlined, DeleteOutlined, MoreOutlined } from '@ant-design/icons'
import type { ColumnsType, TableProps } from 'antd/es/table'
import type { ProductionWorkOrder, CustomerLevel } from '@/types'
// 优先级映射工具已删除，使用本地实现
import { sanitizeProductName, sanitizeCustomerName, sanitizeOrderNumber, sanitizeWorkstationCode, sanitizeText } from '@/utils/security/htmlSanitizer'
import { WorkOrderStatusTag, CreditLevelTag, getStatusOptions } from '@/components/common/UnifiedTagRenderer'
import { SimpleProductWeight } from '@/components/common/ProductWeightDisplay'
import dayjs from 'dayjs'
import { dataAccessManager } from '@/services/dataAccess/DataAccessManager'

const { Search } = Input
const { Option } = Select
const { RangePicker } = DatePicker
const { Text } = Typography

// ✅ 定义ProductItem接口，替换any类型使用
interface ProductItem {
  productName: string
  productCode: string
  quantity: number
}

interface ProductionWorkOrdersListProps {
  workOrders: ProductionWorkOrder[]
  loading?: boolean
  onRefresh?: () => void
  onWorkOrderDetail?: (workOrder: ProductionWorkOrder) => void
  onStatusChange?: (workOrderId: string, newStatus: string) => void
  onEdit?: (workOrder: ProductionWorkOrder) => void
  onDelete?: (workOrderId: string) => void
  onExport?: () => void
  onStartScheduling?: (selectedWorkOrderIds: string[]) => void
}

const ProductionWorkOrdersList: React.FC<ProductionWorkOrdersListProps> = ({
  workOrders = [],
  loading = false,
  onRefresh,
  onWorkOrderDetail,
  onStatusChange,
  onEdit,
  onDelete,
  onExport,
  onStartScheduling
}) => {
  const { modal } = App.useApp()
  // 移除 filteredData 状态，改用 useMemo 计算
  const [searchText, setSearchText] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [creditLevelFilter, setCreditLevelFilter] = useState<string>('')
  const [workstationFilter, setWorkstationFilter] = useState<string>('')
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null)
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])

  // 安全的搜索文本处理
  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const sanitized = sanitizeText(e.target.value, 100) // 限制100字符，防止输入攻击
    setSearchText(sanitized)
  }, [])

  // 安全的工位过滤处理
  const handleWorkstationFilterChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const sanitized = sanitizeWorkstationCode(e.target.value)
    setWorkstationFilter(sanitized)
  }, [])

  // ✅ 性能优化：使用useMemo分离过滤逻辑
  const filteredData = useMemo(() => {
    let filtered = [...workOrders]

    // 搜索过滤
    if (searchText) {
      const lowerSearchText = searchText.toLowerCase()
      filtered = filtered.filter(item =>
        item.batchNumber.toLowerCase().includes(lowerSearchText) ||
        item.productName.toLowerCase().includes(lowerSearchText) ||
        item.productCode.toLowerCase().includes(lowerSearchText) ||
        item.customerName.toLowerCase().includes(lowerSearchText)
      )
    }

    // 状态过滤
    if (statusFilter) {
      filtered = filtered.filter(item => item.status === statusFilter)
    }

    // 信用等级过滤  
    if (creditLevelFilter) {
      filtered = filtered.filter(item => item.customerCreditLevel === creditLevelFilter)
    }

    // 工位过滤
    if (workstationFilter) {
      filtered = filtered.filter(item => item.workstation.includes(workstationFilter))
    }

    // 日期范围过滤
    if (dateRange) {
      const [startDate, endDate] = dateRange
      filtered = filtered.filter(item => {
        const itemDate = dayjs(item.plannedStartTime)
        return itemDate.isAfter(startDate.startOf('day')) && itemDate.isBefore(endDate.endOf('day'))
      })
    }

    return filtered
  }, [workOrders, searchText, statusFilter, creditLevelFilter, workstationFilter, dateRange])

  // ✅ 性能优化：使用useMemo计算统计数据
  const statisticsData = useMemo(() => ({
    total: filteredData.length,
    inProgress: filteredData.filter(item => item.status === 'in_progress').length,
    completed: filteredData.filter(item => item.status === 'completed').length,
    exception: filteredData.filter(item => item.status === 'exception' || item.exceptionCount > 0).length
  }), [filteredData])

  // 使用统一的状态标签渲染
  const renderStatusTag = (status: string) => (
    <WorkOrderStatusTag status={status} />
  )

  // 使用统一的信用等级标签渲染
  const renderCreditLevelTag = (creditLevel: CustomerLevel) => {
    if (!creditLevel) return <Tag color="default">未设置</Tag>
    return <CreditLevelTag level={creditLevel} />
  }

  // 执行比进度条渲染
  const renderExecutionRate = (rate: number) => {
    let status: 'success' | 'exception' | 'normal' = 'normal'
    if (rate >= 100) status = 'success'
    else if (rate < 50) status = 'exception'
    
    return (
      <Progress
        percent={rate}
        size="small"
        status={status}
        format={percent => `${percent?.toFixed(1)}%`}
      />
    )
  }

  // 异常数量渲染
  const renderExceptionCount = (count: number) => {
    if (count > 0) {
      return <span style={{ color: '#ff4d4f', fontWeight: 'bold' }}>{count}</span>
    }
    return <span>{count}</span>
  }

  // 操作菜单
  const getActionMenu = (record: ProductionWorkOrder) => ({
    items: [
      {
        key: 'detail',
        icon: <EyeOutlined />,
        label: '查看详情',
        onClick: () => onWorkOrderDetail?.(record)
      },
      {
        key: 'edit',
        icon: <EditOutlined />,
        label: '编辑',
        onClick: () => onEdit?.(record)
      },
      {
        type: 'divider' as const
      },
      {
        key: 'delete',
        icon: <DeleteOutlined />,
        label: '删除',
        danger: true,
        onClick: () => {
          modal.confirm({
            title: '确认删除',
            content: `确定要删除工单 ${record.batchNumber} 吗？`,
            onOk: () => onDelete?.(record.id)
          })
        }
      }
    ]
  })

  // 表格列定义
  const columns: ColumnsType<ProductionWorkOrder> = [
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: renderStatusTag,
      filters: getStatusOptions('work_order'),
      onFilter: (value, record) => record.status === value
    },
    {
      title: '生产工单ID',
      dataIndex: 'id',
      key: 'id',
      width: 160,
      render: (text: string) => (
        <Text code style={{ fontSize: '12px' }}>
          {text}
        </Text>
      )
    },
    {
      title: '批次号',
      dataIndex: 'batchNumber',
      key: 'batchNumber',
      width: 120,
      render: (text, record) => (
        <Button
          type="link"
          onClick={() => onWorkOrderDetail?.(record)}
          style={{ padding: 0, height: 'auto' }}
        >
          {text}
        </Button>
      )
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      key: 'productName',
      width: 200,
      render: (productName: string, record: ProductionWorkOrder) => { // ✅ 使用正确的类型
        // 如果是共享模具工单，显示实际产品名称组合
        if (record.isSharedMold && record.productItems && record.productItems.length > 0) {
          return (
            <div style={{ lineHeight: '1.4' }}>
              {record.productItems.map((item: any, index: number) => (
                <div key={index} style={{ marginBottom: index < record.productItems.length - 1 ? '2px' : '0' }}>
                  <Text
                    style={{
                      fontSize: '13px',
                      display: 'block',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      maxWidth: '180px'
                    }}
                    title={sanitizeProductName(item.productName)} // ✅ 安全的鼠标悬停显示
                  >
                    {sanitizeProductName(item.productName)}
                  </Text>
                </div>
              ))}
            </div>
          )
        }
        // 传统工单保持原有显示方式
        return <Text ellipsis={{ tooltip: sanitizeProductName(productName) }}>{sanitizeProductName(productName)}</Text>
      },
    },
    {
      title: '产品编码',
      dataIndex: 'productCode',
      key: 'productCode',
      width: 120,
      render: (productCode: string, record: ProductionWorkOrder) => { // ✅ 使用正确的类型
        // 如果是共享模具工单，显示所有产品编码
        if (record.isSharedMold && record.productItems && record.productItems.length > 0) {
          return (
            <div style={{ lineHeight: '1.4' }}>
              {record.productItems.map((item: ProductItem, index: number) => (
                <div key={index} style={{ marginBottom: index < record.productItems.length - 1 ? '2px' : '0' }}>
                  <Text style={{ fontSize: '13px' }}>{sanitizeProductName(item.productCode)}</Text>
                </div>
              ))}
            </div>
          )
        }
        // 传统工单保持原有显示方式
        return <Text>{sanitizeProductName(productCode)}</Text>
      },
    },

    {
      title: '产品重量(g)',
      dataIndex: 'productCode',
      key: 'weight',
      width: 120,
      align: 'right',
      render: (productCode: string, record: ProductionWorkOrder) => { // ✅ 使用正确的类型
        // 如果是共享模具工单，显示所有产品重量
        if (record.isSharedMold && record.productItems && record.productItems.length > 0) {
          return (
            <div style={{ lineHeight: '1.4' }}>
              {record.productItems.map((item: ProductItem, index: number) => (
                <div key={index} style={{ marginBottom: index < record.productItems.length - 1 ? '2px' : '0' }}>
                  <Text style={{ fontSize: '13px' }}>
                    <SimpleProductWeight
                      productCode={item.productCode}
                    />
                  </Text>
                </div>
              ))}
            </div>
          )
        }
        // 传统工单保持原有显示方式
        return (
          <SimpleProductWeight
            productCode={productCode}
          />
        )
      },
      // 排序功能暂时禁用，因为需要异步获取重量数据
      // 可以考虑在未来版本中实现基于服务端排序的功能
      sorter: false
    },
    {
      title: '信用等级',
      dataIndex: 'customerCreditLevel',
      key: 'customerCreditLevel',
      width: 100,
      render: renderCreditLevelTag,
      filters: [
        { text: 'A级', value: 'A' },
        { text: 'B级', value: 'B' },
        { text: 'C级', value: 'C' },
        { text: 'D级', value: 'D' },
        { text: 'E级', value: 'E' }
      ],
      onFilter: (value, record) => record.customerCreditLevel === value
    },
    {
      title: '计划模数',
      dataIndex: 'plannedMoldCount',
      key: 'plannedMoldCount',
      width: 100,
      align: 'right',
      sorter: (a, b) => a.plannedMoldCount - b.plannedMoldCount
    },
    {
      title: '工位',
      dataIndex: 'workstation',
      key: 'workstation',
      width: 100
    },
    {
      title: '计划开始',
      dataIndex: 'plannedStartTime',
      key: 'plannedStartTime',
      width: 140,
      render: (text: string | undefined) => {
        if (!text) {
          return <span style={{ color: '#999' }}>未设置</span>
        }
        return dayjs(text).format('YYYY-MM-DD HH:mm')
      },
      sorter: (a, b) => {
        // 处理空值排序：空值排在最后
        if (!a.plannedStartTime && !b.plannedStartTime) return 0
        if (!a.plannedStartTime) return 1
        if (!b.plannedStartTime) return -1
        return dayjs(a.plannedStartTime).unix() - dayjs(b.plannedStartTime).unix()
      }
    },
    {
      title: '计划结束',
      dataIndex: 'plannedEndTime',
      key: 'plannedEndTime',
      width: 140,
      render: (text: string | undefined) => {
        if (!text) {
          return <span style={{ color: '#999' }}>未设置</span>
        }
        return dayjs(text).format('YYYY-MM-DD HH:mm')
      },
      sorter: (a, b) => {
        // 处理空值排序：空值排在最后
        if (!a.plannedEndTime && !b.plannedEndTime) return 0
        if (!a.plannedEndTime) return 1
        if (!b.plannedEndTime) return -1
        return dayjs(a.plannedEndTime).unix() - dayjs(b.plannedEndTime).unix()
      }
    },
    {
      title: '交货日期',
      dataIndex: 'deliveryDate',
      key: 'deliveryDate',
      width: 110,
      render: (text: string) => dayjs(text).format('YYYY-MM-DD'),
      sorter: (a, b) => dayjs(a.deliveryDate).unix() - dayjs(b.deliveryDate).unix()
    },
    {
      title: '小时产能',
      dataIndex: 'hourlyCapacity',
      key: 'hourlyCapacity',
      width: 120,
      align: 'right',
      render: (capacity: number) => `${capacity}模/小时`,
      sorter: (a, b) => a.hourlyCapacity - b.hourlyCapacity
    },
    {
      title: '完成模数',
      dataIndex: 'completedMoldCount',
      key: 'completedMoldCount',
      width: 100,
      align: 'right',
      sorter: (a, b) => a.completedMoldCount - b.completedMoldCount
    },
    {
      title: '执行比(%)',
      dataIndex: 'executionRate',
      key: 'executionRate',
      width: 120,
      render: renderExecutionRate,
      sorter: (a, b) => a.executionRate - b.executionRate
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 140,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm'),
      sorter: (a: ProductionWorkOrder, b: ProductionWorkOrder) => dayjs(a.createdAt).unix() - dayjs(b.createdAt).unix(),
      defaultSortOrder: 'descend' as const,
    },
    {
      title: '成型模具编号',
      dataIndex: 'formingMoldNumber',
      key: 'formingMoldNumber',
      width: 140,
      render: (moldNumber: string) => moldNumber || '-',
    },
    {
      title: '异常数量',
      dataIndex: 'exceptionCount',
      key: 'exceptionCount',
      width: 100,
      align: 'right',
      render: renderExceptionCount,
      sorter: (a, b) => a.exceptionCount - b.exceptionCount
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      fixed: 'right',
      render: (_, record) => (
        <Dropdown menu={getActionMenu(record)} trigger={['click']}>
          <Button type="text" icon={<MoreOutlined />} />
        </Dropdown>
      )
    }
  ]

  // 行选择配置
  const rowSelection: TableProps<ProductionWorkOrder>['rowSelection'] = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
    getCheckboxProps: (record) => ({
      disabled: record.status !== 'pending', // 只有"待开始"状态的工单可以被选择
      name: record.batchNumber,
    }),
    selections: [
      {
        key: 'select-pending',
        text: '选择待开始工单',
        onSelect: (changeableRowKeys) => {
          const pendingKeys = changeableRowKeys.filter(key => {
            const workOrder = filteredData.find(wo => wo.id === key)
            return workOrder?.status === 'pending'
          })
          setSelectedRowKeys(pendingKeys)
        },
      },
      Table.SELECTION_NONE
    ]
  }

  // 清空筛选
  const handleClearFilters = () => {
    setSearchText('')
    setStatusFilter('')
    setCreditLevelFilter('')
    setWorkstationFilter('')
    setDateRange(null)
  }



  return (
    <div className="production-work-orders-list">
      {/* 工具栏 */}
      <Card size="small" style={{ marginBottom: '16px' }}>
        <Row gutter={16} align="middle">
          <Col flex="auto">
            <Space wrap>
              <Search
                placeholder="搜索批次号、产品名称、产品编码、客户名称"
                value={searchText}
                onChange={handleSearchChange}
                style={{ width: 300 }}
                allowClear
              />
              <Select
                placeholder="状态"
                value={statusFilter}
                onChange={setStatusFilter}
                style={{ width: 120 }}
                allowClear
              >
                <Option value="pending">待开始</Option>
                <Option value="in_progress">进行中</Option>
                <Option value="completed">已完成</Option>
                <Option value="paused">暂停</Option>
                <Option value="cancelled">已取消</Option>
                <Option value="exception">异常</Option>
              </Select>
              <Select
                placeholder="信用等级"
                value={creditLevelFilter}
                onChange={setCreditLevelFilter}
                style={{ width: 100 }}
                allowClear
              >
                <Option value="A">A级</Option>
                <Option value="B">B级</Option>
                <Option value="C">C级</Option>
                <Option value="D">D级</Option>
                <Option value="E">E级</Option>
              </Select>
              <Input
                placeholder="工位"
                value={workstationFilter}
                onChange={handleWorkstationFilterChange}
                style={{ width: 120 }}
                allowClear
              />
              <RangePicker
                value={dateRange}
                onChange={(dates) => setDateRange(dates as [Dayjs, Dayjs] | null)}
                placeholder={['开始日期', '结束日期']}
              />
              <Button
                icon={<FilterOutlined />}
                onClick={handleClearFilters}
              >
                清空筛选
              </Button>
            </Space>
          </Col>
          <Col>
            <Space>
              <Button
                type="primary"
                onClick={() => onStartScheduling?.(selectedRowKeys as string[])}
                disabled={selectedRowKeys.length === 0}
              >
                开始排单 {selectedRowKeys.length > 0 && `(${selectedRowKeys.length})`}
              </Button>
              <Button
                icon={<ExportOutlined />}
                onClick={onExport}
              >
                导出
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={onRefresh}
                loading={loading}
              >
                刷新
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 统计信息 */}
      <Row gutter={16} style={{ marginBottom: '16px' }}>
        <Col span={6}>
          <Card size="small">
            <Statistic
              title="总工单数"
              value={statisticsData.total}
              prefix={<Badge status="default" />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <Statistic
              title="进行中"
              value={statisticsData.inProgress}
              prefix={<Badge status="processing" />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <Statistic
              title="已完成"
              value={statisticsData.completed}
              prefix={<Badge status="success" />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <Statistic
              title="异常工单"
              value={statisticsData.exception}
              prefix={<Badge status="error" />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 数据表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={filteredData}
          rowKey="id"
          loading={loading}
          rowSelection={rowSelection}
          scroll={{ x: 1900 }}
          pagination={{
            total: filteredData.length,
            pageSize: 20,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
          }}
          size="small"
        />
      </Card>
    </div>
  )
}

export default ProductionWorkOrdersList
