---
type: "always_apply"
---

# Add to Changelog

Update the project's CHANGELOG.md file with a new entry.

## Usage:
`/add-to-changelog <version> <change_type> <message>`

## Parameters:
- `<version>`: Version number (e.g., "1.1.0")
- `<change_type>`: One of: "added", "changed", "deprecated", "removed", "fixed", "security"
- `<message>`: Description of the change

## Examples:
- `/add-to-changelog 1.1.0 added "New markdown to BlockDoc conversion feature"`
- `/add-to-changelog 1.0.2 fixed "Bug in HTML renderer causing incorrect output"`

## Steps:
1. Check for existing CHANGELOG.md or create if missing
2. Find or create section for the specified version
3. Add the new entry under the appropriate change type
4. Format according to Keep a Changelog conventions
5. Write the updated changelog back to file
6. Optionally commit the changes with appropriate message

## Format:
Follow [Keep a Changelog](https://keepachangelog.com) format:
- Group changes by type
- List changes as bullet points
- Include date for version sections
- Keep entries concise but descriptive