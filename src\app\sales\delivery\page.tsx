'use client'

import React, { useState, useEffect } from 'react'
import { 
  Card, 
  Table, 
  Button, 
  Space, 
  Tag, 
  Input, 
  Select, 
  Modal,
  Form,
  InputNumber,
  Row,
  Col,
  Statistic,
  Progress,
  message,
  Popconfirm,
  Descriptions,
  Alert,
  Badge,
  Steps,
  Timeline} from 'antd'
import { 
  PlusOutlined, 
  SearchOutlined, 
  EditOutlined, 
  DeleteOutlined,
  EyeOutlined,
  ExportOutlined,
  TruckOutlined,
  InboxOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  WarningOutlined,
  ScanOutlined,
  CalculatorOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import { DeliveryNote, DeliveryItem} from '@/types'
import { styleHelpers } from '@/utils/styles/antdHelpers'
import FormSelect from '@/components/FormSelect'
import dayjs from 'dayjs'

const { Option } = Select
const { TextArea } = Input
const { Step } = Steps

const DeliveryManagement: React.FC = () => {
  const [deliveryNotes, setDeliveryNotes] = useState<DeliveryNote[]>([])
  const [loading, setLoading] = useState(false)
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false)
  const [isTaskModalVisible, setIsTaskModalVisible] = useState(false)
  const [editingDelivery, setEditingDelivery] = useState<DeliveryNote | null>(null)
  const [selectedDelivery, setSelectedDelivery] = useState<DeliveryNote | null>(null)
  const [form] = Form.useForm()
  const [searchText, setSearchText] = useState('')
  const [filterStatus, setFilterStatus] = useState<string | undefined>(undefined)

  // 模拟数据
  useEffect(() => {
    const mockDeliveryNotes: DeliveryNote[] = [
      {
        id: '1',
        deliveryNumber: 'DN-2024-001',
        orderNumber: 'SO-2024-001',
        customerId: '1',
        customerName: '上海包装材料有限公司',
        deliveryDate: '2024-01-25',
        deliveryAddress: '上海市浦东新区张江高科技园区',
        contactPerson: '张经理',
        contactPhone: '13800138001',
        driverName: '李师傅',
        vehicleNumber: '沪A12345',
        vehicleType: '厢式货车',
        totalWeight: 2.5,
        totalVolume: 15.6,
        loadingRate: 92,
        status: 'shipped',
        items: [
          {
            id: '1',
            deliveryNumber: 'DN-2024-001',
            orderItemId: '1',
            productModelCode: 'CP-202',
            productName: '圆形餐盘202mm',
            plannedQuantity: 3000,
            actualQuantity: 3000,
            unit: '个',
            batchNumber: '*********-001',
            warehouseLocation: 'A01-01-01',
            palletNumber: 'PLT-001',
            remainingQuantity: 0
          }
        ],
        warehouseTasks: [
          {
            id: '1',
            deliveryNumber: 'DN-2024-001',
            taskType: 'pick',
            productModelCode: 'CP-202',
            quantity: 3000,
            fromLocation: 'A01-01-01',
            recommendedLocation: 'A01-01-01',
            priority: 'high',
            status: 'completed',
            operator: '仓管员A',
            completedAt: '2024-01-25T08:30:00'
          }
        ],
        remark: '优先发货，客户急需',
        createdAt: '2024-01-24T16:00:00',
        updatedAt: '2024-01-25T10:00:00'
      },
      {
        id: '2',
        deliveryNumber: 'DN-2024-002',
        orderNumber: 'SO-2024-002',
        customerId: '2',
        customerName: '北京绿色包装科技公司',
        deliveryDate: '2024-01-28',
        deliveryAddress: '北京市朝阳区CBD商务区',
        contactPerson: '王总',
        contactPhone: '13900139002',
        driverName: '张师傅',
        vehicleNumber: '京B67890',
        vehicleType: '平板货车',
        totalWeight: 4.2,
        totalVolume: 28.5,
        loadingRate: 85,
        status: 'loading',
        items: [
          {
            id: '2',
            deliveryNumber: 'DN-2024-002',
            orderItemId: '2',
            productModelCode: 'CP-201',
            productName: '方形餐盒180mm',
            plannedQuantity: 20000,
            actualQuantity: 19800,
            unit: '个',
            batchNumber: '*********-001',
            warehouseLocation: 'B02-02-03',
            palletNumber: 'PLT-002',
            remainingQuantity: 200,
            remark: '有200个余量需退回'
          }
        ],
        warehouseTasks: [
          {
            id: '2',
            deliveryNumber: 'DN-2024-002',
            taskType: 'pick',
            productModelCode: 'CP-201',
            quantity: 20000,
            fromLocation: 'B02-02-03',
            recommendedLocation: 'B02-02-03',
            priority: 'medium',
            status: 'in_progress',
            operator: '仓管员B'
          }
        ],
        createdAt: '2024-01-26T09:00:00',
        updatedAt: '2024-01-28T14:00:00'
      }
    ]
    setDeliveryNotes(mockDeliveryNotes)
  }, [])

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const statusMap = {
      'pending': { color: 'orange', text: '待发货', icon: <ClockCircleOutlined /> },
      'loading': { color: 'blue', text: '装货中', icon: <InboxOutlined /> },
      'shipped': { color: 'green', text: '已发货', icon: <TruckOutlined /> },
      'delivered': { color: 'cyan', text: '已送达', icon: <CheckCircleOutlined /> },
      'returned': { color: 'red', text: '已退回', icon: <WarningOutlined /> }
    }
    const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: '未知', icon: null }
    return (
      <Tag color={config.color} icon={config.icon}>
        {config.text}
      </Tag>
    )
  }

  // 获取任务状态标签
  const getTaskStatusTag = (status: string) => {
    const statusMap = {
      'pending': { color: 'default', text: '待处理' },
      'in_progress': { color: 'processing', text: '进行中' },
      'completed': { color: 'success', text: '已完成' }
    }
    const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: '未知' }
    return <Badge status={config.color as any} text={config.text} />
  }

  // 获取任务优先级标签
  const getPriorityTag = (priority: string) => {
    const priorityMap = {
      'high': { color: 'red', text: '高' },
      'medium': { color: 'orange', text: '中' },
      'low': { color: 'green', text: '低' }
    }
    const config = priorityMap[priority as keyof typeof priorityMap] || { color: 'default', text: '未知' }
    return <Tag color={config.color}>{config.text}</Tag>
  }

  // 智能配载建议
  const getLoadingSuggestion = (totalWeight: number, totalVolume: number, loadingRate: number): {
    type: 'success' | 'warning' | 'error';
    message: string;
  } => {
    if (loadingRate >= 90) {
      return { type: 'success', message: '装载率优秀，配载合理' }
    } else if (loadingRate >= 80) {
      return { type: 'warning', message: '装载率良好，可考虑合并其他订单' }
    } else {
      return { type: 'error', message: '装载率偏低，建议优化配载方案' }
    }
  }

  // 表格列定义
  const columns: ColumnsType<DeliveryNote> = [
    {
      title: '送货单号',
      dataIndex: 'deliveryNumber',
      key: 'deliveryNumber',
      width: 140,
      fixed: 'left'
    },
    {
      title: '关联订单',
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      width: 140
    },
    {
      title: '客户名称',
      dataIndex: 'customerName',
      key: 'customerName',
      width: 180,
      ellipsis: true
    },
    {
      title: '发货日期',
      dataIndex: 'deliveryDate',
      key: 'deliveryDate',
      width: 120,
      sorter: (a, b) => new Date(a.deliveryDate).getTime() - new Date(b.deliveryDate).getTime()
    },
    {
      title: '车辆信息',
      key: 'vehicleInfo',
      width: 150,
      render: (_, record) => (
        <div>
          <div>{record.vehicleNumber}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.driverName} | {record.vehicleType}
          </div>
        </div>
      )
    },
    {
      title: '装载信息',
      key: 'loadingInfo',
      width: 150,
      render: (_, record) => {
        const suggestion = getLoadingSuggestion(record.totalWeight, record.totalVolume, record.loadingRate)
        return (
          <div>
            <div>{record.totalWeight}吨 | {record.totalVolume}m³</div>
            <div>
              <Progress 
                percent={record.loadingRate} 
                size="small" 
                status={suggestion.type === 'success' ? 'success' : 
                        suggestion.type === 'warning' ? 'normal' : 'exception'}
              />
            </div>
          </div>
        )
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => getStatusTag(status)
    },
    {
      title: '仓库任务',
      key: 'taskStatus',
      width: 120,
      render: (_, record) => {
        const completedTasks = record.warehouseTasks.filter(t => t.status === 'completed').length
        const totalTasks = record.warehouseTasks.length
        return (
          <div>
            <div>{completedTasks}/{totalTasks}</div>
            <Progress 
              percent={totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0} 
              size="small" 
              showInfo={false}
            />
          </div>
        )
      }
    },
    {
      title: '操作',
      key: 'action',
      width: 250,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button 
            type="link" 
            icon={<EyeOutlined />} 
            onClick={() => handleViewDetail(record)}
          >
            详情
          </Button>
          <Button 
            type="link" 
            icon={<EditOutlined />} 
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button 
            type="link" 
            icon={<ScanOutlined />} 
            onClick={() => handleViewTasks(record)}
          >
            任务
          </Button>
          <Popconfirm
            title="确定要删除这个送货单吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ]

  // 过滤后的送货单数据
  const filteredDeliveryNotes = deliveryNotes.filter(delivery => {
    const matchesSearch = !searchText || 
      delivery.deliveryNumber.toLowerCase().includes(searchText.toLowerCase()) ||
      delivery.orderNumber.toLowerCase().includes(searchText.toLowerCase()) ||
      delivery.customerName.toLowerCase().includes(searchText.toLowerCase())
    
    const matchesStatus = !filterStatus || delivery.status === filterStatus
    
    return matchesSearch && matchesStatus
  })

  // 统计数据
  const stats = {
    total: deliveryNotes.length,
    pending: deliveryNotes.filter(d => d.status === 'pending').length,
    loading: deliveryNotes.filter(d => d.status === 'loading').length,
    shipped: deliveryNotes.filter(d => d.status === 'shipped').length,
    averageLoadingRate: deliveryNotes.length > 0 ? 
      Math.round(deliveryNotes.reduce((sum, d) => sum + d.loadingRate, 0) / deliveryNotes.length) : 0
  }

  const handleCreate = () => {
    setEditingDelivery(null)
    setIsModalVisible(true)
    form.resetFields()
  }

  const handleEdit = (delivery: DeliveryNote) => {
    setEditingDelivery(delivery)
    setIsModalVisible(true)
    form.setFieldsValue({
      ...delivery,
      deliveryDate: dayjs(delivery.deliveryDate)
    })
  }

  const handleViewDetail = (delivery: DeliveryNote) => {
    setSelectedDelivery(delivery)
    setIsDetailModalVisible(true)
  }

  const handleViewTasks = (delivery: DeliveryNote) => {
    setSelectedDelivery(delivery)
    setIsTaskModalVisible(true)
  }

  const handleDelete = (id: string) => {
    setDeliveryNotes(deliveryNotes.filter(d => d.id !== id))
    message.success('送货单删除成功')
  }

  const handleModalOk = () => {
    form.validateFields().then(values => {
      const now = new Date().toISOString()
      const formattedValues = {
        ...values,
        deliveryDate: values.deliveryDate.format('YYYY-MM-DD')
      }

      if (editingDelivery) {
        const updatedDeliveryNotes = deliveryNotes.map(d =>
          d.id === editingDelivery.id
            ? { ...d, ...formattedValues, updatedAt: now }
            : d
        )
        setDeliveryNotes(updatedDeliveryNotes)
        message.success('送货单更新成功')
      } else {
        const newDelivery: DeliveryNote = {
          id: Date.now().toString(),
          deliveryNumber: `DN-${new Date().getFullYear()}-${String(deliveryNotes.length + 1).padStart(3, '0')}`,
          ...formattedValues,
          items: [],
          warehouseTasks: [],
          createdAt: now,
          updatedAt: now
        }
        setDeliveryNotes([...deliveryNotes, newDelivery])
        message.success('送货单创建成功')
      }
      setIsModalVisible(false)
      form.resetFields()
    })
  }

  const handleModalCancel = () => {
    setIsModalVisible(false)
    form.resetFields()
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: styleHelpers.spacing.lg }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: '24px' }}>
        <h1 style={{ fontSize: '24px', fontWeight: 'bold', margin: '0 0 8px 0' }}>发货执行管理</h1>
        <p style={{ color: '#666', margin: 0 }}>智能配载、仓库任务、出库扫码、余量管理</p>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="送货单总数"
              value={stats.total}
              suffix="个"
              prefix={<TruckOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="待发货"
              value={stats.pending}
              suffix="个"
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="装货中"
              value={stats.loading}
              suffix="个"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="平均装载率"
              value={stats.averageLoadingRate}
              suffix="%"
              valueStyle={{ color: stats.averageLoadingRate >= 85 ? '#3f8600' : '#cf1322' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 操作区域 */}
      <Card>
        <Row gutter={[16, 16]} align="middle" justify="space-between">
          <Col xs={24} lg={18}>
            <Space wrap size="middle">
            <Input
              placeholder="搜索送货单号、订单号或客户名称"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: '256px' }}
            />
            <FormSelect
              placeholder="发货状态"
              value={filterStatus}
              onChange={setFilterStatus}
              style={{ width: '128px' }}
              allowClear
            >
              <Option value="pending">待发货</Option>
              <Option value="loading">装货中</Option>
              <Option value="shipped">已发货</Option>
              <Option value="delivered">已送达</Option>
              <Option value="returned">已退回</Option>
            </FormSelect>
            </Space>
          </Col>
          <Col xs={24} lg={6}>
            <Space>
              <Button icon={<ExportOutlined />}>导出</Button>
              <Button icon={<CalculatorOutlined />}>配载优化</Button>
              <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
                新建送货单
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 送货单列表 */}
      <Card title="送货单列表">
        <Table
          columns={columns}
          dataSource={filteredDeliveryNotes}
          rowKey="id"
          loading={loading}
          pagination={{
            total: filteredDeliveryNotes.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            pageSizeOptions: ['10', '20', '50', '100'],
          }}
          scroll={{ x: 1400 }}
        />
      </Card>

      {/* 新建/编辑送货单模态框 */}
      <Modal
        title={editingDelivery ? '编辑送货单' : '新建送货单'}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={800}
        okText="确认"
        cancelText="取消"
      >
        <Alert
          message="智能发货流程"
          description="系统将自动推送仓库任务、推荐库位、计算装载率并生成配载建议"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Form
          form={form}
          layout="vertical"
          initialValues={{
            status: 'pending'
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="orderNumber"
                label="关联订单号"
                rules={[{ required: true, message: '请输入关联订单号' }]}
              >
                <Input placeholder="请输入关联订单号" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="deliveryDate"
                label="发货日期"
                rules={[{ required: true, message: '请选择发货日期' }]}
              >
                <Input placeholder="请选择发货日期" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="customerId"
                label="客户ID"
                rules={[{ required: true, message: '请输入客户ID' }]}
              >
                <Input placeholder="请输入客户ID" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="customerName"
                label="客户名称"
                rules={[{ required: true, message: '请输入客户名称' }]}
              >
                <Input placeholder="请输入客户名称" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="contactPerson"
                label="联系人"
                rules={[{ required: true, message: '请输入联系人' }]}
              >
                <Input placeholder="请输入联系人" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="deliveryAddress"
            label="送货地址"
            rules={[{ required: true, message: '请输入送货地址' }]}
          >
            <Input placeholder="请输入详细送货地址" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="contactPhone"
                label="联系电话"
                rules={[{ required: true, message: '请输入联系电话' }]}
              >
                <Input placeholder="请输入联系电话" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="driverName"
                label="司机姓名"
                rules={[{ required: true, message: '请输入司机姓名' }]}
              >
                <Input placeholder="请输入司机姓名" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="vehicleNumber"
                label="车牌号"
                rules={[{ required: true, message: '请输入车牌号' }]}
              >
                <Input placeholder="请输入车牌号" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="vehicleType"
                label="车型"
                rules={[{ required: true, message: '请选择车型' }]}
              >
                <Select placeholder="请选择车型">
                  <Option value="厢式货车">厢式货车</Option>
                  <Option value="平板货车">平板货车</Option>
                  <Option value="集装箱车">集装箱车</Option>
                  <Option value="冷藏车">冷藏车</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="totalWeight"
                label="总重量(吨)"
                rules={[{ required: true, message: '请输入总重量' }]}
              >
                <InputNumber
                  min={0}
                  step={0.1}
                  style={{ width: '100%' }}
                  placeholder="请输入总重量"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="totalVolume"
                label="总体积(m³)"
                rules={[{ required: true, message: '请输入总体积' }]}
              >
                <InputNumber
                  min={0}
                  step={0.1}
                  style={{ width: '100%' }}
                  placeholder="请输入总体积"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="remark"
            label="备注"
          >
            <TextArea rows={3} placeholder="请输入备注信息" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 送货单详情模态框 */}
      <Modal
        title="送货单详情"
        open={isDetailModalVisible}
        onCancel={() => setIsDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setIsDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={1000}
      >
        {selectedDelivery && selectedDelivery.deliveryNumber && selectedDelivery.orderNumber && (
          <div>
            <Descriptions column={3} bordered>
              <Descriptions.Item label="送货单号">{selectedDelivery.deliveryNumber}</Descriptions.Item>
              <Descriptions.Item label="关联订单">{selectedDelivery.orderNumber}</Descriptions.Item>
              <Descriptions.Item label="客户名称">{selectedDelivery.customerName}</Descriptions.Item>
              <Descriptions.Item label="发货日期">{selectedDelivery.deliveryDate}</Descriptions.Item>
              <Descriptions.Item label="联系人">{selectedDelivery.contactPerson}</Descriptions.Item>
              <Descriptions.Item label="联系电话">{selectedDelivery.contactPhone}</Descriptions.Item>
              <Descriptions.Item label="司机姓名">{selectedDelivery.driverName}</Descriptions.Item>
              <Descriptions.Item label="车牌号">{selectedDelivery.vehicleNumber}</Descriptions.Item>
              <Descriptions.Item label="车型">{selectedDelivery.vehicleType}</Descriptions.Item>
              <Descriptions.Item label="总重量">{selectedDelivery.totalWeight}吨</Descriptions.Item>
              <Descriptions.Item label="总体积">{selectedDelivery.totalVolume}m³</Descriptions.Item>
              <Descriptions.Item label="装载率">
                <span style={{
                  color: selectedDelivery.loadingRate >= 85 ? '#3f8600' : '#cf1322',
                  fontWeight: 'bold'
                }}>
                  {selectedDelivery.loadingRate}%
                </span>
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                {getStatusTag(selectedDelivery.status)}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {new Date(selectedDelivery.createdAt).toLocaleString()}
              </Descriptions.Item>
              <Descriptions.Item label="更新时间">
                {new Date(selectedDelivery.updatedAt).toLocaleString()}
              </Descriptions.Item>
              <Descriptions.Item label="送货地址" span={3}>
                {selectedDelivery.deliveryAddress}
              </Descriptions.Item>
              <Descriptions.Item label="备注" span={3}>
                {selectedDelivery.remark || '无'}
              </Descriptions.Item>
            </Descriptions>

            {/* 配载建议 */}
            <div style={{ marginTop: 16 }}>
              <h4>配载分析</h4>
              {(() => {
                const suggestion = getLoadingSuggestion(
                  selectedDelivery.totalWeight,
                  selectedDelivery.totalVolume,
                  selectedDelivery.loadingRate
                )
                return (
                  <Alert
                    message={`装载率: ${selectedDelivery.loadingRate}%`}
                    description={suggestion.message}
                    type={suggestion.type}
                    showIcon
                  />
                )
              })()}
            </div>

            {/* 发货明细 */}
            <div style={{ marginTop: 16 }}>
              <h4>发货明细</h4>
              <Table
                dataSource={selectedDelivery.items}
                rowKey="id"
                pagination={false}
                size="small"
                columns={[
                  {
                    title: '产品型号',
                    dataIndex: 'productModelCode',
                    key: 'productModelCode',
                    width: 120
                  },
                  {
                    title: '产品名称',
                    dataIndex: 'productName',
                    key: 'productName',
                    width: 150
                  },
                  {
                    title: '计划数量',
                    dataIndex: 'plannedQuantity',
                    key: 'plannedQuantity',
                    width: 100,
                    render: (quantity: number, record: DeliveryItem) =>
                      `${quantity.toLocaleString()} ${record.unit}`
                  },
                  {
                    title: '实装数量',
                    dataIndex: 'actualQuantity',
                    key: 'actualQuantity',
                    width: 100,
                    render: (quantity: number, record: DeliveryItem) =>
                      `${quantity.toLocaleString()} ${record.unit}`
                  },
                  {
                    title: '余量',
                    dataIndex: 'remainingQuantity',
                    key: 'remainingQuantity',
                    width: 100,
                    render: (quantity: number, record: DeliveryItem) => (
                      <span style={{ color: quantity > 0 ? '#faad14' : '#52c41a' }}>
                        {quantity.toLocaleString()} {record.unit}
                      </span>
                    )
                  },
                  {
                    title: '批次号',
                    dataIndex: 'batchNumber',
                    key: 'batchNumber',
                    width: 120
                  },
                  {
                    title: '库位',
                    dataIndex: 'warehouseLocation',
                    key: 'warehouseLocation',
                    width: 100
                  },
                  {
                    title: '托盘号',
                    dataIndex: 'palletNumber',
                    key: 'palletNumber',
                    width: 100
                  }
                ]}
              />
            </div>
          </div>
        )}
      </Modal>

      {/* 仓库任务模态框 */}
      <Modal
        title="仓库任务管理"
        open={isTaskModalVisible}
        onCancel={() => setIsTaskModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setIsTaskModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={900}
      >
        {selectedDelivery && (
          <div>
            <Alert
              message="任务执行流程"
              description="拣货 → 包装 → 装车 → 发货，系统自动推荐最优库位和执行路径"
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />

            <Steps size="small" style={{ marginBottom: 24 }}>
              <Step
                title="拣货"
                status="finish"
                icon={<InboxOutlined />}
                description="从库位拣取商品"
              />
              <Step
                title="包装"
                status="process"
                icon={<CheckCircleOutlined />}
                description="商品包装和标识"
              />
              <Step
                title="装车"
                status="wait"
                icon={<TruckOutlined />}
                description="装载到运输车辆"
              />
              <Step
                title="发货"
                status="wait"
                icon={<ScanOutlined />}
                description="扫码确认发货"
              />
            </Steps>

            <Table
              dataSource={selectedDelivery.warehouseTasks}
              rowKey="id"
              pagination={false}
              size="small"
              columns={[
                {
                  title: '任务类型',
                  dataIndex: 'taskType',
                  key: 'taskType',
                  width: 100,
                  render: (type: string) => {
                    const typeMap = {
                      'pick': '拣货',
                      'pack': '包装',
                      'load': '装车',
                      'return': '退货'
                    }
                    return typeMap[type as keyof typeof typeMap] || type
                  }
                },
                {
                  title: '产品型号',
                  dataIndex: 'productModelCode',
                  key: 'productModelCode',
                  width: 120
                },
                {
                  title: '数量',
                  dataIndex: 'quantity',
                  key: 'quantity',
                  width: 100,
                  render: (quantity: number) => quantity.toLocaleString()
                },
                {
                  title: '源库位',
                  dataIndex: 'fromLocation',
                  key: 'fromLocation',
                  width: 100
                },
                {
                  title: '推荐库位',
                  dataIndex: 'recommendedLocation',
                  key: 'recommendedLocation',
                  width: 100,
                  render: (location: string) => (
                    <Tag color="blue">{location}</Tag>
                  )
                },
                {
                  title: '优先级',
                  dataIndex: 'priority',
                  key: 'priority',
                  width: 80,
                  render: (priority: string) => getPriorityTag(priority)
                },
                {
                  title: '状态',
                  dataIndex: 'status',
                  key: 'status',
                  width: 100,
                  render: (status: string) => getTaskStatusTag(status)
                },
                {
                  title: '操作员',
                  dataIndex: 'operator',
                  key: 'operator',
                  width: 100
                },
                {
                  title: '完成时间',
                  dataIndex: 'completedAt',
                  key: 'completedAt',
                  width: 150,
                  render: (time: string) => time ? new Date(time).toLocaleString() : '-'
                }
              ]}
            />

            <div style={{ marginTop: 16 }}>
              <h4>任务执行时间线</h4>
              <Timeline>
                {selectedDelivery.warehouseTasks.map(task => (
                  <Timeline.Item
                    key={task.id}
                    color={task.status === 'completed' ? 'green' :
                           task.status === 'in_progress' ? 'blue' : 'gray'}
                  >
                    <div>
                      <div style={{ fontWeight: 'bold' }}>
                        {task.taskType === 'pick' ? '拣货任务' :
                         task.taskType === 'pack' ? '包装任务' :
                         task.taskType === 'load' ? '装车任务' : '退货任务'}
                      </div>
                      <div>
                        产品: {task.productModelCode} | 数量: {task.quantity.toLocaleString()}
                      </div>
                      <div style={{ fontSize: '12px', color: '#666' }}>
                        库位: {task.fromLocation} → {task.recommendedLocation}
                      </div>
                      <div style={{ fontSize: '12px', color: '#666' }}>
                        操作员: {task.operator || '待分配'} |
                        状态: {task.status === 'completed' ? '已完成' :
                              task.status === 'in_progress' ? '进行中' : '待处理'}
                      </div>
                      {task.completedAt && (
                        <div style={{ fontSize: '12px', color: '#666' }}>
                          完成时间: {new Date(task.completedAt).toLocaleString()}
                        </div>
                      )}
                    </div>
                  </Timeline.Item>
                ))}
              </Timeline>
            </div>
          </div>
        )}
      </Modal>
    </div>
  )
}

export default DeliveryManagement
