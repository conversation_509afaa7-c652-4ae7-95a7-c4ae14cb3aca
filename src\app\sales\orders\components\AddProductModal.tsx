'use client'

import React, { useState } from 'react'
import { Modal, Form, Input, InputNumber, Select, App } from 'antd'
import FormSelect from '@/components/FormSelect'
import { ProductModel } from '@/types'

const { Option } = Select
const { TextArea } = Input

interface AddProductModalProps {
  open: boolean
  onCancel: () => void
  onConfirm: (productData: Omit<ProductModel, 'id' | 'createdAt' | 'updatedAt'>) => void
  loading?: boolean
}

const AddProductModalComponent: React.FC<AddProductModalProps> = ({
  open,
  onCancel,
  onConfirm,
  loading = false
}) => {
  const [form] = Form.useForm()
  const { message } = App.useApp()

  // 产品类别选项
  const categoryOptions = [
    '紧固件',
    '垫圈',
    '螺母',
    '螺栓',
    '螺钉',
    '铆钉',
    '销钉',
    '弹簧',
    '轴承',
    '密封件',
    '其他'
  ]

  // 单位选项
  const unitOptions = [
    '个',
    '件',
    '套',
    '包',
    '盒',
    '箱',
    'kg',
    'g',
    'm',
    'cm',
    'mm'
  ]

  // 处理确认
  const handleOk = async () => {
    try {
      const values = await form.validateFields()
      
      // 生成物料编码
      const materialCode = `WL${Date.now().toString().slice(-6)}`
      
      const productData: Omit<ProductModel, 'id' | 'createdAt' | 'updatedAt'> = {
        modelCode: materialCode,
        modelName: values.modelName,
        formingMold: 'M-DEFAULT',
        formingMoldQuantity: 1,
        hotPressMold: 'M-DEFAULT',
        hotPressMoldQuantity: 1,
        formingPiecePrice: 0,
        hotPressPiecePrice: 0,
        productPrice: values.price || 0,
        productWeight: values.weight || 0,
        boxSpecification: '默认规格',
        packingQuantity: 100,
        piecesPerMold: 1,
        status: 'active'
      }

      onConfirm(productData)
      // 安全地重置表单
      if (form && typeof form.resetFields === 'function') {
        try {
          form.resetFields()
        } catch (error) {
        }
      }
      message.success('产品型号添加成功！')
    } catch (error) {
    }
  }

  // 处理取消
  const handleCancel = () => {
    // 安全地重置表单
    if (form && typeof form.resetFields === 'function') {
      try {
        form.resetFields()
      } catch (error) {
      }
    }
    onCancel()
  }

  return (
    <Modal
      title="添加新产品型号"
      open={open}
      onOk={handleOk}
      onCancel={handleCancel}
      confirmLoading={loading}
      width={600}
      okText="确认添加"
      cancelText="取消"
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          unit: '个',
          category: '紧固件',
          weight: 0.01,
          price: 1.00,
          status: 'active'
        }}
      >
        <Form.Item
          name="modelName"
          label="型号名称"
          rules={[
            { required: true, message: '请输入型号名称' },
            { min: 2, message: '型号名称至少2个字符' },
            { max: 50, message: '型号名称不能超过50个字符' }
          ]}
        >
          <Input placeholder="请输入产品型号名称" />
        </Form.Item>

        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '16px' }}>
          <Form.Item
            name="category"
            label="产品类别"
            rules={[{ required: true, message: '请选择产品类别' }]}
          >
            <FormSelect placeholder="请选择产品类别">
              {categoryOptions.map(category => (
                <Option key={category} value={category}>
                  {category}
                </Option>
              ))}
            </FormSelect>
          </Form.Item>

          <Form.Item
            name="unit"
            label="计量单位"
            rules={[{ required: true, message: '请选择计量单位' }]}
          >
            <FormSelect placeholder="请选择计量单位">
              {unitOptions.map(unit => (
                <Option key={unit} value={unit}>
                  {unit}
                </Option>
              ))}
            </FormSelect>
          </Form.Item>
        </div>

        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '16px' }}>
          <Form.Item
            name="weight"
            label="单件重量(kg)"
            rules={[
              { required: true, message: '请输入单件重量' },
              { type: 'number', min: 0.001, message: '重量必须大于0.001kg' }
            ]}
          >
            <InputNumber
              min={0.001}
              max={999999}
              precision={3}
              style={{ width: '100%' }}
              placeholder="请输入单件重量"
              addonAfter="kg"
            />
          </Form.Item>

          <Form.Item
            name="price"
            label="产品价格(¥)"
            rules={[
              { required: true, message: '请输入产品价格' },
              { type: 'number', min: 0.01, message: '价格必须大于0.01元' }
            ]}
          >
            <InputNumber
              min={0.01}
              max={999999}
              precision={2}
              style={{ width: '100%' }}
              placeholder="请输入产品价格"
              formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={(value) => value!.replace(/¥\s?|(,*)/g, '') as any}
            />
          </Form.Item>
        </div>

        <Form.Item
          name="description"
          label="产品描述"
          rules={[
            { max: 200, message: '产品描述不能超过200个字符' }
          ]}
        >
          <TextArea
            rows={3}
            placeholder="请输入产品描述（可选）"
            showCount
            maxLength={200}
          />
        </Form.Item>

        <div style={{
          background: '#eff6ff',
          padding: '12px',
          borderRadius: '8px'
        }}>
          <p style={{
            fontSize: '14px',
            color: '#2563eb',
            marginBottom: '4px',
            margin: 0
          }}>
            <strong>提示：</strong>
          </p>
          <ul style={{
            fontSize: '14px',
            color: '#2563eb',
            display: 'flex',
            flexDirection: 'column',
            gap: '4px',
            margin: 0,
            paddingLeft: '16px'
          }}>
            <li>• 物料编码将自动生成</li>
            <li>• 请确保型号名称的唯一性</li>
            <li>• 重量和价格将用于订单计算</li>
          </ul>
        </div>
      </Form>
    </Modal>
  )
}

// 用App组件包裹以提供message等上下文
export const AddProductModal: React.FC<AddProductModalProps> = (props) => {
  return (
    <App>
      <AddProductModalComponent {...props} />
    </App>
  )
}
