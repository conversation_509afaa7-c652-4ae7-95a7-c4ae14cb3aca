'use client'

import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Input,
  Select,
  Modal,
  Form,
  Row,
  Col,
  Statistic,
  Tabs,
  App,
  Popconfirm,
  Descriptions,
  Checkbox
} from 'antd'
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  ExportOutlined,
  ImportOutlined,
  UserOutlined,
  TeamOutlined,
  SettingOutlined} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import type { DataNode } from 'antd/es/tree'
import { Employee, Department, EmployeeStatus, UserRole } from '@/types'
import { styleHelpers } from '@/utils/styles/antdHelpers'
// ✅ 架构合规：使用DataAccessManager统一数据访问
import { dataAccessManager } from '@/services/dataAccess/DataAccessManager'
import { handleApiResponse } from '@/utils/dataAccessErrorHandler'
import DepartmentManagement from './components/DepartmentManagement'

const { Option } = Select
const { TextArea } = Input
const { TabPane } = Tabs

function EmployeeManagement() {
  const { message } = App.useApp()

  // ✅ 架构合规：使用本地状态管理替代Store
  const [employees, setEmployees] = useState<Employee[]>([])
  const [departments, setDepartments] = useState<Department[]>([])
  const [roles, setRoles] = useState<UserRole[]>(['admin', 'manager', 'employee', 'sales', 'viewer'])
  const [loading, setLoading] = useState(false)
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null)

  const [isModalVisible, setIsModalVisible] = useState(false)
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false)
  const [isOrgModalVisible, setIsOrgModalVisible] = useState(false)
  const [isDepartmentModalVisible, setIsDepartmentModalVisible] = useState(false)
  const [editingEmployee, setEditingEmployee] = useState<Employee | null>(null)
  const [activeTabKey, setActiveTabKey] = useState('basic')
  const [form] = Form.useForm()
  const [searchText, setSearchText] = useState('')
  const [filterDepartment, setFilterDepartment] = useState<string | undefined>(undefined)
  const [filterRole, setFilterRole] = useState<UserRole | undefined>(undefined)
  const [filterStatus, setFilterStatus] = useState<EmployeeStatus | undefined>(undefined)

  // ✅ 架构合规：使用DataAccessManager加载员工数据
  const loadEmployees = async () => {
    setLoading(true)
    try {
      const result = await handleApiResponse(
        () => dataAccessManager.employees.getAll(),
        '获取员工数据'
      )
      
      if (result && result.items) {
        setEmployees(result.items)
      }
    } catch (error) {
      console.error('加载员工数据失败:', error)
      message.error('加载员工数据失败')
    } finally {
      setLoading(false)
    }
  }

  // ✅ 架构合规：使用DataAccessManager加载部门数据
  const loadDepartments = async () => {
    try {
      // 通过员工数据获取部门列表
      const result = await handleApiResponse(
        () => dataAccessManager.employees.getAll(),
        '获取员工数据以提取部门信息'
      )
      
      if (result && result.items) {
        // 从员工数据中提取唯一的部门信息
        const departmentNames = [...new Set(result.items.map(emp => emp.department).filter(Boolean))]
        const departmentList: Department[] = departmentNames.map((name, index) => ({
          id: `dept-${index + 1}`,
          departmentName: name,
          departmentCode: `DEPT${String(index + 1).padStart(3, '0')}`,
          description: `${name}部门`,
          level: 1, // 所有部门都设为一级部门
          employeeCount: result.items.filter(emp => emp.department === name).length,
          status: 'active' as const,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }))
        
        setDepartments(departmentList)
      }
    } catch (error) {
      console.error('加载部门数据失败:', error)
      message.error('加载部门数据失败')
    }
  }

  // ✅ 架构合规：生成下一个员工编码
  const generateNextEmployeeCode = () => {
    const maxCode = employees.reduce((max, emp) => {
      const codeNum = parseInt(emp.employeeCode.replace('EMP', ''), 10)
      return codeNum > max ? codeNum : max
    }, 0)
    return `EMP${(maxCode + 1).toString().padStart(4, '0')}`
  }

  // 初始化数据加载
  useEffect(() => {
    loadEmployees()
    loadDepartments()
  }, [])

  // 检查员工编码唯一性
  const checkEmployeeCodeUniqueness = (code: string, excludeId?: string): boolean => {
    return !employees.some(emp =>
      emp.employeeCode === code && emp.id !== excludeId
    )
  }

  // 数据初始化
  useEffect(() => {
    // 数据已通过Store和持久化机制自动加载
    console.log('员工数据统计:', {
      employees: employees.length,
      departments: departments.length,
      roles: roles.length
    })
  }, [employees.length, departments.length, roles.length])

  // 状态映射
  const statusMap = {
    active: { color: 'green', text: '在职' },
    inactive: { color: 'orange', text: '停职' },
    resigned: { color: 'red', text: '离职' }
  }

  const roleMap = {
    admin: { color: 'red', text: '管理员' },
    manager: { color: 'blue', text: '经理' },
    employee: { color: 'green', text: '员工' },
    sales: { color: 'orange', text: '销售员' },
    viewer: { color: 'gray', text: '访客' }
  }

  // 表格列定义
  const columns: ColumnsType<Employee> = [
    {
      title: '员工编码',
      dataIndex: 'employeeCode',
      key: 'employeeCode',
      width: 100,
      fixed: 'left'
    },
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      width: 100,
      fixed: 'left'
    },
    {
      title: '部门',
      dataIndex: 'department',
      key: 'department',
      width: 120
    },
    {
      title: '职位',
      dataIndex: 'position',
      key: 'position',
      width: 120
    },
    {
      title: '联系电话',
      dataIndex: 'phone',
      key: 'phone',
      width: 130
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      width: 100,
      render: (role: UserRole) => {
        const roleInfo = roleMap[role] || { color: 'default', text: role }
        return (
          <Tag color={roleInfo.color}>
            {roleInfo.text}
          </Tag>
        )
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: EmployeeStatus) => (
        <Tag color={statusMap[status].color}>
          {statusMap[status].text}
        </Tag>
      )
    },
    {
      title: '入职日期',
      dataIndex: 'hireDate',
      key: 'hireDate',
      width: 110
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          >
            查看
          </Button>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个员工吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ]

  // 过滤后的员工数据
  const filteredEmployees = employees.filter(employee => {
    const matchesSearch = !searchText ||
      employee.name.toLowerCase().includes(searchText.toLowerCase()) ||
      employee.employeeCode.toLowerCase().includes(searchText.toLowerCase())

    const matchesDepartment = !filterDepartment || employee.department === filterDepartment
    const matchesRole = !filterRole || employee.role === filterRole
    const matchesStatus = !filterStatus || employee.status === filterStatus

    return matchesSearch && matchesDepartment && matchesRole && matchesStatus
  })

  // 刷新员工数据
  const refreshEmployees = async () => {
    try {
      const response = await dataAccessManager.employees.getAll()
      if (response.status === 'success' && response.data) {
        setEmployees(response.data.items || [])
      }
    } catch (error) {
      console.error('刷新员工数据失败:', error)
    }
  }

  // 初始化数据
  useEffect(() => {
    refreshEmployees()
  }, [])

  // 统计数据
  const stats = {
    total: employees.length,
    active: employees.filter(e => e.status === 'active').length,
    managers: employees.filter(e => e.isDepartmentHead).length,
    departments: departments.length,
    avgAge: 0, // 可以根据实际需求计算
    newHires: employees.filter(e => {
      const hireDate = new Date(e.hireDate)
      const threeMonthsAgo = new Date()
      threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3)
      return hireDate > threeMonthsAgo
    }).length
  }

  // 操作函数
  const handleCreate = () => {
    setEditingEmployee(null)
    setIsModalVisible(true)
    setActiveTabKey('basic')
    form.resetFields()

    // 自动生成员工编码
    const nextCode = generateNextEmployeeCode()
    if (nextCode) {
      form.setFieldsValue({ employeeCode: nextCode })
    }
  }

  const handleEdit = (employee: Employee) => {
    setEditingEmployee(employee)
    setIsModalVisible(true)
    setActiveTabKey('basic')
    form.setFieldsValue(employee)
  }

  const handleViewDetail = (employee: Employee) => {
    setSelectedEmployee(employee)
    setIsDetailModalVisible(true)
  }

  // ✅ 架构合规：删除员工
  const handleDelete = async (id: string) => {
    try {
      setLoading(true)
      const result = await handleApiResponse(
        () => dataAccessManager.employees.delete(id),
        '删除员工'
      )

      if (result) {
        await loadEmployees() // 重新加载员工列表
        message.success('员工删除成功')
      }
    } catch (error) {
      console.error('删除员工失败:', error)
      message.error('删除员工失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields()
      setLoading(true)

      if (editingEmployee) {
        // ✅ 架构合规：更新员工
        const result = await handleApiResponse(
          () => dataAccessManager.employees.update(editingEmployee.id, values),
          '更新员工信息'
        )

        if (result) {
          await loadEmployees()
          message.success('员工信息更新成功')
        } else {
          return
        }
      } else {
        // ✅ 架构合规：创建新员工
        const newEmployeeData = {
          ...values,
          permissions: values.permissions || [],
          subordinates: []
        }

        const result = await handleApiResponse(
          () => dataAccessManager.employees.create(newEmployeeData),
          '创建员工'
        )

        if (result) {
          await loadEmployees()
          message.success('员工创建成功')
        } else {
          return
        }
      }

      setIsModalVisible(false)
      setActiveTabKey('basic')
      form.resetFields()
    } catch (error) {
      message.error('操作失败，请稍后重试')
      console.error('员工操作失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleModalCancel = () => {
    setIsModalVisible(false)
    setActiveTabKey('basic')
    form.resetFields()
  }

  const handleOrgChart = () => {
    setIsOrgModalVisible(true)
  }

  const handleDepartmentManagement = () => {
    setIsDepartmentModalVisible(true)
  }

  // ✅ 架构合规：部门数据更新回调
  const handleDepartmentUpdate = (updatedDepartments: Department[]) => {
    setDepartments(updatedDepartments)
  }

  // ✅ 架构合规：员工数据更新回调
  const handleEmployeeUpdate = (updatedEmployees: Employee[]) => {
    setEmployees(updatedEmployees)
  }

  const handleExport = () => {
    message.info('导出功能开发中...')
  }

  const handleImport = () => {
    message.info('导入功能开发中...')
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: styleHelpers.spacing.lg }}>
      {/* 页面标题 */}
      <div className="page-header">
        <h1 className="page-title">员工信息</h1>
        <p className="page-description">基础数据 - 员工信息 - 管理员工基本信息、组织架构和权限设置</p>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="员工总数"
              value={stats.total}
              suffix="人"
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="在职员工"
              value={stats.active}
              suffix="人"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="部门经理"
              value={stats.managers}
              suffix="人"
              valueStyle={{ color: '#1890ff' }}
              prefix={<TeamOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="部门数量"
              value={stats.departments}
              suffix="个"
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 操作区域 */}
      <Card>
        <Row gutter={[16, 16]} align="middle" justify="space-between">
          <Col xs={24} lg={18}>
            <Space wrap size="middle">
              <Input
                placeholder="搜索员工姓名或编码"
                prefix={<SearchOutlined />}
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                style={{ width: '256px' }}
              />
              <Select
                placeholder="部门"
                value={filterDepartment}
                onChange={setFilterDepartment}
                style={{ width: '128px' }}
                allowClear
              >
                {departments.map(dept => (
                  <Option key={dept.id} value={dept.departmentName}>
                    {dept.departmentName}
                  </Option>
                ))}
              </Select>
              <Select
                placeholder="角色"
                value={filterRole}
                onChange={setFilterRole}
                style={{ width: '128px' }}
                allowClear
              >
                <Option value="admin">管理员</Option>
                <Option value="manager">经理</Option>
                <Option value="employee">员工</Option>
                <Option value="sales">销售员</Option>
                <Option value="viewer">访客</Option>
              </Select>
              <Select
                placeholder="状态"
                value={filterStatus}
                onChange={setFilterStatus}
                style={{ width: '128px' }}
                allowClear
              >
                <Option value="active">在职</Option>
                <Option value="inactive">停职</Option>
                <Option value="resigned">离职</Option>
              </Select>
            </Space>
          </Col>
          <Col xs={24} lg={6}>
            <Space wrap>
              <Button icon={<SettingOutlined />} onClick={handleDepartmentManagement}>
                部门管理
              </Button>
              <Button icon={<TeamOutlined />} onClick={handleOrgChart}>
                组织架构
              </Button>
              <Button icon={<ImportOutlined />} onClick={handleImport}>
                导入
              </Button>
              <Button icon={<ExportOutlined />} onClick={handleExport}>
                导出
              </Button>
              <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
                新建员工
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 员工列表 */}
      <Card title="员工列表">
        <Table
          columns={columns}
          dataSource={filteredEmployees}
          rowKey="id"
          loading={loading}
          pagination={{
            total: filteredEmployees.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          scroll={{ x: 1400 }}
        />
      </Card>

      {/* 新建/编辑员工模态框 */}
      <Modal
        title={editingEmployee ? '编辑员工' : '新建员工'}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={900}
        okText="确认"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            status: 'active',
            role: 'employee',
            permissions: []
          }}
        >
          <Tabs
            activeKey={activeTabKey}
            onChange={setActiveTabKey}
            items={[
              {
                key: 'basic',
                label: '基础信息',
                children: (
                  <div style={{ display: 'flex', flexDirection: 'column', gap: styleHelpers.spacing.md }}>
                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          name="employeeCode"
                          label="员工编码"
                          rules={[
                            { required: true, message: '请输入员工编码' },
                            { pattern: /^A\d{3,4}$/, message: '格式：AXXX或AXXXX（如：A001）' },
                            {
                              validator: (_, value) => {
                                if (!value) return Promise.resolve()
                                if (!checkEmployeeCodeUniqueness(value, editingEmployee?.id)) {
                                  return Promise.reject(new Error('员工编码已存在，请使用其他编码'))
                                }
                                return Promise.resolve()
                              }
                            }
                          ]}
                        >
                          <Input placeholder="如：A001（自动生成）" />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          name="name"
                          label="姓名"
                          rules={[{ required: true, message: '请输入姓名' }]}
                        >
                          <Input placeholder="请输入姓名" />
                        </Form.Item>
                      </Col>
                    </Row>

                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          name="position"
                          label="职位"
                          rules={[{ required: true, message: '请输入职位' }]}
                        >
                          <Input placeholder="请输入职位" />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        {/* 预留位置，可以添加其他字段 */}
                      </Col>
                    </Row>

                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          name="phone"
                          label="联系电话"
                          rules={[
                            {
                              pattern: /^1[3-9]\d{9}$/,
                              message: '请输入正确的手机号格式'
                            }
                          ]}
                        >
                          <Input placeholder="请输入联系电话" />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          name="email"
                          label="邮箱"
                          rules={[
                            {
                              type: 'email',
                              message: '请输入正确的邮箱格式'
                            }
                          ]}
                        >
                          <Input placeholder="请输入邮箱" />
                        </Form.Item>
                      </Col>
                    </Row>

                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          name="hireDate"
                          label="入职日期"
                          rules={[{ required: true, message: '请选择入职日期' }]}
                        >
                          <Input type="date" />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          name="status"
                          label="状态"
                          rules={[{ required: true, message: '请选择状态' }]}
                        >
                          <Select placeholder="请选择状态">
                            <Option value="active">在职</Option>
                            <Option value="inactive">停职</Option>
                            <Option value="resigned">离职</Option>
                          </Select>
                        </Form.Item>
                      </Col>
                    </Row>

                    <Form.Item
                      name="remark"
                      label="备注"
                    >
                      <TextArea rows={3} placeholder="请输入备注信息" />
                    </Form.Item>
                  </div>
                )
              },
              {
                key: 'organization',
                label: '组织关系',
                children: (
                  <div style={{ display: 'flex', flexDirection: 'column', gap: styleHelpers.spacing.md }}>
                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          name="department"
                          label="所属部门"
                          rules={[{ required: true, message: '请选择所属部门' }]}
                        >
                          <Select placeholder="请选择所属部门">
                            {departments.map(dept => (
                              <Option key={dept.id} value={dept.departmentName}>
                                {dept.departmentName}
                              </Option>
                            ))}
                          </Select>
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          name="directSupervisor"
                          label="直接上级"
                        >
                          <Select placeholder="请选择直接上级" allowClear>
                            {employees.filter(emp => emp.id !== editingEmployee?.id).map(emp => (
                              <Option key={emp.id} value={emp.id}>
                                {emp.name} ({emp.employeeCode})
                              </Option>
                            ))}
                          </Select>
                        </Form.Item>
                      </Col>
                    </Row>

                    <Form.Item
                      name="isDepartmentHead"
                      valuePropName="checked"
                    >
                      <Checkbox>是否部门负责人</Checkbox>
                    </Form.Item>

                    <div style={{ color: '#9ca3af', textAlign: 'center', padding: '32px 0' }}>
                      <p>组织架构图和下属管理功能开发中...</p>
                    </div>
                  </div>
                )
              },
              {
                key: 'permissions',
                label: '权限设置',
                children: (
                  <div style={{ display: 'flex', flexDirection: 'column', gap: styleHelpers.spacing.md }}>
                    <Form.Item
                      name="role"
                      label="用户角色"
                      rules={[{ required: true, message: '请选择用户角色' }]}
                    >
                      <Select placeholder="请选择用户角色">
                        <Option value="admin">系统管理员</Option>
                        <Option value="manager">部门经理</Option>
                        <Option value="employee">普通员工</Option>
                        <Option value="sales">销售员</Option>
                        <Option value="viewer">只读用户</Option>
                      </Select>
                    </Form.Item>

                    <Form.Item
                      name="permissions"
                      label="功能权限"
                    >
                      <Checkbox.Group>
                        <Row>
                          <Col span={8}>
                            <Checkbox value="sales_read">销售查看</Checkbox>
                          </Col>
                          <Col span={8}>
                            <Checkbox value="sales_write">销售编辑</Checkbox>
                          </Col>
                          <Col span={8}>
                            <Checkbox value="customer_read">客户查看</Checkbox>
                          </Col>
                          <Col span={8}>
                            <Checkbox value="customer_write">客户编辑</Checkbox>
                          </Col>
                          <Col span={8}>
                            <Checkbox value="production_read">生产查看</Checkbox>
                          </Col>
                          <Col span={8}>
                            <Checkbox value="production_write">生产编辑</Checkbox>
                          </Col>
                          <Col span={8}>
                            <Checkbox value="finance_read">财务查看</Checkbox>
                          </Col>
                          <Col span={8}>
                            <Checkbox value="finance_write">财务编辑</Checkbox>
                          </Col>
                          <Col span={8}>
                            <Checkbox value="report_read">报表查看</Checkbox>
                          </Col>
                        </Row>
                      </Checkbox.Group>
                    </Form.Item>

                    <div style={{ color: '#9ca3af', textAlign: 'center', padding: '32px 0' }}>
                      <p>详细权限配置和角色管理功能开发中...</p>
                    </div>
                  </div>
                )
              }
            ]}
          />
        </Form>
      </Modal>

      {/* 员工详情查看模态框 */}
      <Modal
        title="员工详情"
        open={isDetailModalVisible}
        onCancel={() => setIsDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setIsDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {selectedEmployee && selectedEmployee.employeeCode && selectedEmployee.name && (
          <Descriptions bordered column={2}>
            <Descriptions.Item label="员工编码">{selectedEmployee.employeeCode}</Descriptions.Item>
            <Descriptions.Item label="姓名">{selectedEmployee.name}</Descriptions.Item>
            <Descriptions.Item label="部门">{selectedEmployee.department}</Descriptions.Item>
            <Descriptions.Item label="职位">{selectedEmployee.position}</Descriptions.Item>
            <Descriptions.Item label="联系电话">{selectedEmployee.phone || '-'}</Descriptions.Item>
            <Descriptions.Item label="邮箱">{selectedEmployee.email || '-'}</Descriptions.Item>
            <Descriptions.Item label="角色">
              {(() => {
                const roleInfo = roleMap[selectedEmployee.role] || { color: 'default', text: selectedEmployee.role }
                return (
                  <Tag color={roleInfo.color}>
                    {roleInfo.text}
                  </Tag>
                )
              })()}
            </Descriptions.Item>
            <Descriptions.Item label="状态">
              <Tag color={statusMap[selectedEmployee.status].color}>
                {statusMap[selectedEmployee.status].text}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="是否部门负责人">
              {selectedEmployee.isDepartmentHead ? '是' : '否'}
            </Descriptions.Item>
            <Descriptions.Item label="入职日期">{selectedEmployee.hireDate}</Descriptions.Item>
            <Descriptions.Item label="离职日期">{selectedEmployee.resignDate || '-'}</Descriptions.Item>
            <Descriptions.Item label="直接上级">
              {selectedEmployee.directSupervisor ?
                employees.find(e => e.id === selectedEmployee.directSupervisor)?.name || '-' : '-'}
            </Descriptions.Item>
            <Descriptions.Item label="权限数量" span={2}>{selectedEmployee.permissions.length}个</Descriptions.Item>
            <Descriptions.Item label="备注" span={2}>
              {selectedEmployee.remark || '-'}
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>

      {/* 组织架构模态框 */}
      <Modal
        title="组织架构"
        open={isOrgModalVisible}
        onCancel={() => setIsOrgModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setIsOrgModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={1000}
      >
        <div style={{ display: 'flex', flexDirection: 'column', gap: styleHelpers.spacing.md }}>
          <Row gutter={16}>
            {departments.map(dept => (
              <Col key={dept.id} span={8} style={{ marginBottom: styleHelpers.spacing.md }}>
                <Card
                  title={dept.departmentName}
                  size="small"
                  extra={<Tag color="blue">{dept.employeeCount}人</Tag>}
                >
                  <div style={{ display: 'flex', flexDirection: 'column', gap: styleHelpers.spacing.sm }}>
                    {employees
                      .filter(emp => emp.department === dept.departmentName)
                      .map(emp => (
                        <div key={emp.id} style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                          <span style={{ display: 'flex', alignItems: 'center' }}>
                            <UserOutlined style={{ marginRight: '4px' }} />
                            {emp.name}
                            {emp.isDepartmentHead && (
                              <Tag color="red" style={{ marginLeft: '4px' }}>负责人</Tag>
                            )}
                          </span>
                          {(() => {
                            const roleInfo = roleMap[emp.role] || { color: 'default', text: emp.role }
                            return (
                              <Tag color={roleInfo.color}>
                                {roleInfo.text}
                              </Tag>
                            )
                          })()}
                        </div>
                      ))}
                  </div>
                </Card>
              </Col>
            ))}
          </Row>

          <div style={{ color: '#9ca3af', textAlign: 'center', padding: '32px 0' }}>
            <p>树形组织架构图和层级管理功能开发中...</p>
          </div>
        </div>
      </Modal>

      {/* 部门管理模态框 */}
      <DepartmentManagement
        visible={isDepartmentModalVisible}
        onClose={() => setIsDepartmentModalVisible(false)}
        departments={departments}
        employees={employees}
        onDepartmentUpdate={handleDepartmentUpdate}
        onEmployeeUpdate={handleEmployeeUpdate}
      />
    </div>
  )
}

// 用App组件包裹以提供message等上下文
export default function EmployeeManagementPage() {
  return (
    <App>
      <EmployeeManagement />
    </App>
  )
}
