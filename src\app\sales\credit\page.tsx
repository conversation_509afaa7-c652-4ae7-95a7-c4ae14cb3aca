'use client'

import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Input,
  Select,
  Modal,
  Form,
  InputNumber,
  Row,
  Col,
  Statistic,
  Progress,
  message,
  Popconfirm,
  Descriptions,
  Alert,
  DatePicker
} from 'antd'
import { 
  PlusOutlined, 
  SearchOutlined, 
  EditOutlined, 
  DeleteOutlined,
  EyeOutlined,
  ExportOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  SafetyOutlined,
  TrophyOutlined,
  DollarOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import { CreditAssessment, Customer, CustomerLevel } from '@/types'
import dayjs from 'dayjs'

const { Option } = Select
const { TextArea } = Input

const CreditManagement: React.FC = () => {
  const [assessments, setAssessments] = useState<CreditAssessment[]>([])
  const [customers, setCustomers] = useState<Customer[]>([])
  const [loading, setLoading] = useState(false)
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false)
  const [editingAssessment, setEditingAssessment] = useState<CreditAssessment | null>(null)
  const [selectedAssessment, setSelectedAssessment] = useState<CreditAssessment | null>(null)
  const [form] = Form.useForm()
  const [searchText, setSearchText] = useState('')
  const [filterRiskLevel, setFilterRiskLevel] = useState('')
  const [filterCreditLevel, setFilterCreditLevel] = useState('')

  // 模拟数据
  useEffect(() => {
    const mockCustomers: Customer[] = [
      {
        id: '1',
        customerCode: 'CUS-001',
        customerName: '上海包装材料有限公司',
        customerLevel: 'A',
        customerCategory: 'important',
        creditLimit: 500000,
        usedCredit: 320000,
        annualSalesAmount: 1200000,
        contactPerson: '张经理',
        contactPhone: '13800138001',
        address: '上海市浦东新区',
        paymentTerms: '月结30天',
        discountRate: 0.05,
        preferredProducts: ['CP-202'],
        salesRepresentative: '李销售',
        status: 'active',
        createdAt: '2023-01-15',
        updatedAt: '2024-01-15'
      }
    ]

    const mockAssessments: CreditAssessment[] = [
      {
        id: '1',
        customerId: '1',
        assessmentDate: '2024-01-15',
        paymentHistoryScore: 45, // 满分50
        orderScaleScore: 28,     // 满分30
        productPriceScore: 18,   // 满分20
        totalScore: 91,          // 总分100
        creditLevel: 'A',
        riskLevel: 'low',
        assessor: '风控专员',
        remark: '优质客户，付款及时，订单规模大',
        createdAt: '2024-01-15T10:00:00'
      },
      {
        id: '2',
        customerId: '2',
        assessmentDate: '2024-01-18',
        paymentHistoryScore: 35,
        orderScaleScore: 20,
        productPriceScore: 15,
        totalScore: 70,
        creditLevel: 'B',
        riskLevel: 'medium',
        assessor: '风控专员',
        remark: '一般客户，偶有延期付款',
        createdAt: '2024-01-18T14:00:00'
      },
      {
        id: '3',
        customerId: '3',
        assessmentDate: '2024-01-20',
        paymentHistoryScore: 25,
        orderScaleScore: 15,
        productPriceScore: 10,
        totalScore: 50,
        creditLevel: 'C',
        riskLevel: 'high',
        assessor: '风控专员',
        remark: '高风险客户，多次逾期付款',
        createdAt: '2024-01-20T16:00:00'
      }
    ]

    setCustomers(mockCustomers)
    setAssessments(mockAssessments)
  }, [])

  // 获取风险等级标签
  const getRiskLevelTag = (level: string) => {
    const levelMap = {
      'low': { color: 'green', text: '低风险', icon: <CheckCircleOutlined /> },
      'medium': { color: 'orange', text: '中风险', icon: <ExclamationCircleOutlined /> },
      'high': { color: 'red', text: '高风险', icon: <WarningOutlined /> }
    }
    const config = levelMap[level as keyof typeof levelMap] || { color: 'default', text: '未知', icon: null }
    return (
      <Tag color={config.color} icon={config.icon}>
        {config.text}
      </Tag>
    )
  }

  // 获取信用等级标签
  const getCreditLevelTag = (level: CustomerLevel) => {
    const levelMap = {
      'A': { color: 'gold', text: 'A级', icon: <TrophyOutlined /> },
      'B': { color: 'blue', text: 'B级', icon: <SafetyOutlined /> },
      'C': { color: 'green', text: 'C级', icon: <DollarOutlined /> },
      'D': { color: 'orange', text: 'D级', icon: <DollarOutlined /> },
      'E': { color: 'red', text: 'E级', icon: <DollarOutlined /> }
    }
    const config = levelMap[level] || { color: 'default', text: '未知', icon: null }
    return (
      <Tag color={config.color} icon={config.icon}>
        {config.text}
      </Tag>
    )
  }

  // 获取评分颜色
  const getScoreColor = (score: number, maxScore: number): string => {
    const percentage = (score / maxScore) * 100
    if (percentage >= 80) return '#52c41a'
    if (percentage >= 60) return '#faad14'
    return '#ff4d4f'
  }

  // 自动计算信用评估
  const calculateCreditAssessment = (customerId: string) => {
    // 模拟计算逻辑
    const customer = customers.find(c => c.id === customerId)
    if (!customer) return null

    // 历史付款评分 (0-50分)
    // 模拟：根据客户等级给分
    let paymentHistoryScore = 0
    switch (customer.customerLevel) {
      case 'A': paymentHistoryScore = 45; break
      case 'B': paymentHistoryScore = 35; break
      case 'C': paymentHistoryScore = 25; break
    }

    // 订单规模评分 (0-30分)
    const orderScaleScore = Math.min(30, ((customer.annualSalesAmount || 0) / 100000) * 3)

    // 产品单价评分 (0-20分) - 模拟计算
    const productPriceScore = customer.customerLevel === 'A' ? 18 : 
                             customer.customerLevel === 'B' ? 15 : 10

    const totalScore = paymentHistoryScore + orderScaleScore + productPriceScore

    // 确定信用等级和风险等级
    let creditLevel: CustomerLevel = 'E'
    let riskLevel: 'low' | 'medium' | 'high' = 'high'

    if (totalScore >= 90) {
      creditLevel = 'A'
      riskLevel = 'low'
    } else if (totalScore >= 75) {
      creditLevel = 'B'
      riskLevel = 'low'
    } else if (totalScore >= 60) {
      creditLevel = 'C'
      riskLevel = 'medium'
    } else if (totalScore >= 40) {
      creditLevel = 'D'
      riskLevel = 'medium'
    } else {
      creditLevel = 'E'
      riskLevel = 'high'
    }

    return {
      paymentHistoryScore,
      orderScaleScore,
      productPriceScore,
      totalScore,
      creditLevel,
      riskLevel
    }
  }

  // 表格列定义
  const columns: ColumnsType<CreditAssessment> = [
    {
      title: '客户ID',
      dataIndex: 'customerId',
      key: 'customerId',
      width: 100,
      fixed: 'left'
    },
    {
      title: '评估日期',
      dataIndex: 'assessmentDate',
      key: 'assessmentDate',
      width: 120,
      sorter: (a, b) => new Date(a.assessmentDate).getTime() - new Date(b.assessmentDate).getTime()
    },
    {
      title: '信用等级',
      dataIndex: 'creditLevel',
      key: 'creditLevel',
      width: 100,
      render: (level: CustomerLevel) => getCreditLevelTag(level)
    },
    {
      title: '风险等级',
      dataIndex: 'riskLevel',
      key: 'riskLevel',
      width: 100,
      render: (level: string) => getRiskLevelTag(level)
    },
    {
      title: '总评分',
      dataIndex: 'totalScore',
      key: 'totalScore',
      width: 120,
      render: (score: number) => (
        <div>
          <div style={{ fontWeight: 'bold', color: getScoreColor(score, 100) }}>
            {score}分
          </div>
          <Progress 
            percent={score} 
            size="small" 
            strokeColor={getScoreColor(score, 100)}
            showInfo={false}
          />
        </div>
      ),
      sorter: (a, b) => a.totalScore - b.totalScore
    },
    {
      title: '评分详情',
      key: 'scoreDetails',
      width: 200,
      render: (_, record) => (
        <div style={{ fontSize: '12px' }}>
          <div>历史付款: {record.paymentHistoryScore}/50</div>
          <div>订单规模: {record.orderScaleScore}/30</div>
          <div>产品单价: {record.productPriceScore}/20</div>
        </div>
      )
    },
    {
      title: '评估人',
      dataIndex: 'assessor',
      key: 'assessor',
      width: 100
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button 
            type="link" 
            icon={<EyeOutlined />} 
            onClick={() => handleViewDetail(record)}
          >
            详情
          </Button>
          <Button 
            type="link" 
            icon={<EditOutlined />} 
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个评估记录吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ]

  // 过滤后的评估数据
  const filteredAssessments = assessments.filter(assessment => {
    const matchesSearch = !searchText || 
      assessment.customerId.toLowerCase().includes(searchText.toLowerCase()) ||
      assessment.assessor.toLowerCase().includes(searchText.toLowerCase())
    
    const matchesRiskLevel = !filterRiskLevel || assessment.riskLevel === filterRiskLevel
    const matchesCreditLevel = !filterCreditLevel || assessment.creditLevel === filterCreditLevel
    
    return matchesSearch && matchesRiskLevel && matchesCreditLevel
  })

  // 统计数据
  const stats = {
    total: assessments.length,
    lowRisk: assessments.filter(a => a.riskLevel === 'low').length,
    mediumRisk: assessments.filter(a => a.riskLevel === 'medium').length,
    highRisk: assessments.filter(a => a.riskLevel === 'high').length,
    averageScore: assessments.length > 0 ? 
      Math.round(assessments.reduce((sum, a) => sum + a.totalScore, 0) / assessments.length) : 0
  }

  const handleCreate = () => {
    setEditingAssessment(null)
    setIsModalVisible(true)
    form.resetFields()
  }

  const handleEdit = (assessment: CreditAssessment) => {
    setEditingAssessment(assessment)
    setIsModalVisible(true)
    form.setFieldsValue({
      ...assessment,
      assessmentDate: dayjs(assessment.assessmentDate)
    })
  }

  const handleViewDetail = (assessment: CreditAssessment) => {
    setSelectedAssessment(assessment)
    setIsDetailModalVisible(true)
  }

  const handleDelete = (id: string) => {
    setAssessments(assessments.filter(a => a.id !== id))
    message.success('评估记录删除成功')
  }

  const handleModalOk = () => {
    form.validateFields().then(values => {
      const now = new Date().toISOString()
      const formattedValues = {
        ...values,
        assessmentDate: values.assessmentDate.format('YYYY-MM-DD')
      }

      if (editingAssessment) {
        const updatedAssessments = assessments.map(a =>
          a.id === editingAssessment.id
            ? { ...a, ...formattedValues }
            : a
        )
        setAssessments(updatedAssessments)
        message.success('评估记录更新成功')
      } else {
        const newAssessment: CreditAssessment = {
          id: Date.now().toString(),
          ...formattedValues,
          createdAt: now
        }
        setAssessments([...assessments, newAssessment])
        message.success('评估记录创建成功')
      }
      setIsModalVisible(false)
      form.resetFields()
    })
  }

  const handleModalCancel = () => {
    setIsModalVisible(false)
    form.resetFields()
  }

  const handleAutoCalculate = () => {
    const customerId = form.getFieldValue('customerId')
    if (!customerId) {
      message.warning('请先选择客户')
      return
    }

    const calculation = calculateCreditAssessment(customerId)
    if (calculation) {
      form.setFieldsValue(calculation)
      message.success('自动计算完成')
    } else {
      message.error('未找到客户信息')
    }
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: '24px' }}>
        <h1 style={{ fontSize: '24px', fontWeight: 'bold', margin: '0 0 8px 0' }}>信用风控管理</h1>
        <p style={{ color: '#666', margin: 0 }}>三维度评估模型，智能风险识别与管控</p>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="评估总数"
              value={stats.total}
              suffix="个"
              prefix={<SafetyOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="平均评分"
              value={stats.averageScore}
              suffix="分"
              valueStyle={{ color: getScoreColor(stats.averageScore, 100) }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="高风险客户"
              value={stats.highRisk}
              suffix="个"
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="低风险客户"
              value={stats.lowRisk}
              suffix="个"
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 风险分布 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="低风险"
              value={stats.lowRisk}
              suffix="个"
              valueStyle={{ color: '#52c41a' }}
            />
            <Progress
              percent={stats.total > 0 ? (stats.lowRisk / stats.total) * 100 : 0}
              strokeColor="#52c41a"
              showInfo={false}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="中风险"
              value={stats.mediumRisk}
              suffix="个"
              valueStyle={{ color: '#faad14' }}
            />
            <Progress
              percent={stats.total > 0 ? (stats.mediumRisk / stats.total) * 100 : 0}
              strokeColor="#faad14"
              showInfo={false}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="高风险"
              value={stats.highRisk}
              suffix="个"
              valueStyle={{ color: '#ff4d4f' }}
            />
            <Progress
              percent={stats.total > 0 ? (stats.highRisk / stats.total) * 100 : 0}
              strokeColor="#ff4d4f"
              showInfo={false}
            />
          </Card>
        </Col>
      </Row>

      {/* 操作区域 */}
      <Card>
        <Row justify="space-between" align="middle" gutter={[16, 16]}>
          <Col xs={24} lg={18}>
            <Row gutter={[16, 16]}>
            <Input
              placeholder="搜索客户ID或评估人"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: '100%' }}
            />
            <Select
              placeholder="风险等级"
              value={filterRiskLevel}
              onChange={setFilterRiskLevel}
              style={{ width: '100%' }}
              allowClear
            >
              <Option value="low">低风险</Option>
              <Option value="medium">中风险</Option>
              <Option value="high">高风险</Option>
            </Select>
            <Select
              placeholder="信用等级"
              value={filterCreditLevel}
              onChange={setFilterCreditLevel}
              style={{ width: '100%' }}
              allowClear
            >
              <Option value="A">A级</Option>
              <Option value="B">B级</Option>
              <Option value="C">C级</Option>
            </Select>
            </Row>
          </Col>
          <Col xs={24} lg={6}>
            <Row gutter={[8, 8]} justify="end">
              <Col>
                <Button icon={<ExportOutlined />}>导出</Button>
              </Col>
              <Col>
                <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
                  新建评估
                </Button>
              </Col>
            </Row>
          </Col>
        </Row>
      </Card>

      {/* 评估列表 */}
      <Card title="信用评估列表">
        <Table
          columns={columns}
          dataSource={filteredAssessments}
          rowKey="id"
          loading={loading}
          pagination={{
            total: filteredAssessments.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 新建/编辑评估模态框 */}
      <Modal
        title={editingAssessment ? '编辑信用评估' : '新建信用评估'}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={800}
        okText="确认"
        cancelText="取消"
      >
        <Alert
          message="三维度评估模型"
          description="历史付款(50分) + 订单规模(30分) + 产品单价(20分) = 总评分(100分)"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Form
          form={form}
          layout="vertical"
          initialValues={{
            riskLevel: 'medium'
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="customerId"
                label="客户ID"
                rules={[{ required: true, message: '请输入客户ID' }]}
              >
                <Select placeholder="请选择客户">
                  {customers.map(customer => (
                    <Option key={customer.id} value={customer.id}>
                      {customer.customerCode} - {customer.customerName}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="assessmentDate"
                label="评估日期"
                rules={[{ required: true, message: '请选择评估日期' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <div style={{ textAlign: 'center', marginBottom: 16 }}>
            <Button type="dashed" onClick={handleAutoCalculate}>
              智能计算评分
            </Button>
          </div>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="paymentHistoryScore"
                label="历史付款评分 (0-50分)"
                rules={[{ required: true, message: '请输入历史付款评分' }]}
              >
                <InputNumber
                  min={0}
                  max={50}
                  style={{ width: '100%' }}
                  placeholder="0-50分"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="orderScaleScore"
                label="订单规模评分 (0-30分)"
                rules={[{ required: true, message: '请输入订单规模评分' }]}
              >
                <InputNumber
                  min={0}
                  max={30}
                  style={{ width: '100%' }}
                  placeholder="0-30分"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="productPriceScore"
                label="产品单价评分 (0-20分)"
                rules={[{ required: true, message: '请输入产品单价评分' }]}
              >
                <InputNumber
                  min={0}
                  max={20}
                  style={{ width: '100%' }}
                  placeholder="0-20分"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="totalScore"
                label="总评分"
                rules={[{ required: true, message: '请输入总评分' }]}
              >
                <InputNumber
                  min={0}
                  max={100}
                  style={{ width: '100%' }}
                  placeholder="0-100分"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="creditLevel"
                label="信用等级"
                rules={[{ required: true, message: '请选择信用等级' }]}
              >
                <Select>
                  <Option value="A">A级 (80分以上)</Option>
                  <Option value="B">B级 (60-79分)</Option>
                  <Option value="C">C级 (60分以下)</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="riskLevel"
                label="风险等级"
                rules={[{ required: true, message: '请选择风险等级' }]}
              >
                <Select>
                  <Option value="low">低风险</Option>
                  <Option value="medium">中风险</Option>
                  <Option value="high">高风险</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="assessor"
            label="评估人"
            rules={[{ required: true, message: '请输入评估人' }]}
          >
            <Input placeholder="请输入评估人" />
          </Form.Item>

          <Form.Item
            name="remark"
            label="评估备注"
          >
            <TextArea rows={3} placeholder="请输入评估备注" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 评估详情模态框 */}
      <Modal
        title="信用评估详情"
        open={isDetailModalVisible}
        onCancel={() => setIsDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setIsDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={700}
      >
        {selectedAssessment && selectedAssessment.customerId && selectedAssessment.assessmentDate && (
          <div>
            <Descriptions column={2} bordered>
              <Descriptions.Item label="客户ID">{selectedAssessment.customerId}</Descriptions.Item>
              <Descriptions.Item label="评估日期">{selectedAssessment.assessmentDate}</Descriptions.Item>
              <Descriptions.Item label="信用等级">
                {getCreditLevelTag(selectedAssessment.creditLevel)}
              </Descriptions.Item>
              <Descriptions.Item label="风险等级">
                {getRiskLevelTag(selectedAssessment.riskLevel)}
              </Descriptions.Item>
              <Descriptions.Item label="总评分">
                <span style={{
                  fontSize: '18px',
                  fontWeight: 'bold',
                  color: getScoreColor(selectedAssessment.totalScore, 100)
                }}>
                  {selectedAssessment.totalScore}分
                </span>
              </Descriptions.Item>
              <Descriptions.Item label="评估人">{selectedAssessment.assessor}</Descriptions.Item>
              <Descriptions.Item label="创建时间" span={2}>
                {new Date(selectedAssessment.createdAt).toLocaleString()}
              </Descriptions.Item>
              <Descriptions.Item label="备注" span={2}>
                {selectedAssessment.remark || '无'}
              </Descriptions.Item>
            </Descriptions>

            <div style={{ marginTop: 24 }}>
              <h4>评分详情</h4>
              <Row gutter={16}>
                <Col span={8}>
                  <Card size="small">
                    <Statistic
                      title="历史付款"
                      value={selectedAssessment.paymentHistoryScore}
                      suffix="/50"
                      valueStyle={{ color: getScoreColor(selectedAssessment.paymentHistoryScore, 50) }}
                    />
                    <Progress
                      percent={(selectedAssessment.paymentHistoryScore / 50) * 100}
                      strokeColor={getScoreColor(selectedAssessment.paymentHistoryScore, 50)}
                      showInfo={false}
                    />
                  </Card>
                </Col>
                <Col span={8}>
                  <Card size="small">
                    <Statistic
                      title="订单规模"
                      value={selectedAssessment.orderScaleScore}
                      suffix="/30"
                      valueStyle={{ color: getScoreColor(selectedAssessment.orderScaleScore, 30) }}
                    />
                    <Progress
                      percent={(selectedAssessment.orderScaleScore / 30) * 100}
                      strokeColor={getScoreColor(selectedAssessment.orderScaleScore, 30)}
                      showInfo={false}
                    />
                  </Card>
                </Col>
                <Col span={8}>
                  <Card size="small">
                    <Statistic
                      title="产品单价"
                      value={selectedAssessment.productPriceScore}
                      suffix="/20"
                      valueStyle={{ color: getScoreColor(selectedAssessment.productPriceScore, 20) }}
                    />
                    <Progress
                      percent={(selectedAssessment.productPriceScore / 20) * 100}
                      strokeColor={getScoreColor(selectedAssessment.productPriceScore, 20)}
                      showInfo={false}
                    />
                  </Card>
                </Col>
              </Row>
            </div>

            <div style={{ marginTop: 24 }}>
              <h4>风险管控建议</h4>
              <Alert
                message={
                  selectedAssessment.riskLevel === 'low' ? '低风险客户' :
                  selectedAssessment.riskLevel === 'medium' ? '中风险客户' : '高风险客户'
                }
                description={
                  selectedAssessment.riskLevel === 'low' ?
                    '建议：正常交易，可适当提高信用额度' :
                  selectedAssessment.riskLevel === 'medium' ?
                    '建议：加强跟踪，控制信用额度，缩短付款周期' :
                    '建议：严格控制信用额度，要求预付款或担保，降低排单优先级'
                }
                type={
                  selectedAssessment.riskLevel === 'low' ? 'success' :
                  selectedAssessment.riskLevel === 'medium' ? 'warning' : 'error'
                }
                showIcon
              />
            </div>
          </div>
        )}
      </Modal>
    </div>
  )
}

export default CreditManagement
