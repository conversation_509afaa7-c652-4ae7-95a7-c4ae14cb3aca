/**
 * Header组件测试
 * 验证Tailwind迁移后的功能完整性
 */

import React from 'react'
import { render, screen } from '@testing-library/react'
import { Header } from '../Header'

// Mock useAppStore
jest.mock('@/store/useAppStore', () => ({
  useAppStore: () => ({
    isMobile: false,
    user: {
      name: '测试用户',
      avatar: null
    },
    setMobileDrawerVisible: jest.fn()
  })
}))

describe('Header', () => {
  it('应该正确渲染基本结构', () => {
    render(<Header />)

    // 验证标题
    expect(screen.getByText('企业资源规划系统')).toBeInTheDocument()

    // 验证用户名
    expect(screen.getByText('测试用户')).toBeInTheDocument()
  })

  it('应该显示通知徽章', () => {
    const { container } = render(<Header />)

    // 查找Badge组件
    const badge = container.querySelector('.ant-badge')
    expect(badge).toBeInTheDocument()
  })

  it('应该显示用户头像', () => {
    const { container } = render(<Header />)

    // 查找Avatar组件
    const avatar = container.querySelector('.ant-avatar')
    expect(avatar).toBeInTheDocument()
  })
})
