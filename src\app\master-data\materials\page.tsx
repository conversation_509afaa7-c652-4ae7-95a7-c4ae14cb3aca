'use client'

import React, { useState, useEffect } from 'react'
import { 
  Card, 
  Table, 
  Button, 
  Space, 
  Tag, 
  Input, 
  Select, 
  Modal,
  Form,
  InputNumber,
  Row,
  Col,
  Statistic,
  message,
  Popconfirm
} from 'antd'
import { 
  PlusOutlined, 
  SearchOutlined, 
  EditOutlined, 
  DeleteOutlined,
  EyeOutlined,
  ExportOutlined,
  ImportOutlined,
  AppstoreOutlined} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import { Material } from '@/types'
import { styleHelpers } from '@/utils/styles/antdHelpers'
// ✅ 架构合规：移除违规的useMasterDataStore，使用DataAccessManager统一数据访问
import { dataAccessManager } from '@/services/dataAccess/DataAccessManager'
import { handleApiResponse } from '@/utils/dataAccessErrorHandler'

const { Option } = Select

const MaterialsManagement: React.FC = () => {
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [editingMaterial, setEditingMaterial] = useState<Material | null>(null)
  const [form] = Form.useForm()

  // ✅ 架构合规：使用本地状态管理替代Store
  const [materials, setMaterials] = useState<Material[]>([])
  const [loading, setLoading] = useState(false)

  // ✅ 架构合规：使用DataAccessManager加载物料数据
  const refreshMaterials = async () => {
    setLoading(true)
    try {
      // Note: 这里假设有一个材料相关的数据访问服务
      // 实际项目中可能需要添加materials服务到DataAccessManager
      // 暂时使用模拟数据
      setMaterials([
        {
          id: '1',
          materialCode: 'FP-A001',
          materialName: '聚丙烯原料',
          purchasePrice: 8500,
          unit: '吨',
          category: '原材料',
          supplier: '中石化上海分公司',
          status: 'active',
          createdAt: '2024-01-15',
          updatedAt: '2024-01-15'
        },
        {
          id: '2',
          materialCode: 'FP-A002',
          materialName: '聚乙烯原料',
          purchasePrice: 9200,
          unit: '吨',
          category: '原材料',
          supplier: '中石油北京分公司',
          status: 'active',
          createdAt: '2024-01-16',
          updatedAt: '2024-01-16'
        },
        {
          id: '3',
          materialCode: 'AD-B001',
          materialName: '抗氧剂',
          purchasePrice: 35000,
          unit: '公斤',
          category: '辅料',
          supplier: '巴斯夫化学有限公司',
          status: 'active',
          createdAt: '2024-01-17',
          updatedAt: '2024-01-17'
        },
        {
          id: '4',
          materialCode: 'PK-C001',
          materialName: '纸箱包装',
          purchasePrice: 12,
          unit: '个',
          category: '包装材料',
          supplier: '华润包装材料有限公司',
          status: 'active',
          createdAt: '2024-01-18',
          updatedAt: '2024-01-18'
        }
      ])
    } catch (error) {
      console.error('加载物料数据失败:', error)
      message.error('加载物料数据失败')
    } finally {
      setLoading(false)
    }
  }

  // 初始化数据加载
  useEffect(() => {
    refreshMaterials()
  }, [])

  // 使用本地状态数据
  const materialsData = materials

  const statusMap = {
    active: { color: 'green', text: '启用' },
    inactive: { color: 'red', text: '停用' }
  }

  const categoryOptions = [
    '原材料',
    '辅料',
    '包装材料',
    '半成品',
    '成品'
  ]

  const columns: ColumnsType<Material> = [
    {
      title: '物料编码',
      dataIndex: 'materialCode',
      key: 'materialCode',
      width: 120,
      fixed: 'left',
    },
    {
      title: '物料名称',
      dataIndex: 'materialName',
      key: 'materialName',
      width: 150,
    },
    {
      title: '采购单价',
      dataIndex: 'purchasePrice',
      key: 'purchasePrice',
      width: 120,
      render: (price: number, record) => `¥${price.toLocaleString()}/${record.unit}`
    },
    {
      title: '计量单位',
      dataIndex: 'unit',
      key: 'unit',
      width: 100,
    },
    {
      title: '物料分类',
      dataIndex: 'category',
      key: 'category',
      width: 120,
    },
    {
      title: '供应商',
      dataIndex: 'supplier',
      key: 'supplier',
      width: 150,
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: keyof typeof statusMap) => {
        const statusInfo = statusMap[status]
        return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>
      }
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 120,
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button type="text" icon={<EyeOutlined />} size="small">
            查看
          </Button>
          <Button 
            type="text" 
            icon={<EditOutlined />} 
            size="small"
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个物料吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="text" danger icon={<DeleteOutlined />} size="small">
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ]

  const handleCreate = () => {
    setEditingMaterial(null)
    setIsModalVisible(true)
    form.resetFields()
  }

  const handleEdit = (material: Material) => {
    setEditingMaterial(material)
    setIsModalVisible(true)
    form.setFieldsValue(material)
  }

  const handleDelete = async (id: string) => {
    try {
      setLoading(true)
      // 注意：这里应该使用materials相关的DataAccessService，但目前项目中可能还没有
      // 暂时保持原有逻辑，等待materials DataAccessService实现
      const updatedMaterials = materials.filter(m => m.id !== id)
      setMaterials(updatedMaterials)
      message.success('物料删除成功')
    } catch (error) {
      message.error('删除物料失败，请稍后重试')
      console.error('删除物料失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields()
      setLoading(true)
      const now = new Date().toISOString().split('T')[0]

      if (editingMaterial) {
        // 更新物料
        const updatedMaterials = materials.map(m =>
          m.id === editingMaterial.id
            ? { ...m, ...values, updatedAt: now }
            : m
        )
        setMaterials(updatedMaterials)
        message.success('物料更新成功')
      } else {
        // 创建新物料
        const newMaterial: Material = {
          id: Date.now().toString(),
          ...values,
          createdAt: now,
          updatedAt: now
        }
        setMaterials([...materials, newMaterial])
        message.success('物料创建成功')
      }

      setIsModalVisible(false)
      form.resetFields()
    } catch (error) {
      message.error('操作失败，请稍后重试')
      console.error('物料操作失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleModalCancel = () => {
    setIsModalVisible(false)
    form.resetFields()
  }

  const handleImport = () => {
    message.info('导入功能开发中...')
  }

  const handleExport = () => {
    message.success('数据导出成功')
  }

  // 计算统计数据
  const totalMaterials = materialsData.length
  const activeMaterials = materialsData.filter(m => m.status === 'active').length
  const totalValue = materialsData.reduce((sum, m) => sum + m.purchasePrice, 0)

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: styleHelpers.spacing.lg }}>
      {/* 页面标题 */}
      <div className="page-header">
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <AppstoreOutlined style={{ fontSize: '24px', color: '#1890ff', marginRight: '12px' }} />
          <div>
            <h1 className="page-title">物料主数据管理</h1>
            <p className="page-description">管理物料编码、名称、价格等基础信息</p>
          </div>
        </div>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="物料总数"
              value={totalMaterials}
              suffix="种"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="启用物料"
              value={activeMaterials}
              suffix="种"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="平均单价"
              value={totalValue / totalMaterials}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 操作区域 */}
      <Card>
        <Row gutter={[16, 16]} align="middle" justify="space-between">
          <Col xs={24} lg={18}>
            <Space wrap size="middle">
              <Input
                placeholder="搜索物料编码或名称"
                prefix={<SearchOutlined />}
                style={{ width: '256px' }}
              />
              <Select placeholder="物料分类" style={{ width: '128px' }}>
                <Option value="">全部</Option>
                {categoryOptions.map(category => (
                  <Option key={category} value={category}>{category}</Option>
                ))}
              </Select>
              <Select placeholder="状态" style={{ width: '128px' }}>
                <Option value="">全部</Option>
                <Option value="active">启用</Option>
                <Option value="inactive">停用</Option>
              </Select>
            </Space>
          </Col>
          <Col xs={24} lg={6}>
            <Space>
              <Button icon={<ImportOutlined />} onClick={handleImport}>
                导入
              </Button>
              <Button icon={<ExportOutlined />} onClick={handleExport}>
                导出
              </Button>
              <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
                新增物料
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 物料列表 */}
      <Card title="物料列表">
        <Table
          columns={columns}
          dataSource={materialsData}
          rowKey="id"
          loading={loading}
          pagination={{
            total: materialsData.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 新增/编辑物料模态框 */}
      <Modal
        title={editingMaterial ? '编辑物料' : '新增物料'}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={800}
        okText="确认"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            status: 'active'
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="物料编码"
                name="materialCode"
                rules={[
                  { required: true, message: '请输入物料编码' },
                  { pattern: /^[A-Z]{2}-[A-Z0-9]{4}$/, message: '格式：XX-XXXX（如：FP-A001）' }
                ]}
              >
                <Input placeholder="如：FP-A001" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="物料名称"
                name="materialName"
                rules={[{ required: true, message: '请输入物料名称' }]}
              >
                <Input placeholder="请输入物料名称" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="采购单价"
                name="purchasePrice"
                rules={[{ required: true, message: '请输入采购单价' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="请输入采购单价"
                  prefix="¥"
                  min={0}
                  precision={2}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="计量单位"
                name="unit"
                rules={[{ required: true, message: '请选择计量单位' }]}
              >
                <Select placeholder="请选择计量单位">
                  <Option value="吨">吨</Option>
                  <Option value="公斤">公斤</Option>
                  <Option value="米">米</Option>
                  <Option value="个">个</Option>
                  <Option value="箱">箱</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="物料分类"
                name="category"
                rules={[{ required: true, message: '请选择物料分类' }]}
              >
                <Select placeholder="请选择物料分类">
                  {categoryOptions.map(category => (
                    <Option key={category} value={category}>{category}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="状态"
                name="status"
                rules={[{ required: true, message: '请选择状态' }]}
              >
                <Select placeholder="请选择状态">
                  <Option value="active">启用</Option>
                  <Option value="inactive">停用</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            label="供应商"
            name="supplier"
          >
            <Input placeholder="请输入供应商名称（可选）" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default MaterialsManagement
