'use client'

import React, { useState, useEffect } from 'react'
import { Card, Button, Table, Tag, Space, Alert, Statistic, Row, Col, Typography, Modal, message, App } from 'antd'
import { ClearOutlined, BarChartOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons'
import { analyzeWorkstationQueues, cleanAllWorkstationQueues } from '@/utils/workstationQueueCleaner'
import { styleHelpers } from '@/utils/styles/antdHelpers'

const { Title, Text, Paragraph } = Typography

function QueueCleanerPageComponent() {
  const { modal } = App.useApp()
  const [analysisResult, setAnalysisResult] = useState<any>(null)
  const [cleanupResult, setCleanupResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [cleanupLoading, setCleanupLoading] = useState(false)

  // 分析队列数据
  const handleAnalyze = () => {
    setLoading(true)
    try {
      const result = analyzeWorkstationQueues()
      setAnalysisResult(result)
      message.success('队列数据分析完成')
    } catch (error) {
      message.error('分析失败: ' + (error as Error).message)
    } finally {
      setLoading(false)
    }
  }

  // 执行清理
  const handleCleanup = () => {
    modal.confirm({
      title: '确认清理队列数据',
      content: (
        <div>
          <Paragraph>
            此操作将清理所有工位队列中的无效数据（工单ID格式），只保留有效的批次号格式数据。
          </Paragraph>
          <Paragraph type="warning">
            <ExclamationCircleOutlined /> 此操作不可撤销，请确认继续。
          </Paragraph>
        </div>
      ),
      onOk: async () => {
        setCleanupLoading(true)
        try {
          const result = await cleanAllWorkstationQueues()
          setCleanupResult(result)
          message.success(`清理完成！共清理 ${(result as any)?.removedItems || 0} 个无效项目`)
          // 重新分析数据
          handleAnalyze()
        } catch (error) {
          message.error('清理失败: ' + (error as Error).message)
        } finally {
          setCleanupLoading(false)
        }
      }
    })
  }

  // 页面加载时自动分析
  useEffect(() => {
    handleAnalyze()
  }, [])

  // 分析结果表格列定义
  const analysisColumns = [
    {
      title: '工位编码',
      dataIndex: 'workstationCode',
      key: 'workstationCode',
      width: 100
    },
    {
      title: '工位名称',
      dataIndex: 'workstationName',
      key: 'workstationName',
      width: 200
    },
    {
      title: '队列长度',
      dataIndex: 'queueLength',
      key: 'queueLength',
      width: 100,
      render: (length: number) => (
        <Tag color={length > 0 ? 'blue' : 'default'}>{length}</Tag>
      )
    },
    {
      title: '有效批次号',
      dataIndex: 'validItems',
      key: 'validItems',
      width: 120,
      render: (items: string[]) => (
        <Tag color="green">{items.length}</Tag>
      )
    },
    {
      title: '无效工单ID',
      dataIndex: 'invalidWorkOrderIds',
      key: 'invalidWorkOrderIds',
      width: 120,
      render: (items: string[]) => (
        <Tag color={items.length > 0 ? 'red' : 'default'}>{items.length}</Tag>
      )
    },
    {
      title: '其他无效项',
      dataIndex: 'otherInvalidItems',
      key: 'otherInvalidItems',
      width: 120,
      render: (items: string[]) => (
        <Tag color={items.length > 0 ? 'orange' : 'default'}>{items.length}</Tag>
      )
    }
  ]

  // 清理结果表格列定义
  const cleanupColumns = [
    {
      title: '工位编码',
      dataIndex: 'workstationCode',
      key: 'workstationCode',
      width: 100
    },
    {
      title: '原队列长度',
      dataIndex: 'originalQueueLength',
      key: 'originalQueueLength',
      width: 120
    },
    {
      title: '清理后长度',
      dataIndex: 'cleanedQueueLength',
      key: 'cleanedQueueLength',
      width: 120
    },
    {
      title: '清理项目',
      dataIndex: 'removedItems',
      key: 'removedItems',
      render: (items: string[]) => (
        <div style={{ maxWidth: 300 }}>
          {items.map((item, index) => (
            <Tag key={index} color="red" style={{ marginBottom: 4 }}>
              {item.length > 20 ? `${item.substring(0, 20)}...` : item}
            </Tag>
          ))}
        </div>
      )
    }
  ]

  return (
    <div style={{ padding: styleHelpers.spacing.lg }}>
      <Title level={2}>
        <ClearOutlined /> 工位队列数据清理工具
      </Title>

      <Paragraph>
        此工具用于分析和清理工位队列中的无效数据。工位队列应该只包含有效的批次号格式（PC开头的14位格式），
        而不应包含工单ID格式（wo_开头的格式）。
      </Paragraph>

      {/* 操作按钮 */}
      <Card style={{ marginBottom: styleHelpers.spacing.lg }}>
        <Space>
          <Button
            type="primary"
            icon={<BarChartOutlined />}
            onClick={handleAnalyze}
            loading={loading}
          >
            分析队列数据
          </Button>
          <Button
            type="primary"
            danger
            icon={<ClearOutlined />}
            onClick={handleCleanup}
            loading={cleanupLoading}
            disabled={!analysisResult || analysisResult.invalidWorkOrderIds === 0}
          >
            清理无效数据
          </Button>
        </Space>
      </Card>

      {/* 分析结果统计 */}
      {analysisResult && (
        <Card title="队列数据分析结果" style={{ marginBottom: styleHelpers.spacing.lg }}>
          <Row gutter={16}>
            <Col span={6}>
              <Statistic
                title="总工位数"
                value={analysisResult.totalWorkstations}
                prefix={<CheckCircleOutlined />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="有队列的工位"
                value={analysisResult.workstationsWithQueues}
                valueStyle={{ color: '#1890ff' }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="有效批次号"
                value={analysisResult.validBatchNumbers}
                valueStyle={{ color: '#52c41a' }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="无效工单ID"
                value={analysisResult.invalidWorkOrderIds}
                valueStyle={{ color: analysisResult.invalidWorkOrderIds > 0 ? '#ff4d4f' : '#52c41a' }}
              />
            </Col>
          </Row>

          {analysisResult.invalidWorkOrderIds > 0 && (
            <Alert
              className="mt-4"
              message="发现无效数据"
              description={`检测到 ${analysisResult.invalidWorkOrderIds} 个工单ID格式的无效数据，建议立即清理。`}
              type="warning"
              showIcon
            />
          )}

          {analysisResult.invalidWorkOrderIds === 0 && (
            <Alert
              className="mt-4"
              message="数据格式正确"
              description="所有工位队列数据格式正确，无需清理。"
              type="success"
              showIcon
            />
          )}
        </Card>
      )}

      {/* 详细分析结果 */}
      {analysisResult && (
        <Card title="工位队列详细分析" style={{ marginBottom: styleHelpers.spacing.lg }}>
          <Table
            columns={analysisColumns}
            dataSource={analysisResult.details}
            rowKey="workstationId"
            pagination={{ pageSize: 10 }}
            scroll={{ x: 800 }}
          />
        </Card>
      )}

      {/* 清理结果 */}
      {cleanupResult && (
        <Card title="清理结果" style={{ marginBottom: styleHelpers.spacing.lg }}>
          <Row gutter={16} style={{ marginBottom: styleHelpers.spacing.md }}>
            <Col span={8}>
              <Statistic
                title="清理工位数"
                value={cleanupResult.cleanedWorkstations}
                valueStyle={{ color: '#52c41a' }}
              />
            </Col>
            <Col span={8}>
              <Statistic
                title="清理项目数"
                value={cleanupResult.removedItems}
                valueStyle={{ color: '#ff4d4f' }}
              />
            </Col>
            <Col span={8}>
              <Statistic
                title="清理完成率"
                value={cleanupResult.totalWorkstations > 0 ? 
                  Math.round((cleanupResult.cleanedWorkstations / cleanupResult.totalWorkstations) * 100) : 0}
                suffix="%"
                valueStyle={{ color: '#1890ff' }}
              />
            </Col>
          </Row>

          {cleanupResult.cleanupDetails.length > 0 && (
            <Table
              columns={cleanupColumns}
              dataSource={cleanupResult.cleanupDetails}
              rowKey="workstationId"
              pagination={{ pageSize: 10 }}
              scroll={{ x: 800 }}
            />
          )}
        </Card>
      )}
    </div>
  )
}

export default function QueueCleanerPage() {
  return (
    <App>
      <QueueCleanerPageComponent />
    </App>
  )
}
