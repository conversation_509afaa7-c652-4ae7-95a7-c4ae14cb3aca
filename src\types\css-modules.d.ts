/**
 * CSS Modules类型声明
 * 为CSS Modules文件提供TypeScript类型支持
 */

declare module '*.module.css' {
  const classes: { [key: string]: string }
  export default classes
}

declare module '*.module.scss' {
  const classes: { [key: string]: string }
  export default classes
}

declare module '*.module.sass' {
  const classes: { [key: string]: string }
  export default classes
}

declare module '*.module.less' {
  const classes: { [key: string]: string }
  export default classes
}

declare module '*.module.styl' {
  const classes: { [key: string]: string }
  export default classes
}

declare module '*.module.stylus' {
  const classes: { [key: string]: string }
  export default classes
}

// 为常用的CSS Modules文件提供具体的类型定义
declare module '*/common.module.css' {
  interface CommonClasses {
    // 卡片样式
    card: string
    cardHover: string
    cardShadowSm: string
    cardShadowMd: string
    cardShadowLg: string
    
    // 布局样式
    flexCenter: string
    flexBetween: string
    flexStart: string
    flexEnd: string
    flexCol: string
    flexColCenter: string
    flexWrap: string
    flexGrow: string
    flexShrink: string
    
    // 网格布局
    grid: string
    gridCols1: string
    gridCols2: string
    gridCols3: string
    gridCols4: string
    gridGap2: string
    gridGap4: string
    gridGap6: string
    
    // 间距样式
    p1: string
    p2: string
    p3: string
    p4: string
    p6: string
    p8: string
    px1: string
    px2: string
    px3: string
    px4: string
    px6: string
    py1: string
    py2: string
    py3: string
    py4: string
    py6: string
    m1: string
    m2: string
    m3: string
    m4: string
    m6: string
    m8: string
    mx1: string
    mx2: string
    mx4: string
    mx6: string
    my1: string
    my2: string
    my4: string
    my6: string
    mb2: string
    mb4: string
    mb6: string
    mt2: string
    mt4: string
    mt6: string
    
    // 尺寸样式
    wFull: string
    hFull: string
    minHScreen: string
    maxWFull: string
    
    // 背景色
    bgWhite: string
    bgGray50: string
    bgGray100: string
    bgGray200: string
    bgPrimary50: string
    bgPrimary100: string
    bgPrimary500: string
    
    // 文本颜色
    textGray600: string
    textGray700: string
    textGray900: string
    textPrimary500: string
    textPrimary600: string
    textWhite: string
    
    // 字体大小
    textXs: string
    textSm: string
    textBase: string
    textLg: string
    textXl: string
    text2xl: string
    
    // 字体粗细
    fontNormal: string
    fontMedium: string
    fontSemibold: string
    fontBold: string
    
    // 文本对齐
    textLeft: string
    textCenter: string
    textRight: string
    
    // 边框
    border: string
    borderGray200: string
    borderGray300: string
    borderB: string
    borderT: string
    borderR: string
    borderL: string
    
    // 圆角
    rounded: string
    roundedMd: string
    roundedLg: string
    roundedXl: string
    roundedFull: string
    
    // 阴影
    shadowSm: string
    shadowMd: string
    shadowLg: string
    shadowNone: string
    
    // 过渡动画
    transition: string
    transitionFast: string
    transitionSlow: string
    
    // 显示/隐藏
    hidden: string
    block: string
    inlineBlock: string
    inline: string
    
    // 定位
    relative: string
    absolute: string
    fixed: string
    sticky: string
    
    // 溢出
    overflowHidden: string
    overflowAuto: string
    overflowScroll: string
    
    // 光标
    cursorPointer: string
    cursorDefault: string
    cursorNotAllowed: string
    
    // 选择状态
    selectNone: string
    selectText: string
    selectAll: string
    
    // 响应式工具类
    mdBlock: string
    mdHidden: string
    mdFlex: string
    mdGrid: string
    mdGridCols2: string
    mdGridCols3: string
    mdP6: string
    mdTextLg: string
  }
  
  const classes: CommonClasses
  export default classes
}

declare module '*/layout.module.css' {
  interface LayoutClasses {
    // 主布局
    mainLayout: string
    layoutHeader: string
    layoutSider: string
    layoutContent: string
    layoutFooter: string
    
    // 页面容器
    pageContainer: string
    pageContent: string
    pageHeader: string
    pageTitle: string
    pageDescription: string
    
    // 卡片容器
    cardContainer: string
    cardHeader: string
    cardTitle: string
    cardDescription: string
    cardBody: string
    cardFooter: string
    
    // 侧边栏
    sidebar: string
    sidebarOpen: string
    sidebarCollapsed: string
    sidebarHeader: string
    sidebarContent: string
    
    // 移动端抽屉
    mobileDrawer: string
    drawerOverlay: string
    drawerOverlayVisible: string
    
    // 内容区域
    contentArea: string
    contentAreaCollapsed: string
    contentMain: string
    
    // 网格布局
    gridLayout: string
    gridCols1: string
    gridCols2: string
    gridCols3: string
    gridCols4: string
    
    // 弹性布局
    flexLayout: string
    flexCol: string
    flexWrap: string
    
    // 容器尺寸
    containerSm: string
    containerMd: string
    containerLg: string
    containerXl: string
    containerFull: string
    
    // 分隔线
    divider: string
    dividerVertical: string
    
    // 间距工具
    spacingXs: string
    spacingSm: string
    spacingMd: string
    spacingLg: string
    spacingXl: string
  }
  
  const classes: LayoutClasses
  export default classes
}

declare module '*/animations.module.css' {
  interface AnimationClasses {
    // 基础过渡
    transition: string
    transitionFast: string
    transitionSlow: string
    transitionColors: string
    transitionOpacity: string
    transitionTransform: string
    transitionShadow: string
    
    // 悬停效果
    hoverScale: string
    hoverScaleSm: string
    hoverScaleLg: string
    hoverLift: string
    hoverLiftSm: string
    hoverLiftLg: string
    hoverShadow: string
    hoverShadowSm: string
    hoverShadowLg: string
    hoverOpacity: string
    hoverBrightness: string
    
    // 焦点效果
    focusRing: string
    focusRingInset: string
    focusVisible: string
    
    // 动画类
    animateFadeIn: string
    animateFadeOut: string
    animateFadeInUp: string
    animateFadeInDown: string
    animateFadeInLeft: string
    animateFadeInRight: string
    animateSlideInUp: string
    animateSlideInDown: string
    animateSlideInLeft: string
    animateSlideInRight: string
    animateScaleIn: string
    animateScaleOut: string
    animateSpin: string
    animatePulse: string
    animateBounce: string
    
    // 动画延迟
    animateDelay75: string
    animateDelay100: string
    animateDelay150: string
    animateDelay200: string
    animateDelay300: string
    animateDelay500: string
    animateDelay700: string
    animateDelay1000: string
    
    // 动画持续时间
    animateDuration75: string
    animateDuration100: string
    animateDuration150: string
    animateDuration200: string
    animateDuration300: string
    animateDuration500: string
    animateDuration700: string
    animateDuration1000: string
    
    // 加载动画
    loading: string
    skeleton: string
  }
  
  const classes: AnimationClasses
  export default classes
}
