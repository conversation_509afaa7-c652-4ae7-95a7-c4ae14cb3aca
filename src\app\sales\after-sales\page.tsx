'use client'

import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Input,
  Select,
  Modal,
  Form,
  Row,
  Col,
  Statistic,
  message,
  Popconfirm,
  Descriptions,
  Alert,
  Rate,
  Timeline,
  Steps,
  Tooltip
} from 'antd'
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  ExportOutlined,
  CustomerServiceOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ScanOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import { AfterSalesService, QualityTraceability } from '@/types'

const { Option } = Select
const { TextArea } = Input
const { Step } = Steps

const AfterSalesManagement: React.FC = () => {
  const [services, setServices] = useState<AfterSalesService[]>([])
  const [loading, setLoading] = useState(false)
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false)
  const [isTraceModalVisible, setIsTraceModalVisible] = useState(false)
  const [editingService, setEditingService] = useState<AfterSalesService | null>(null)
  const [selectedService, setSelectedService] = useState<AfterSalesService | null>(null)
  const [traceabilityData, setTraceabilityData] = useState<QualityTraceability | null>(null)
  const [form] = Form.useForm()
  const [searchText, setSearchText] = useState('')
  const [filterStatus, setFilterStatus] = useState('')
  const [filterType, setFilterType] = useState('')
  const [filterUrgency, setFilterUrgency] = useState('')

  // 模拟数据
  useEffect(() => {
    const mockServices: AfterSalesService[] = [
      {
        id: '1',
        serviceNumber: 'AS-2024-001',
        customerId: '1',
        customerName: '上海包装材料有限公司',
        contactPerson: '张经理',
        contactPhone: '13800138001',
        serviceType: 'complaint',
        relatedOrderNumber: 'SO-2024-001',
        productBatchNumber: 'P20240115-001',
        issueDescription: '产品表面有轻微划痕，影响外观质量',
        urgencyLevel: 'medium',
        status: 'processing',
        assignedTo: '客服专员A',
        resolutionPlan: '安排质检人员现场查看，确认问题原因并提供解决方案',
        resolutionResult: '已确认为运输过程中造成的划痕，将重新发货',
        customerSatisfaction: 4,
        traceabilityInfo: {
          id: '1',
          batchNumber: 'P20240115-001',
          productModelCode: 'CP-202',
          productionDate: '2024-01-15',
          workstation: 'WS03',
          operator: '操作员张三',
          moldCode: 'M-JX-05',
          productionOrderNumber: 'PO-2024-001',
          qualityCheckResult: '合格',
          rawMaterialBatch: 'RM-20240110-001',
          environmentalCertificate: 'ENV-2024-001',
          createdAt: '2024-01-15T08:00:00'
        },
        createdAt: '2024-01-26T09:00:00',
        updatedAt: '2024-01-26T15:00:00',
        resolvedAt: '2024-01-26T15:00:00'
      },
      {
        id: '2',
        serviceNumber: 'AS-2024-002',
        customerId: '2',
        customerName: '北京绿色包装科技公司',
        contactPerson: '王总',
        contactPhone: '13900139002',
        serviceType: 'return',
        relatedOrderNumber: 'SO-2024-002',
        productBatchNumber: 'P20240118-001',
        issueDescription: '客户订单变更，需要退回部分产品',
        urgencyLevel: 'low',
        status: 'resolved',
        assignedTo: '客服专员B',
        resolutionPlan: '确认退货原因，安排物流回收',
        resolutionResult: '已安排物流回收，退款已处理',
        customerSatisfaction: 5,
        createdAt: '2024-01-28T10:00:00',
        updatedAt: '2024-01-29T16:00:00',
        resolvedAt: '2024-01-29T16:00:00'
      },
      {
        id: '3',
        serviceNumber: 'AS-2024-003',
        customerId: '3',
        customerName: '广州环保餐具厂',
        contactPerson: '陈主任',
        contactPhone: '13700137003',
        serviceType: 'repair',
        productBatchNumber: 'P20240120-001',
        issueDescription: '产品出现质量问题，需要技术支持',
        urgencyLevel: 'high',
        status: 'pending',
        assignedTo: '技术专员C',
        resolutionPlan: '派遣技术人员现场支持',
        createdAt: '2024-01-30T14:00:00',
        updatedAt: '2024-01-30T14:00:00'
      }
    ]
    setServices(mockServices)
  }, [])

  // 获取服务类型标签
  const getServiceTypeTag = (type: string) => {
    const typeMap = {
      'complaint': { color: 'red', text: '投诉' },
      'return': { color: 'orange', text: '退货' },
      'exchange': { color: 'blue', text: '换货' },
      'repair': { color: 'purple', text: '维修' },
      'consultation': { color: 'green', text: '咨询' }
    }
    const config = typeMap[type as keyof typeof typeMap] || { color: 'default', text: '其他' }
    return <Tag color={config.color}>{config.text}</Tag>
  }

  // 获取紧急程度标签
  const getUrgencyTag = (level: string) => {
    const levelMap = {
      'low': { color: 'green', text: '低' },
      'medium': { color: 'orange', text: '中' },
      'high': { color: 'red', text: '高' },
      'urgent': { color: 'volcano', text: '紧急' }
    }
    const config = levelMap[level as keyof typeof levelMap] || { color: 'default', text: '未知' }
    return <Tag color={config.color}>{config.text}</Tag>
  }

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const statusMap = {
      'pending': { color: 'orange', text: '待处理', icon: <ClockCircleOutlined /> },
      'processing': { color: 'blue', text: '处理中', icon: <CustomerServiceOutlined /> },
      'resolved': { color: 'green', text: '已解决', icon: <CheckCircleOutlined /> },
      'closed': { color: 'default', text: '已关闭', icon: <CheckCircleOutlined /> }
    }
    const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: '未知', icon: null }
    return (
      <Tag color={config.color} icon={config.icon}>
        {config.text}
      </Tag>
    )
  }

  // 表格列定义
  const columns: ColumnsType<AfterSalesService> = [
    {
      title: '服务单号',
      dataIndex: 'serviceNumber',
      key: 'serviceNumber',
      width: 140,
      fixed: 'left'
    },
    {
      title: '客户信息',
      key: 'customerInfo',
      width: 180,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{record.customerName}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.contactPerson} | {record.contactPhone}
          </div>
        </div>
      )
    },
    {
      title: '服务类型',
      dataIndex: 'serviceType',
      key: 'serviceType',
      width: 100,
      render: (type: string) => getServiceTypeTag(type)
    },
    {
      title: '紧急程度',
      dataIndex: 'urgencyLevel',
      key: 'urgencyLevel',
      width: 100,
      render: (level: string) => getUrgencyTag(level)
    },
    {
      title: '问题描述',
      dataIndex: 'issueDescription',
      key: 'issueDescription',
      width: 250,
      ellipsis: true,
      render: (text: string) => (
        <Tooltip title={text}>
          {text}
        </Tooltip>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => getStatusTag(status)
    },
    {
      title: '处理人',
      dataIndex: 'assignedTo',
      key: 'assignedTo',
      width: 100
    },
    {
      title: '客户满意度',
      dataIndex: 'customerSatisfaction',
      key: 'customerSatisfaction',
      width: 120,
      render: (rating: number) => rating ? <Rate disabled value={rating} /> : '-'
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (time: string) => new Date(time).toLocaleString(),
      sorter: (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
    },
    {
      title: '操作',
      key: 'action',
      width: 250,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button 
            type="link" 
            icon={<EyeOutlined />} 
            onClick={() => handleViewDetail(record)}
          >
            详情
          </Button>
          <Button 
            type="link" 
            icon={<EditOutlined />} 
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          {record.productBatchNumber && (
            <Button 
              type="link" 
              icon={<ScanOutlined />} 
              onClick={() => handleTrace(record)}
            >
              追溯
            </Button>
          )}
          <Popconfirm
            title="确定要删除这个服务记录吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ]

  // 过滤后的服务数据
  const filteredServices = services.filter(service => {
    const matchesSearch = !searchText || 
      service.serviceNumber.toLowerCase().includes(searchText.toLowerCase()) ||
      service.customerName.toLowerCase().includes(searchText.toLowerCase()) ||
      service.contactPerson.toLowerCase().includes(searchText.toLowerCase()) ||
      service.issueDescription.toLowerCase().includes(searchText.toLowerCase())
    
    const matchesStatus = !filterStatus || service.status === filterStatus
    const matchesType = !filterType || service.serviceType === filterType
    const matchesUrgency = !filterUrgency || service.urgencyLevel === filterUrgency
    
    return matchesSearch && matchesStatus && matchesType && matchesUrgency
  })

  // 统计数据
  const stats = {
    total: services.length,
    pending: services.filter(s => s.status === 'pending').length,
    processing: services.filter(s => s.status === 'processing').length,
    resolved: services.filter(s => s.status === 'resolved').length,
    averageSatisfaction: services.filter(s => s.customerSatisfaction).length > 0 ? 
      Math.round(services.filter(s => s.customerSatisfaction).reduce((sum, s) => sum + (s.customerSatisfaction || 0), 0) / 
      services.filter(s => s.customerSatisfaction).length * 10) / 10 : 0,
    highUrgency: services.filter(s => s.urgencyLevel === 'high' || s.urgencyLevel === 'urgent').length
  }

  const handleCreate = () => {
    setEditingService(null)
    setIsModalVisible(true)
    form.resetFields()
  }

  const handleEdit = (service: AfterSalesService) => {
    setEditingService(service)
    setIsModalVisible(true)
    form.setFieldsValue(service)
  }

  const handleViewDetail = (service: AfterSalesService) => {
    setSelectedService(service)
    setIsDetailModalVisible(true)
  }

  const handleTrace = (service: AfterSalesService) => {
    setSelectedService(service)
    setTraceabilityData(service.traceabilityInfo || null)
    setIsTraceModalVisible(true)
  }

  const handleDelete = (id: string) => {
    setServices(services.filter(s => s.id !== id))
    message.success('服务记录删除成功')
  }

  const handleModalOk = () => {
    form.validateFields().then(values => {
      const now = new Date().toISOString()

      if (editingService) {
        const updatedServices = services.map(s =>
          s.id === editingService.id
            ? { ...s, ...values, updatedAt: now }
            : s
        )
        setServices(updatedServices)
        message.success('服务记录更新成功')
      } else {
        const newService: AfterSalesService = {
          id: Date.now().toString(),
          serviceNumber: `AS-${new Date().getFullYear()}-${String(services.length + 1).padStart(3, '0')}`,
          ...values,
          createdAt: now,
          updatedAt: now
        }
        setServices([...services, newService])
        message.success('服务记录创建成功')
      }
      setIsModalVisible(false)
      form.resetFields()
    })
  }

  const handleModalCancel = () => {
    setIsModalVisible(false)
    form.resetFields()
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: '24px' }}>
        <h1 style={{ fontSize: '24px', fontWeight: 'bold', margin: '0 0 8px 0' }}>售后服务管理</h1>
        <p style={{ color: '#666', margin: 0 }}>质量追溯、客户投诉处理、服务记录管理</p>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="服务总数"
              value={stats.total}
              suffix="个"
              prefix={<CustomerServiceOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="待处理"
              value={stats.pending}
              suffix="个"
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="平均满意度"
              value={stats.averageSatisfaction}
              suffix="/5"
              valueStyle={{ color: stats.averageSatisfaction >= 4 ? '#3f8600' : '#cf1322' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="高紧急度"
              value={stats.highUrgency}
              suffix="个"
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 服务状态分布 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="待处理"
              value={stats.pending}
              suffix="个"
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="处理中"
              value={stats.processing}
              suffix="个"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="已解决"
              value={stats.resolved}
              suffix="个"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 操作区域 */}
      <Card>
        <Row justify="space-between" align="middle" gutter={[16, 16]}>
          <Col xs={24} lg={18}>
            <Row gutter={[16, 16]}>
            <Input
              placeholder="搜索服务单号、客户名称或问题描述"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: '100%' }}
            />
            <Select
              placeholder="服务状态"
              value={filterStatus}
              onChange={setFilterStatus}
              style={{ width: '100%' }}
              allowClear
            >
              <Option value="pending">待处理</Option>
              <Option value="processing">处理中</Option>
              <Option value="resolved">已解决</Option>
              <Option value="closed">已关闭</Option>
            </Select>
            <Select
              placeholder="服务类型"
              value={filterType}
              onChange={setFilterType}
              style={{ width: '100%' }}
              allowClear
            >
              <Option value="complaint">投诉</Option>
              <Option value="return">退货</Option>
              <Option value="exchange">换货</Option>
              <Option value="repair">维修</Option>
              <Option value="consultation">咨询</Option>
            </Select>
            <Select
              placeholder="紧急程度"
              value={filterUrgency}
              onChange={setFilterUrgency}
              style={{ width: '100%' }}
              allowClear
            >
              <Option value="low">低</Option>
              <Option value="medium">中</Option>
              <Option value="high">高</Option>
              <Option value="urgent">紧急</Option>
            </Select>
            </Row>
          </Col>
          <Col xs={24} lg={6}>
            <Row gutter={[8, 8]} justify="end">
              <Col>
                <Button icon={<ExportOutlined />}>导出</Button>
              </Col>
              <Col>
                <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
                  新建服务
                </Button>
              </Col>
            </Row>
          </Col>
        </Row>
      </Card>

      {/* 服务列表 */}
      <Card title="售后服务列表">
        <Table
          columns={columns}
          dataSource={filteredServices}
          rowKey="id"
          loading={loading}
          pagination={{
            total: filteredServices.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          scroll={{ x: 1600 }}
        />
      </Card>

      {/* 新建/编辑服务模态框 */}
      <Modal
        title={editingService ? '编辑售后服务' : '新建售后服务'}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={800}
        okText="确认"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            serviceType: 'complaint',
            urgencyLevel: 'medium',
            status: 'pending'
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="customerId"
                label="客户ID"
                rules={[{ required: true, message: '请输入客户ID' }]}
              >
                <Input placeholder="请输入客户ID" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="customerName"
                label="客户名称"
                rules={[{ required: true, message: '请输入客户名称' }]}
              >
                <Input placeholder="请输入客户名称" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="contactPerson"
                label="联系人"
                rules={[{ required: true, message: '请输入联系人' }]}
              >
                <Input placeholder="请输入联系人" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="contactPhone"
                label="联系电话"
                rules={[{ required: true, message: '请输入联系电话' }]}
              >
                <Input placeholder="请输入联系电话" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="serviceType"
                label="服务类型"
                rules={[{ required: true, message: '请选择服务类型' }]}
              >
                <Select>
                  <Option value="complaint">投诉</Option>
                  <Option value="return">退货</Option>
                  <Option value="exchange">换货</Option>
                  <Option value="repair">维修</Option>
                  <Option value="consultation">咨询</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="urgencyLevel"
                label="紧急程度"
                rules={[{ required: true, message: '请选择紧急程度' }]}
              >
                <Select>
                  <Option value="low">低</Option>
                  <Option value="medium">中</Option>
                  <Option value="high">高</Option>
                  <Option value="urgent">紧急</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="status"
                label="状态"
                rules={[{ required: true, message: '请选择状态' }]}
              >
                <Select>
                  <Option value="pending">待处理</Option>
                  <Option value="processing">处理中</Option>
                  <Option value="resolved">已解决</Option>
                  <Option value="closed">已关闭</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="relatedOrderNumber"
                label="关联订单号"
              >
                <Input placeholder="请输入关联订单号" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="productBatchNumber"
                label="产品批号"
              >
                <Input placeholder="请输入产品批号" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="issueDescription"
            label="问题描述"
            rules={[{ required: true, message: '请输入问题描述' }]}
          >
            <TextArea rows={3} placeholder="请详细描述问题" />
          </Form.Item>

          <Form.Item
            name="assignedTo"
            label="处理人"
            rules={[{ required: true, message: '请输入处理人' }]}
          >
            <Select placeholder="请选择处理人">
              <Option value="客服专员A">客服专员A</Option>
              <Option value="客服专员B">客服专员B</Option>
              <Option value="技术专员C">技术专员C</Option>
              <Option value="质检专员D">质检专员D</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="resolutionPlan"
            label="解决方案"
          >
            <TextArea rows={3} placeholder="请输入解决方案" />
          </Form.Item>

          <Form.Item
            name="resolutionResult"
            label="处理结果"
          >
            <TextArea rows={3} placeholder="请输入处理结果" />
          </Form.Item>

          <Form.Item
            name="customerSatisfaction"
            label="客户满意度"
          >
            <Rate />
          </Form.Item>
        </Form>
      </Modal>

      {/* 服务详情模态框 */}
      <Modal
        title="售后服务详情"
        open={isDetailModalVisible}
        onCancel={() => setIsDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setIsDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={900}
      >
        {selectedService && selectedService.serviceNumber && selectedService.customerName && (
          <div>
            <Descriptions column={2} bordered>
              <Descriptions.Item label="服务单号">{selectedService.serviceNumber}</Descriptions.Item>
              <Descriptions.Item label="客户名称">{selectedService.customerName}</Descriptions.Item>
              <Descriptions.Item label="联系人">{selectedService.contactPerson}</Descriptions.Item>
              <Descriptions.Item label="联系电话">{selectedService.contactPhone}</Descriptions.Item>
              <Descriptions.Item label="服务类型">
                {getServiceTypeTag(selectedService.serviceType)}
              </Descriptions.Item>
              <Descriptions.Item label="紧急程度">
                {getUrgencyTag(selectedService.urgencyLevel)}
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                {getStatusTag(selectedService.status)}
              </Descriptions.Item>
              <Descriptions.Item label="处理人">{selectedService.assignedTo}</Descriptions.Item>
              <Descriptions.Item label="关联订单号">
                {selectedService.relatedOrderNumber || '无'}
              </Descriptions.Item>
              <Descriptions.Item label="产品批号">
                {selectedService.productBatchNumber || '无'}
              </Descriptions.Item>
              <Descriptions.Item label="客户满意度">
                {selectedService.customerSatisfaction ?
                  <Rate disabled value={selectedService.customerSatisfaction} /> : '未评价'}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {new Date(selectedService.createdAt).toLocaleString()}
              </Descriptions.Item>
              <Descriptions.Item label="更新时间">
                {new Date(selectedService.updatedAt).toLocaleString()}
              </Descriptions.Item>
              <Descriptions.Item label="解决时间">
                {selectedService.resolvedAt ?
                  new Date(selectedService.resolvedAt).toLocaleString() : '未解决'}
              </Descriptions.Item>
              <Descriptions.Item label="问题描述" span={2}>
                {selectedService.issueDescription}
              </Descriptions.Item>
              <Descriptions.Item label="解决方案" span={2}>
                {selectedService.resolutionPlan || '待制定'}
              </Descriptions.Item>
              <Descriptions.Item label="处理结果" span={2}>
                {selectedService.resolutionResult || '处理中'}
              </Descriptions.Item>
            </Descriptions>

            <div style={{ marginTop: 24 }}>
              <h4>服务处理流程</h4>
              <Steps size="small" current={
                selectedService.status === 'pending' ? 0 :
                selectedService.status === 'processing' ? 1 :
                selectedService.status === 'resolved' ? 2 : 3
              }>
                <Step
                  title="服务创建"
                  description={new Date(selectedService.createdAt).toLocaleString()}
                  icon={<CustomerServiceOutlined />}
                />
                <Step
                  title="开始处理"
                  description={selectedService.assignedTo}
                  icon={<ClockCircleOutlined />}
                />
                <Step
                  title="问题解决"
                  description={selectedService.resolvedAt ?
                    new Date(selectedService.resolvedAt).toLocaleString() : '处理中'}
                  icon={<CheckCircleOutlined />}
                />
                <Step
                  title="服务关闭"
                  description="客户确认满意"
                  icon={<CheckCircleOutlined />}
                />
              </Steps>
            </div>

            {selectedService.productBatchNumber && (
              <div style={{ marginTop: 24 }}>
                <Alert
                  message="质量追溯"
                  description={`产品批号: ${selectedService.productBatchNumber}，点击追溯按钮查看详细生产信息`}
                  type="info"
                  showIcon
                  action={
                    <Button
                      size="small"
                      type="primary"
                      icon={<ScanOutlined />}
                      onClick={() => handleTrace(selectedService)}
                    >
                      质量追溯
                    </Button>
                  }
                />
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* 质量追溯模态框 */}
      <Modal
        title="质量追溯信息"
        open={isTraceModalVisible}
        onCancel={() => setIsTraceModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setIsTraceModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {traceabilityData && (
          <div>
            <Alert
              message="产品质量追溯"
              description="通过产品批号追溯完整的生产过程和质量信息"
              type="success"
              showIcon
              style={{ marginBottom: 16 }}
            />

            <Descriptions column={2} bordered>
              <Descriptions.Item label="产品批号">{traceabilityData.batchNumber}</Descriptions.Item>
              <Descriptions.Item label="产品型号">{traceabilityData.productModelCode}</Descriptions.Item>
              <Descriptions.Item label="生产日期">{traceabilityData.productionDate}</Descriptions.Item>
              <Descriptions.Item label="生产工位">{traceabilityData.workstation}</Descriptions.Item>
              <Descriptions.Item label="操作员">{traceabilityData.operator}</Descriptions.Item>
              <Descriptions.Item label="模具编码">{traceabilityData.moldCode}</Descriptions.Item>
              <Descriptions.Item label="生产订单号">{traceabilityData.productionOrderNumber}</Descriptions.Item>
              <Descriptions.Item label="质检结果">
                <Tag color={traceabilityData.qualityCheckResult === '合格' ? 'green' : 'red'}>
                  {traceabilityData.qualityCheckResult}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="原料批次">{traceabilityData.rawMaterialBatch}</Descriptions.Item>
              <Descriptions.Item label="环保证书">{traceabilityData.environmentalCertificate}</Descriptions.Item>
              <Descriptions.Item label="记录时间" span={2}>
                {new Date(traceabilityData.createdAt).toLocaleString()}
              </Descriptions.Item>
            </Descriptions>

            <div style={{ marginTop: 24 }}>
              <h4>生产追溯时间线</h4>
              <Timeline>
                <Timeline.Item color="blue">
                  <div>
                    <div style={{ fontWeight: 'bold' }}>原料投入</div>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      原料批次: {traceabilityData.rawMaterialBatch}
                    </div>
                  </div>
                </Timeline.Item>
                <Timeline.Item color="green">
                  <div>
                    <div style={{ fontWeight: 'bold' }}>开始生产</div>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      生产日期: {traceabilityData.productionDate} |
                      工位: {traceabilityData.workstation} |
                      操作员: {traceabilityData.operator}
                    </div>
                  </div>
                </Timeline.Item>
                <Timeline.Item color="orange">
                  <div>
                    <div style={{ fontWeight: 'bold' }}>模具成型</div>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      模具编码: {traceabilityData.moldCode}
                    </div>
                  </div>
                </Timeline.Item>
                <Timeline.Item color="purple">
                  <div>
                    <div style={{ fontWeight: 'bold' }}>质量检验</div>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      检验结果: {traceabilityData.qualityCheckResult}
                    </div>
                  </div>
                </Timeline.Item>
                <Timeline.Item color="cyan">
                  <div>
                    <div style={{ fontWeight: 'bold' }}>环保认证</div>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      证书编号: {traceabilityData.environmentalCertificate}
                    </div>
                  </div>
                </Timeline.Item>
                <Timeline.Item color="green">
                  <div>
                    <div style={{ fontWeight: 'bold' }}>产品入库</div>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      批号: {traceabilityData.batchNumber}
                    </div>
                  </div>
                </Timeline.Item>
              </Timeline>
            </div>
          </div>
        )}
      </Modal>
    </div>
  )
}

export default AfterSalesManagement
