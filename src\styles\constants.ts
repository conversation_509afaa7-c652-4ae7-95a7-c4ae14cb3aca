/**
 * 样式常量定义
 * 统一的样式常量，用于保持设计一致性
 */

// 颜色常量
export const COLORS = {
  // 主色调
  PRIMARY: {
    50: '#f0f9ff',
    100: '#e0f2fe',
    500: '#0ea5e9',
    600: '#0284c7',
    700: '#0369a1',
  },
  
  // 灰度色
  GRAY: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111827',
  },
  
  // 语义化颜色
  SUCCESS: '#10b981',
  WARNING: '#f59e0b',
  ERROR: '#ef4444',
  INFO: '#3b82f6',
  
  // 背景色
  BACKGROUND: {
    WHITE: '#ffffff',
    LIGHT: '#f9fafb',
    GRAY: '#f3f4f6',
  },
  
  // 文本颜色
  TEXT: {
    PRIMARY: '#111827',
    SECONDARY: '#6b7280',
    TERTIARY: '#9ca3af',
    INVERSE: '#ffffff',
  },
} as const

// 间距常量
export const SPACING = {
  XS: 4,
  SM: 8,
  MD: 16,
  LG: 24,
  XL: 32,
  XXL: 48,
  XXXL: 64,
} as const

// 字体常量
export const TYPOGRAPHY = {
  FONT_FAMILY: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif',
  
  FONT_SIZE: {
    XS: 12,
    SM: 14,
    BASE: 16,
    LG: 18,
    XL: 20,
    '2XL': 24,
    '3XL': 30,
    '4XL': 36,
  },
  
  FONT_WEIGHT: {
    NORMAL: 400,
    MEDIUM: 500,
    SEMIBOLD: 600,
    BOLD: 700,
  },
  
  LINE_HEIGHT: {
    TIGHT: 1.25,
    NORMAL: 1.5,
    RELAXED: 1.75,
  },
} as const

// 圆角常量
export const BORDER_RADIUS = {
  NONE: 0,
  SM: 4,
  MD: 8,
  LG: 12,
  XL: 16,
  FULL: 9999,
} as const

// 阴影常量
export const SHADOWS = {
  NONE: 'none',
  SM: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  MD: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
  LG: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
  XL: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
  INNER: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
} as const

// 边框常量
export const BORDERS = {
  WIDTH: {
    NONE: 0,
    THIN: 1,
    MEDIUM: 2,
    THICK: 4,
  },
  
  STYLE: {
    SOLID: 'solid',
    DASHED: 'dashed',
    DOTTED: 'dotted',
  },
  
  COLOR: {
    DEFAULT: COLORS.GRAY[200],
    LIGHT: COLORS.GRAY[100],
    DARK: COLORS.GRAY[300],
  },
} as const

// 过渡动画常量
export const TRANSITIONS = {
  DURATION: {
    FAST: '150ms',
    NORMAL: '200ms',
    SLOW: '300ms',
  },
  
  TIMING: {
    EASE: 'ease',
    EASE_IN: 'ease-in',
    EASE_OUT: 'ease-out',
    EASE_IN_OUT: 'ease-in-out',
    LINEAR: 'linear',
  },
  
  // 预定义的过渡效果
  ALL_FAST: 'all 150ms ease-in-out',
  ALL_NORMAL: 'all 200ms ease-in-out',
  ALL_SLOW: 'all 300ms ease-in-out',
} as const

// 断点常量
export const BREAKPOINTS = {
  SM: '640px',
  MD: '768px',
  LG: '1024px',
  XL: '1280px',
  XXL: '1536px',
} as const

// Z-index常量
export const Z_INDEX = {
  DROPDOWN: 1000,
  STICKY: 1020,
  FIXED: 1030,
  MODAL_BACKDROP: 1040,
  MODAL: 1050,
  POPOVER: 1060,
  TOOLTIP: 1070,
  TOAST: 1080,
} as const

// 布局常量
export const LAYOUT = {
  HEADER_HEIGHT: 64,
  SIDEBAR_WIDTH: 256,
  SIDEBAR_COLLAPSED_WIDTH: 80,
  FOOTER_HEIGHT: 48,
  
  CONTAINER: {
    MAX_WIDTH: '1200px',
    PADDING: SPACING.MD,
  },
} as const

// 表单常量
export const FORM = {
  INPUT_HEIGHT: 40,
  LABEL_MARGIN_BOTTOM: SPACING.XS,
  ITEM_MARGIN_BOTTOM: SPACING.MD,
  
  VALIDATION: {
    ERROR_COLOR: COLORS.ERROR,
    SUCCESS_COLOR: COLORS.SUCCESS,
  },
} as const

// 表格常量
export const TABLE = {
  ROW_HEIGHT: 48,
  HEADER_HEIGHT: 56,
  CELL_PADDING: SPACING.SM,
  
  COLORS: {
    HEADER_BG: COLORS.GRAY[50],
    ROW_HOVER_BG: COLORS.GRAY[50],
    BORDER: COLORS.GRAY[200],
  },
} as const

// 卡片常量
export const CARD = {
  PADDING: SPACING.LG,
  BORDER_RADIUS: BORDER_RADIUS.LG,
  SHADOW: SHADOWS.SM,
  BORDER: `1px solid ${COLORS.GRAY[200]}`,
} as const

// 按钮常量
export const BUTTON = {
  HEIGHT: {
    SMALL: 32,
    MEDIUM: 40,
    LARGE: 48,
  },
  
  PADDING: {
    SMALL: `${SPACING.XS}px ${SPACING.SM}px`,
    MEDIUM: `${SPACING.SM}px ${SPACING.MD}px`,
    LARGE: `${SPACING.SM}px ${SPACING.LG}px`,
  },
  
  BORDER_RADIUS: BORDER_RADIUS.MD,
} as const

// 导出所有常量的类型
export type ColorKeys = keyof typeof COLORS
export type SpacingKeys = keyof typeof SPACING
export type TypographyKeys = keyof typeof TYPOGRAPHY
export type BorderRadiusKeys = keyof typeof BORDER_RADIUS
export type ShadowKeys = keyof typeof SHADOWS
export type TransitionKeys = keyof typeof TRANSITIONS
export type BreakpointKeys = keyof typeof BREAKPOINTS
