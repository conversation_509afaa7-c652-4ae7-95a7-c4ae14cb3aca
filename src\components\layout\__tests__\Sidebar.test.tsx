/**
 * Sidebar组件测试
 * 验证Tailwind迁移后的功能完整性
 */

import React from 'react'
import { render, screen } from '@testing-library/react'
import { Sidebar } from '../Sidebar'

// Mock useAppStore
jest.mock('@/store/useAppStore', () => ({
  useAppStore: () => ({
    sidebarCollapsed: false,
    isMobile: false,
    mobileDrawerVisible: false,
    toggleSidebar: jest.fn(),
    setMobileDrawerVisible: jest.fn()
  })
}))

// Mock useRouter and usePathname
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    pathname: '/dashboard'
  }),
  usePathname: () => '/dashboard'
}))

describe('Sidebar', () => {
  it('应该正确渲染基本结构', () => {
    render(<Sidebar />)

    // 验证Logo区域存在
    expect(screen.getByText('ERP系统')).toBeInTheDocument()
  })

  it('应该应用正确的内联样式', () => {
    const { container } = render(<Sidebar />)

    // 验证Sider存在
    const sider = container.querySelector('.ant-layout-sider')
    expect(sider).toBeInTheDocument()
  })

  it('应该渲染菜单组件', () => {
    const { container } = render(<Sidebar />)

    // 验证Menu组件存在
    const menu = container.querySelector('.ant-menu')
    expect(menu).toBeInTheDocument()
  })

})
