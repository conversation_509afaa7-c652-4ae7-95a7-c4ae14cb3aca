/**
 * 共享的产品表单组件
 * 用于产品数据模块和订单模块的产品新增功能
 */

import React, { useState } from 'react'
import {
  Form,
  Input,
  InputNumber,
  Select,
  Row,
  Col,
  Tabs
} from 'antd'
import { ToolOutlined, DollarOutlined } from '@ant-design/icons'
import { ProductModel } from '@/types'
import { ValidationUtils } from '@/utils/validationUtils'

const { Option } = Select

interface ProductFormProps {
  form: any
  editingModel?: ProductModel | null
  onFormingMoldChange?: (value: string) => void
  getProductsUsingMold?: (moldCode: string, type: 'forming' | 'hotPress') => ProductModel[]
  validateMoldFormat?: (value: string) => boolean
  checkCodeUniqueness?: (code: string, excludeId?: string) => boolean
  currentFormingMold?: string
  productModelsData?: ProductModel[]
}

const ProductForm: React.FC<ProductFormProps> = ({
  form,
  editingModel,
  onFormingMoldChange,
  getProductsUsingMold,
  validateMoldFormat,
  checkCodeUniqueness,
  currentFormingMold,
  productModelsData = []
}) => {
  const [currentFormingMoldLocal, setCurrentFormingMoldLocal] = useState(currentFormingMold || '')

  const handleFormingMoldChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setCurrentFormingMoldLocal(value)
    onFormingMoldChange?.(value)
  }

  // 使用统一验证工具
  const defaultValidateMoldFormat = (value: string): boolean => {
    return ValidationUtils.validateMoldNumber(value).isValid
  }

  const defaultCheckCodeUniqueness = (code: string, excludeId?: string): boolean => {
    return ValidationUtils.checkProductCodeUniqueness(code, productModelsData, excludeId).isValid
  }

  const validateMoldFormatFn = validateMoldFormat || defaultValidateMoldFormat
  const checkCodeUniquenessFn = checkCodeUniqueness || defaultCheckCodeUniqueness
  const getProductsUsingMoldFn = getProductsUsingMold || (() => [])

  return (
    <>
      {/* 基本信息 */}
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label="产品编码"
            name="modelCode"
            rules={[
              { required: true, message: '请输入产品编码' },
              { pattern: /^P\d{5}$/, message: '格式：P + 5位数字（如：P00001）' },
              {
                validator: (_, value) => {
                  if (!value) return Promise.resolve()
                  if (!checkCodeUniquenessFn(value, editingModel?.id)) {
                    return Promise.reject(new Error('产品编码已存在，请使用其他编码'))
                  }
                  return Promise.resolve()
                }
              }
            ]}
          >
            <Input placeholder="如：P00001（自动生成）" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label="产品名称"
            name="modelName"
            rules={[{ required: true, message: '请输入产品名称' }]}
          >
            <Input placeholder="请输入产品名称" />
          </Form.Item>
        </Col>
      </Row>

      {/* Tabs分组 */}
      <Tabs
        defaultActiveKey="mold"
        items={[
          {
            key: 'mold',
            label: <span><ToolOutlined />模具信息</span>,
            children: (
              <>
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      label="成型模具编号"
                      name="formingMold"
                      rules={[
                        { required: true, message: '请输入成型模具编号' },
                        {
                          validator: (_, value) => {
                            if (!value) return Promise.resolve()
                            if (validateMoldFormatFn(value)) {
                              return Promise.resolve()
                            }
                            return Promise.reject(new Error('模具编号格式错误，请使用格式：M-XX-XX（如：M-JX-05）'))
                          }
                        }
                      ]}
                      extra={
                        currentFormingMoldLocal &&
                        getProductsUsingMoldFn(currentFormingMoldLocal, 'forming').length > 0 && (
                          <div style={{ marginTop: '4px' }}>
                            <span style={{ color: '#1890ff', fontSize: '12px' }}>
                              💡 此模具已被 {getProductsUsingMoldFn(currentFormingMoldLocal, 'forming').length} 个产品使用：
                            </span>
                            <div style={{ fontSize: '11px', color: '#666', marginTop: '2px' }}>
                              {getProductsUsingMoldFn(currentFormingMoldLocal, 'forming')
                                .filter(p => p.id !== editingModel?.id)
                                .map(p => p.modelName)
                                .join('、')}
                            </div>
                          </div>
                        )
                      }
                    >
                      <Input
                        placeholder="如：M-JX-05（支持多产品共享）"
                        onChange={handleFormingMoldChange}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      label="成型模具单模数量"
                      name="formingMoldQuantity"
                      rules={[{ required: true, message: '请输入单模数量' }]}
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        placeholder="请输入数量"
                        suffix="个/模"
                        min={1}
                      />
                    </Form.Item>
                  </Col>
                </Row>
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      label="热压模具编号"
                      name="hotPressMold"
                      rules={[
                        { required: true, message: '请输入热压模具编号' },
                        {
                          validator: (_, value) => {
                            if (!value) return Promise.resolve()
                            if (!validateMoldFormatFn(value)) {
                              return Promise.reject(new Error('模具编号格式错误，请使用格式：M-XX-XX（如：M-RY-12）'))
                            }
                            // 热压模具保持唯一性约束
                            const existingModels = editingModel
                              ? productModelsData.filter(model => model.id !== editingModel.id)
                              : productModelsData
                            const isDuplicate = existingModels.some(model => model.hotPressMold === value)
                            if (isDuplicate) {
                              return Promise.reject(new Error('热压模具编号已存在，请使用其他编号'))
                            }
                            return Promise.resolve()
                          }
                        }
                      ]}
                    >
                      <Input placeholder="如：M-RY-12" />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      label="热压模具单模数量"
                      name="hotPressMoldQuantity"
                      rules={[{ required: true, message: '请输入单模数量' }]}
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        placeholder="请输入数量"
                        suffix="个/模"
                        min={1}
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <Form.Item
                  label="单模出数"
                  name="piecesPerMold"
                  rules={[
                    { required: true, message: '请输入单模出数' },
                    { type: 'number', min: 1, message: '单模出数必须大于0' }
                  ]}
                >
                  <InputNumber
                    style={{ width: '100%' }}
                    placeholder="用于智能排产模数计算"
                    suffix="个/模"
                    min={1}
                    precision={0}
                    step={1}
                  />
                </Form.Item>
              </>
            )
          },

          {
            key: 'price',
            label: <span><DollarOutlined />计件单价</span>,
            children: (
              <>
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      label="成型计件单价"
                      name="formingPiecePrice"
                      rules={[{ required: true, message: '请输入成型计件单价' }]}
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        placeholder="请输入单价"
                        prefix="¥"
                        suffix="/模"
                        min={0}
                        precision={2}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      label="热压计件单价"
                      name="hotPressPiecePrice"
                      rules={[{ required: true, message: '请输入热压计件单价' }]}
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        placeholder="请输入单价"
                        prefix="¥"
                        suffix="/模"
                        min={0}
                        precision={2}
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      label="产品价格"
                      name="productPrice"
                      rules={[
                        { required: true, message: '请输入产品价格' },
                        { type: 'number', min: 0.01, message: '产品价格必须大于0' }
                      ]}
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        placeholder="请输入产品价格"
                        prefix="¥"
                        min={0}
                        precision={3}
                        step={0.001}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      label="产品重量"
                      name="productWeight"
                      rules={[
                        { required: true, message: '请输入产品重量' },
                        { type: 'number', min: 0.01, message: '产品重量必须大于0' }
                      ]}
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        placeholder="请输入产品重量"
                        suffix="克"
                        min={0}
                        precision={2}
                        step={0.01}
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      label="箱规"
                      name="boxSpecification"
                      rules={[{ required: true, message: '请输入箱规' }]}
                    >
                      <Input
                        placeholder="如：30×20×15 cm"
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      label="装箱数"
                      name="packingQuantity"
                      rules={[
                        { required: true, message: '请输入装箱数' },
                        { type: 'number', min: 1, message: '装箱数必须大于0' }
                      ]}
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        placeholder="请输入装箱数"
                        suffix="个/箱"
                        min={1}
                        precision={0}
                        step={1}
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <Form.Item
                  label="状态"
                  name="status"
                  rules={[{ required: true, message: '请选择状态' }]}
                >
                  <Select placeholder="请选择状态">
                    <Option value="active">启用</Option>
                    <Option value="inactive">停用</Option>
                  </Select>
                </Form.Item>
              </>
            )
          }
        ]}
      />
    </>
  )
}

export default ProductForm
