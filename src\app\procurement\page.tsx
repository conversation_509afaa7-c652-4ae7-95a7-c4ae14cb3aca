'use client'

import React, { useState } from 'react'
import { 
  Card, 
  Table, 
  Button, 
  Space, 
  Tag, 
  Input, 
  Select, 
  DatePicker, 
  Modal,
  Form,
  InputNumber,
  Row,
  Col,
  Statistic
} from 'antd'
import { 
  PlusOutlined, 
  SearchOutlined, 
  EditOutlined, 
  DeleteOutlined,
  EyeOutlined,
  ExportOutlined,
  ShopOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import { PurchaseOrder } from '@/types'
import { styleHelpers } from '@/utils/styles/antdHelpers'

const { RangePicker } = DatePicker
const { Option } = Select

const ProcurementManagement: React.FC = () => {
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [form] = Form.useForm()

  // 模拟数据
  const procurementData: PurchaseOrder[] = [
    {
      id: '1',
      orderNumber: 'PO-2024-001',
      supplierName: '华东原材料供应商',
      supplierContact: '刘经理 - 13600136000',
      orderDate: '2024-01-15',
      expectedDate: '2024-01-25',
      totalAmount: 156000,
      status: 'sent',
      items: []
    },
    {
      id: '2',
      orderNumber: 'PO-2024-002',
      supplierName: '南方设备制造厂',
      supplierContact: '陈总 - 13500135000',
      orderDate: '2024-01-14',
      expectedDate: '2024-01-28',
      totalAmount: 89500,
      status: 'confirmed',
      items: []
    },
    {
      id: '3',
      orderNumber: 'PO-2024-003',
      supplierName: '北京零部件公司',
      supplierContact: '赵主管 - 13400134000',
      orderDate: '2024-01-13',
      expectedDate: '2024-01-20',
      totalAmount: 234500,
      status: 'received',
      items: []
    }
  ]

  const statusMap = {
    draft: { color: 'default', text: '草稿' },
    sent: { color: 'orange', text: '已发送' },
    confirmed: { color: 'blue', text: '已确认' },
    received: { color: 'green', text: '已收货' },
    completed: { color: 'cyan', text: '已完成' }
  }

  const columns: ColumnsType<PurchaseOrder> = [
    {
      title: '采购单号',
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      width: 120,
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierName',
      key: 'supplierName',
      width: 150,
    },
    {
      title: '联系方式',
      dataIndex: 'supplierContact',
      key: 'supplierContact',
      width: 150,
    },
    {
      title: '采购日期',
      dataIndex: 'orderDate',
      key: 'orderDate',
      width: 100,
    },
    {
      title: '预期到货',
      dataIndex: 'expectedDate',
      key: 'expectedDate',
      width: 100,
    },
    {
      title: '采购金额',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      width: 120,
      render: (amount: number) => `¥${amount.toLocaleString()}`
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: keyof typeof statusMap) => {
        const statusInfo = statusMap[status]
        return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>
      }
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Button type="text" icon={<EyeOutlined />} size="small">
            查看
          </Button>
          <Button type="text" icon={<EditOutlined />} size="small">
            编辑
          </Button>
          <Button type="text" danger icon={<DeleteOutlined />} size="small">
            删除
          </Button>
        </Space>
      ),
    },
  ]

  const handleCreateOrder = () => {
    setIsModalVisible(true)
  }

  const handleModalOk = () => {
    form.validateFields().then(values => {
      setIsModalVisible(false)
      form.resetFields()
    })
  }

  const handleModalCancel = () => {
    setIsModalVisible(false)
    form.resetFields()
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: styleHelpers.spacing.lg }}>
      {/* 页面标题 */}
      <div className="page-header">
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <ShopOutlined style={{ fontSize: '24px', color: '#1890ff', marginRight: '12px' }} />
          <div>
            <h1 className="page-title">采购管理</h1>
            <p className="page-description">管理采购订单、供应商信息和采购成本</p>
          </div>
        </div>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="本月采购额"
              value={1256780}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="待收货订单"
              value={12}
              suffix="个"
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="活跃供应商"
              value={45}
              suffix="家"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 操作区域 */}
      <Card>
        <Row gutter={[16, 16]} align="middle" justify="space-between">
          <Col xs={24} lg={18}>
            <Space wrap size="middle">
              <Input
                placeholder="搜索采购单号或供应商"
                prefix={<SearchOutlined />}
                style={{ width: '256px' }}
              />
              <Select placeholder="订单状态" style={{ width: '128px' }}>
                <Option value="">全部</Option>
                <Option value="draft">草稿</Option>
                <Option value="sent">已发送</Option>
                <Option value="confirmed">已确认</Option>
                <Option value="received">已收货</Option>
              </Select>
              <RangePicker />
            </Space>
          </Col>
          <Col xs={24} lg={6}>
            <Space>
              <Button icon={<ExportOutlined />}>导出</Button>
              <Button type="primary" icon={<PlusOutlined />} onClick={handleCreateOrder}>
                新建采购单
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 采购单列表 */}
      <Card title="采购订单列表">
        <Table
          columns={columns}
          dataSource={procurementData}
          rowKey="id"
          pagination={{
            total: procurementData.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            pageSizeOptions: ['10', '20', '50', '100'],
          }}
          scroll={{ x: 1000 }}
        />
      </Card>

      {/* 新建采购单模态框 */}
      <Modal
        title="新建采购订单"
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={800}
        okText="确认"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            orderDate: new Date().toISOString().split('T')[0]
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="供应商名称"
                name="supplierName"
                rules={[{ required: true, message: '请输入供应商名称' }]}
              >
                <Input placeholder="请输入供应商名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="联系方式"
                name="supplierContact"
                rules={[{ required: true, message: '请输入联系方式' }]}
              >
                <Input placeholder="请输入联系方式" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="采购日期"
                name="orderDate"
                rules={[{ required: true, message: '请选择采购日期' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="预期到货日期"
                name="expectedDate"
                rules={[{ required: true, message: '请选择预期到货日期' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            label="采购金额"
            name="totalAmount"
            rules={[{ required: true, message: '请输入采购金额' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="请输入采购金额"
              prefix="¥"
              min={0}
              precision={2}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default ProcurementManagement
