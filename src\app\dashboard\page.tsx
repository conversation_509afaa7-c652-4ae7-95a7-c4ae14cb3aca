'use client'

import React from 'react'
import { Row, Col, Card, Statistic, Table, Tag } from 'antd'
import {
  ArrowUpOutlined,
  ArrowDownOutlined,
  ShoppingCartOutlined,
  InboxOutlined,
  ToolOutlined
} from '@ant-design/icons'
import { styleHelpers } from '@/utils/styles/antdHelpers'

const Dashboard: React.FC = () => {
  // 模拟数据
  const statsData = [
    {
      title: '今日销售额',
      value: 112893,
      precision: 2,
      prefix: '¥',
      suffix: '',
      valueStyle: { color: '#3f8600' },
      prefix_icon: <ArrowUpOutlined />,
      change: '+12.5%'
    },
    {
      title: '待处理订单',
      value: 28,
      suffix: '个',
      valueStyle: { color: '#cf1322' },
      prefix_icon: <ArrowDownOutlined />,
      change: '-5.2%'
    },
    {
      title: '库存总值',
      value: 2456789,
      precision: 2,
      prefix: '¥',
      valueStyle: { color: '#1890ff' },
      prefix_icon: <ArrowUpOutlined />,
      change: '+8.1%'
    },
    {
      title: '生产任务',
      value: 15,
      suffix: '个',
      valueStyle: { color: '#722ed1' },
      prefix_icon: <ArrowUpOutlined />,
      change: '+3.7%'
    }
  ]

  const recentOrders = [
    {
      key: '1',
      orderNo: 'SO-2024-001',
      customer: '北京科技有限公司',
      amount: 25600,
      status: 'pending',
      date: '2024-01-15'
    },
    {
      key: '2',
      orderNo: 'SO-2024-002',
      customer: '上海贸易公司',
      amount: 18900,
      status: 'confirmed',
      date: '2024-01-14'
    },
    {
      key: '3',
      orderNo: 'SO-2024-003',
      customer: '深圳制造企业',
      amount: 45200,
      status: 'shipped',
      date: '2024-01-13'
    }
  ]

  const orderColumns = [
    {
      title: '订单号',
      dataIndex: 'orderNo',
      key: 'orderNo',
    },
    {
      title: '客户',
      dataIndex: 'customer',
      key: 'customer',
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number) => `¥${amount.toLocaleString()}`
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap = {
          pending: { color: 'orange', text: '待处理' },
          confirmed: { color: 'blue', text: '已确认' },
          shipped: { color: 'green', text: '已发货' }
        }
        const statusInfo = statusMap[status as keyof typeof statusMap]
        return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>
      }
    },
    {
      title: '日期',
      dataIndex: 'date',
      key: 'date',
    }
  ]

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: styleHelpers.spacing.lg }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: styleHelpers.spacing.md }}>
        <h1 style={{
          fontSize: '24px',
          fontWeight: 'bold',
          color: styleHelpers.colors.gray[900],
          margin: 0,
          marginBottom: styleHelpers.spacing.xs
        }}>仪表板</h1>
        <p style={{
          color: styleHelpers.colors.gray[600],
          margin: 0,
          fontSize: '14px'
        }}>欢迎使用ERP管理系统，这里是您的业务概览</p>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]}>
        {statsData.map((stat, index) => (
          <Col xs={24} sm={12} lg={6} key={index}>
            <Card>
              <Statistic
                title={stat.title}
                value={stat.value}
                precision={stat.precision}
                valueStyle={stat.valueStyle}
                prefix={stat.prefix}
                suffix={stat.suffix}
              />
              <div style={{
                display: 'flex',
                alignItems: 'center',
                marginTop: styleHelpers.spacing.xs,
                fontSize: '14px'
              }}>
                {stat.prefix_icon}
                <span style={{ marginLeft: styleHelpers.spacing.xs }}>{stat.change}</span>
                <span style={{
                  marginLeft: styleHelpers.spacing.xs,
                  color: styleHelpers.colors.gray[500]
                }}>较昨日</span>
              </div>
            </Card>
          </Col>
        ))}
      </Row>

      {/* 图表和表格区域 */}
      <Row gutter={[16, 16]}>
        {/* 销售趋势 */}
        <Col xs={24} lg={16}>
          <Card title="销售趋势" style={{ height: 384 }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              height: 256,
              color: styleHelpers.colors.gray[400]
            }}>
              <div style={{ textAlign: 'center' }}>
                <ShoppingCartOutlined style={{
                  fontSize: '36px',
                  marginBottom: styleHelpers.spacing.md
                }} />
                <p style={{ margin: 0, marginBottom: styleHelpers.spacing.xs }}>销售趋势图表</p>
                <p style={{
                  fontSize: '14px',
                  margin: 0
                }}>（图表组件将在后续集成）</p>
              </div>
            </div>
          </Card>
        </Col>

        {/* 快速操作 */}
        <Col xs={24} lg={8}>
          <Card title="快速操作" style={{ height: 384 }}>
            <div style={{ display: 'flex', flexDirection: 'column', gap: styleHelpers.spacing.md }}>
              <div style={{
                padding: styleHelpers.spacing.md,
                border: '1px solid #e5e7eb',
                borderRadius: styleHelpers.borderRadius.lg,
                cursor: 'pointer',
                transition: 'background-color 0.2s ease',
                backgroundColor: 'transparent'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = styleHelpers.colors.gray[50]
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent'
              }}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <ShoppingCartOutlined style={{
                    fontSize: '20px',
                    color: '#3b82f6',
                    marginRight: styleHelpers.spacing.sm
                  }} />
                  <div>
                    <div style={{ fontWeight: 500 }}>新建销售订单</div>
                    <div style={{
                      fontSize: '14px',
                      color: styleHelpers.colors.gray[500]
                    }}>创建新的销售订单</div>
                  </div>
                </div>
              </div>

              <div style={{
                padding: styleHelpers.spacing.md,
                border: '1px solid #e5e7eb',
                borderRadius: styleHelpers.borderRadius.lg,
                cursor: 'pointer',
                transition: 'background-color 0.2s ease',
                backgroundColor: 'transparent'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = styleHelpers.colors.gray[50]
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent'
              }}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <InboxOutlined style={{
                    fontSize: '20px',
                    color: '#10b981',
                    marginRight: styleHelpers.spacing.sm
                  }} />
                  <div>
                    <div style={{ fontWeight: 500 }}>库存盘点</div>
                    <div style={{
                      fontSize: '14px',
                      color: styleHelpers.colors.gray[500]
                    }}>进行库存盘点操作</div>
                  </div>
                </div>
              </div>

              <div style={{
                padding: styleHelpers.spacing.md,
                border: '1px solid #e5e7eb',
                borderRadius: styleHelpers.borderRadius.lg,
                cursor: 'pointer',
                transition: 'background-color 0.2s ease',
                backgroundColor: 'transparent'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = styleHelpers.colors.gray[50]
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent'
              }}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <ToolOutlined style={{
                    fontSize: '20px',
                    color: '#8b5cf6',
                    marginRight: styleHelpers.spacing.sm
                  }} />
                  <div>
                    <div style={{ fontWeight: 500 }}>生产计划</div>
                    <div style={{
                      fontSize: '14px',
                      color: styleHelpers.colors.gray[500]
                    }}>制定生产计划</div>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 最近订单 */}
      <Row>
        <Col span={24}>
          <Card title="最近订单">
            <Table 
              columns={orderColumns} 
              dataSource={recentOrders}
              pagination={false}
              size="middle"
            />
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default Dashboard
