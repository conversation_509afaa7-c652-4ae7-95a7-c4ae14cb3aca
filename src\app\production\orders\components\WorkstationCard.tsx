import React from 'react'
import { <PERSON>, <PERSON>, But<PERSON>, Tooltip, Popconfirm } from 'antd'
import { EditOutlined, DeleteOutlined, SettingOutlined } from '@ant-design/icons'
import { Workstation } from '@/types'
import { StatusFormatter } from '@/utils/formatters'
import { sanitizeText } from '@/utils/security/htmlSanitizer'
import { styleHelpers } from '@/utils/styles/antdHelpers'

interface WorkstationCardProps {
  workstation: Workstation
  onEdit: (workstation: Workstation) => void
  onDelete: (id: string) => void
  onStatusToggle: (workstation: Workstation) => void
}

const WorkstationCard: React.FC<WorkstationCardProps> = ({
  workstation,
  onEdit,
  onDelete,
  onStatusToggle
}) => {
  // 🔧 重构：移除重复的状态显示逻辑，使用统一的StatusFormatter

  return (
    <Card
      style={{
        height: '100%',
        transition: styleHelpers.transitions.normal,
        borderColor: workstation.status === 'active' ? '#bbf7d0' : styleHelpers.colors.gray[200],
        ...styleHelpers.createShadow('md')
      }}
      styles={{
        body: { padding: styleHelpers.spacing.md },
        header: { borderBottom: `1px solid ${styleHelpers.colors.gray[200]}` }
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.boxShadow = styleHelpers.shadows.lg
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.boxShadow = styleHelpers.shadows.md
      }}
      actions={[
        <Tooltip title="编辑" key="edit">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => onEdit(workstation)}
          />
        </Tooltip>,
        <Tooltip title={workstation.status === 'active' ? '停用' : '启用'} key="toggle">
          <Button
            type="text"
            icon={StatusFormatter.getStatusIcon(workstation.status, 'workstation')}
            onClick={() => onStatusToggle(workstation)}
          />
        </Tooltip>,
        <Popconfirm
          title="确定要删除这个工位吗？"
          description="删除后将无法恢复，请谨慎操作。"
          onConfirm={() => onDelete(workstation.id)}
          okText="确定"
          cancelText="取消"
          key="delete"
        >
          <Tooltip title="删除">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
            />
          </Tooltip>
        </Popconfirm>
      ]}
    >
      <div style={{ display: 'flex', flexDirection: 'column', gap: styleHelpers.spacing.md }}>
        {/* 工位标题 */}
        <div style={{
          display: 'flex',
          alignItems: 'flex-start',
          justifyContent: 'space-between'
        }}>
          <div style={{ flex: 1 }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: styleHelpers.spacing.sm
            }}>
              <span style={{
                fontFamily: 'monospace',
                fontSize: '18px',
                fontWeight: 'bold',
                color: styleHelpers.colors.primary[600]
              }}>
                {sanitizeText(workstation.code)}
              </span>
              {StatusFormatter.renderWorkstationStatusTag(workstation.status)}
            </div>
            <h3 style={{
              fontSize: '16px',
              fontWeight: '600',
              marginTop: styleHelpers.spacing.xs,
              color: styleHelpers.colors.gray[800],
              margin: `${styleHelpers.spacing.xs}px 0 0 0`
            }}>
              {sanitizeText(workstation.name)}
            </h3>
            {workstation.description && (
              <p style={{
                fontSize: '14px',
                color: styleHelpers.colors.gray[500],
                marginTop: styleHelpers.spacing.xs,
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden',
                margin: `${styleHelpers.spacing.xs}px 0 0 0`
              }}>
                {sanitizeText(workstation.description)}
              </p>
            )}
          </div>
        </div>

        {/* 工位信息 */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: styleHelpers.spacing.sm }}>
          {/* 功能开发中提示 */}
          <div style={{
            background: styleHelpers.colors.primary[50],
            borderRadius: styleHelpers.borderRadius.sm,
            padding: styleHelpers.spacing.sm,
            marginTop: styleHelpers.spacing.sm
          }}>
            <div style={{
              fontSize: '12px',
              color: styleHelpers.colors.primary[600],
              marginBottom: styleHelpers.spacing.xs
            }}>
              <SettingOutlined style={{ marginRight: styleHelpers.spacing.xs }} />
              高级功能
            </div>
            <div style={{
              fontSize: '12px',
              color: styleHelpers.colors.primary[500]
            }}>
              工位状态监控、产能统计等功能正在开发中
            </div>
          </div>
        </div>

        {/* 时间信息 */}
        <div style={{
          fontSize: '12px',
          color: styleHelpers.colors.gray[400],
          borderTop: `1px solid ${styleHelpers.colors.gray[200]}`,
          paddingTop: styleHelpers.spacing.sm
        }}>
          <div>创建: {new Date(workstation.createdAt).toLocaleDateString()}</div>
          <div>更新: {new Date(workstation.updatedAt).toLocaleDateString()}</div>
        </div>
      </div>
    </Card>
  )
}

export default WorkstationCard
