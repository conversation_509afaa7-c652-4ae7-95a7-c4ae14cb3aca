'use client'

import React, { useState, useEffect, useMemo, useCallback } from 'react'
import {
  Card,
  Row,
  Col,
  Button,
  Space,
  Tabs,
  Statistic,
  App,
  Spin,
  Tag,
  Dropdown,
  Badge,
  message
} from 'antd'
import { CalendarOutlined, SettingOutlined, ReloadOutlined, ClockCircleOutlined, Bar<PERSON><PERSON>Outlined, FileTextOutlined, SyncOutlined, DownOutlined, UnorderedListOutlined, ToolOutlined } from '@ant-design/icons'
import ProductionSchedulingBoard from './components/ProductionSchedulingBoard'
import ProductionOrdersList from './components/ProductionOrdersList'
import ProductionOrderDetailModal from './components/ProductionOrderDetailModal'
import WorkstationManagementTab from './components/WorkstationManagementTab'
import WorkTimeManagementTab from './components/WorkTimeManagementTab'

import ProductionWorkOrdersList from './components/ProductionWorkOrdersList'
import SchedulingConfigModal, { SchedulingConfig } from './components/SchedulingConfigModal'
import SchedulingResultModal from './components/SchedulingResultModal'
import MessageQueueDebugger from '@/components/MessageQueueDebugger'
import { dataAccessManager } from '@/services/dataAccess/DataAccessManager'
import { dataChangeNotifier } from '@/services/dataAccess/DataChangeNotifier'
import { ProductionOrder, ProductionWorkOrder, Workstation } from '@/types'
import {
  SameMoldPrioritySchedulingService,
  SchedulingExecutionResult
} from '@/services/scheduling/SameMoldPrioritySchedulingService'
import { handleApiResponse } from '@/utils/dataAccessErrorHandler'
import { ProductionOrderBusinessRules } from '@/utils/business/productionOrderRules'
// ✅ 架构合规：使用DataAccessManager统一监控和合规的Hooks
import { useDebouncedCallback } from '@/hooks/useDebouncedCallback'
import { useDataChangeListener } from '@/hooks/useEventListener'
import { useDataAccessMonitor } from '@/hooks/useDataAccessMonitor'
import { generateWorkOrderFromOrder } from '@/utils/workOrderGenerator'
import { batchOperationController } from '@/utils/concurrencyControl'
import { StatusTransitionValidator } from '@/utils/orderStatusTransitions'
// ✅ 重构：使用新的统一状态管理服务，消除所有重复代码
import { dataAccessCompliantStateManager } from '@/services/stateTransition/DataAccessCompliantStateManager'
// 向后兼容导入（将在后续版本中移除）
import { statusTransitionManager } from '@/utils/business'
import { unifiedStateTransition } from '@/utils/business/unifiedStateTransition'
import DataAccessMonitorCard from '@/components/debug/MemoryMonitorCard'

// 模拟数据类型已删除

const ProductionOrderManagement: React.FC = () => {
  const { message } = App.useApp()

  // ✅ 架构合规：使用DataAccessManager统一监控体系
  const {
    metrics,
    cacheStats,
    isMonitoring,
    lastUpdateTime,
    error,
    clearCache,
    getPerformanceAlerts,
    formatMemorySize,
    formatPercentage,
    isHealthy,
    needsOptimization
  } = useDataAccessMonitor({
    interval: 60000, // 1分钟间隔，符合架构文档
    enabled: true,
    showDetails: process.env.NODE_ENV === 'development'
  })

  const [orderDetailVisible, setOrderDetailVisible] = useState(false)
  const [selectedOrder, setSelectedOrder] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  // 排程相关状态
  const [schedulingConfigVisible, setSchedulingConfigVisible] = useState(false)
  const [schedulingResultVisible, setSchedulingResultVisible] = useState(false)
  const [selectedWorkOrdersForScheduling, setSelectedWorkOrdersForScheduling] = useState<ProductionWorkOrder[]>([])
  const [schedulingResult, setSchedulingResult] = useState<SchedulingExecutionResult | null>(null)
  const [schedulingLoading, setSchedulingLoading] = useState(false)

  const [activeTab, setActiveTab] = useState('pending-orders')
  // ✅ 架构合规：使用DataAccessManager统一监控，移除违规的PerformanceMonitor
  const [dataAccessMonitorVisible, setDataAccessMonitorVisible] = useState(false)

  // URL路由支持
  useEffect(() => {
    // 检查URL参数
    const urlParams = new URLSearchParams(window.location.search)
    const tabParam = urlParams.get('tab')

    // 检查hash路由
    const hash = window.location.hash.replace('#', '')

    // 优先使用URL参数，其次使用hash
    const targetTab = tabParam || hash

    if (targetTab && ['pending-orders', 'production-work-orders', 'scheduling-board', 'workstation-management'].includes(targetTab)) {
      setActiveTab(targetTab)
    }
  }, [])

  // 标签页切换时更新URL
  const handleTabChange = (key: string) => {
    setActiveTab(key)

    // 更新URL参数（不刷新页面）
    const url = new URL(window.location.href)
    url.searchParams.set('tab', key)
    window.history.replaceState({}, '', url.toString())
  }

  // 数据状态管理
  const [productionOrders, setProductionOrders] = useState<ProductionOrder[]>([])
  const [productionWorkOrders, setProductionWorkOrders] = useState<ProductionWorkOrder[]>([])
  const [workstations, setWorkstations] = useState<Workstation[]>([])

  // 分别获取不同状态的订单用于统计
  const allProductionOrders = productionOrders
  const inPlanOrders = productionOrders.filter(order => order.status === 'in_plan')
  const plannedOrders = productionOrders.filter(order => order.status === 'planned')
  const inProgressOrders = productionOrders.filter(order => order.status === 'in_progress')
  const completedOrders = productionOrders.filter(order => order.status === 'completed')

  // 排程相关状态已删除，添加基本统计状态
  const [statistics, setStatistics] = useState({
    totalOrders: 0,
    inPlanOrders: 0,
    plannedOrders: 0,
    inProgressOrders: 0,
    completedOrders: 0
  })

  // 更新统计数据 - 使用useMemo避免无限循环
  const calculatedStatistics = useMemo(() => ({
    totalOrders: allProductionOrders.length,
    inPlanOrders: inPlanOrders.length,
    plannedOrders: plannedOrders.length,
    inProgressOrders: inProgressOrders.length,
    completedOrders: completedOrders.length
  }), [allProductionOrders.length, inPlanOrders.length, plannedOrders.length, inProgressOrders.length, completedOrders.length])

  useEffect(() => {
    setStatistics(calculatedStatistics)
  }, [calculatedStatistics])

  // 数据加载函数 - 使用dataAccessManager和标准错误处理
  const loadProductionOrders = async (forceRefresh: boolean = false) => {
    try {
      // 如果需要强制刷新，先清除缓存
      if (forceRefresh) {
        // 清除生产订单相关缓存
        dataAccessManager.clearAllCache()
      }

      const response = await dataAccessManager.productionOrders.getAll()

      if (response.status === 'success' && response.data && response.data.items) {
        setProductionOrders(response.data.items)
        console.log(`📊 加载生产订单: ${response.data.items.length} 个订单`)
      } else {
        message.error(response.message || '加载生产订单失败')
        console.error('加载生产订单失败:', response)
      }
    } catch (error) {
      message.error('系统错误，请稍后重试')
      console.error('加载生产订单异常:', error)
    }
  }

  const loadProductionWorkOrders = async (forceRefresh: boolean = false) => {
    try {
      // 🔧 修复：彻底清除工单相关缓存，解决缓存导致的数据不一致问题
      if (forceRefresh) {
        console.log('🔧 开始彻底清除工单缓存...')

        // 使用公共方法清除缓存
        dataAccessManager.clearAllCache()
        console.log('✅ 缓存已清除')

        // 2. 清除传统缓存服务中的工单缓存（如果存在）
        // 注意：cacheManager 是私有属性，已通过 clearCache() 方法处理

        // 3. 🔧 移除：生产数据缓存管理器已迁移到UnifiedCacheManager
        // try {
        //   const { productionCacheManager } = require('@/services/dataAccess/DataCacheManager')
        //   productionCacheManager.invalidateWorkOrderCaches()
        //   console.log('✅ 生产数据缓存管理器缓存已清除')
        // } catch (error) {
        //   console.log('⚠️ 生产数据缓存管理器清除失败:', error.message)
        // }

        // 4. 强制等待一小段时间确保缓存清除完成
        await new Promise(resolve => setTimeout(resolve, 100))

        console.log('🔧 所有工单相关缓存已彻底清除')
      }

      console.log('📊 开始获取工单数据...')

      // 🔧 修复：尝试多种方式获取数据，解决缓存不一致问题
      let response
      try {
        // 方式1：通过dataAccessManager获取（带缓存）
        response = await dataAccessManager.productionWorkOrders.getAll()
        console.log('📊 工单数据响应 (方式1-缓存):', {
          status: response.status,
          hasData: !!response.data,
          itemsLength: response.data?.items?.length || 0
        })


      } catch (error) {
        console.error('获取工单数据失败:', error)
        throw error
      }

      if (response.status === 'success' && response.data && response.data.items) {
        setProductionWorkOrders(response.data.items)
        console.log(`📊 加载生产工单: ${response.data.items.length} 个工单`)

        // 🔧 新增：详细记录工单信息用于调试
        if (response.data.items.length > 0) {
          console.log('📋 工单详情:', response.data.items.map(wo => ({
            id: wo.id,
            batchNumber: wo.batchNumber,
            productName: wo.productName,
            status: wo.status
          })))
        }
      } else {
        message.error(response.message || '加载生产工单失败')
        console.error('加载生产工单失败:', response)
      }
    } catch (error) {
      message.error('系统错误，请稍后重试')
      console.error('加载生产工单异常:', error)
    }
  }

  // 新增：工位数据加载函数 - 遵循数据调用规范
  const loadWorkstations = async (shouldResetWorkstations: boolean = false) => {
    try {
      // 🔧 新增：在页面初始化时重置所有工位状态，清除之前的测试数据
      if (shouldResetWorkstations) {
        console.log('🔄 重置所有工位状态到初始空闲状态...')

        // 🔧 修复：使用统一的重置方法替代已注释的批量重置
        const resetResponse = await dataAccessManager.workstations.resetAllWorkstationsToIdle() as any

        if (resetResponse?.status === 'success') {
          console.log(`✅ 工位状态重置成功，成功重置了 ${resetResponse.data?.resetCount || 0} 个工位`)
          if (resetResponse.data?.details?.length > 0) {
            console.log('📋 重置详情:', resetResponse.data.details.map((detail: any) => ({
              工位ID: detail.workstationId,
              状态: '成功',
              消息: `工位 ${detail.workstationCode} 已重置为空闲状态`
            })))
          }
        } else {
          console.warn('⚠️ 重置工位状态失败:', resetResponse?.message)
        }
      }

      const response = await dataAccessManager.workstations.getAll()

      if (response.status === 'success' && response.data && response.data.items) {
        setWorkstations(response.data.items)
        console.log({
          count: response.data.items.length,
          workstations: response.data.items.map(ws => ({ id: ws.id, code: ws.code, name: ws.name }))
        })
      } else {
        message.error(response.message || '加载工位数据失败')
      }
    } catch (error) {
      message.error('系统错误，请稍后重试')
    }
  }

  const refreshData = async (forceRefresh: boolean = true, isInitialLoad: boolean = false) => {
    console.log('🔄 开始刷新数据...', { forceRefresh, isInitialLoad })

    // 🔧 修复：移除状态重置逻辑，保持工单创建后的正确状态
    // 工单创建成功后，订单状态应该保持为 'planned'，不应该被重置

    await Promise.all([
      loadProductionOrders(forceRefresh),
      loadProductionWorkOrders(forceRefresh),
      loadWorkstations(isInitialLoad) // ✅ 新增：加载工位数据，初始加载时重置工位状态
    ])

    console.log('✅ 数据刷新完成')
  }

  // 单独的统计数据更新函数
  const updateStatistics = useCallback(() => {
    const totalOrders = productionOrders.length
    const inPlanCount = productionOrders.filter(order => order.status === 'in_plan').length
    const plannedCount = productionOrders.filter(order => order.status === 'planned').length
    const inProgressCount = productionOrders.filter(order => order.status === 'in_progress').length
    const completedCount = productionOrders.filter(order => order.status === 'completed').length

    setStatistics({
      totalOrders,
      inPlanOrders: inPlanCount,
      plannedOrders: plannedCount,
      inProgressOrders: inProgressCount,
      completedOrders: completedCount
    })
  }, [productionOrders])

  // 当生产订单数据变化时更新统计
  useEffect(() => {
    updateStatistics()
  }, [updateStatistics])

  // 数据一致性验证 - 使用统一的DataConsistencyService
  const validateDataConsistency = useCallback(async () => {
    if (productionOrders.length === 0) return

    try {
      // 使用统一的数据一致性服务
      const { dataConsistencyService } = await import('@/services/dataAccess/DataConsistencyService')
      const checkResult = await dataConsistencyService.performFullConsistencyCheck()

      const totalIssues = checkResult.results.reduce((sum, result) => sum + result.inconsistentItems, 0)
      if (totalIssues > 0) {
        message.warning(`发现 ${totalIssues} 个数据一致性问题，正在重新加载...`)
        await refreshData(true)
      }
    } catch (error) {
      console.error('数据一致性验证失败:', error)
    }
  }, [productionOrders])

  // 定期验证数据一致性
  useEffect(() => {
    if (productionOrders.length > 0) {
      const timer = setTimeout(() => {
        validateDataConsistency()
      }, 2000) // 2秒后验证

      return () => clearTimeout(timer)
    }
  }, [productionOrders, validateDataConsistency])

  // 模拟数据已删除，使用真实数据

  // 🔧 P3内存管理完善：使用新的防抖Hook替代直接使用debounce
  const debouncedRefreshOrders = useDebouncedCallback(
    () => loadProductionOrders(),
    300,
    [loadProductionOrders]
  )

  const debouncedRefreshWorkOrders = useDebouncedCallback(
    () => loadProductionWorkOrders(false),
    300,
    [loadProductionWorkOrders]
  )

  // 🔧 P3内存管理完善：使用新的事件监听器Hook
  useDataChangeListener('production-orders-page', {
    onProductionOrderCreated: (order) => {
      console.log('生产订单页面收到新订单创建事件:', order)
      debouncedRefreshOrders()
    },
    onProductionOrderUpdated: (order) => {
      console.log('生产订单页面收到订单更新事件:', order)
      debouncedRefreshOrders()
    },
    onProductionWorkOrderCreated: (workOrder) => {
      console.log('生产订单页面收到新工单创建事件:', workOrder)
      debouncedRefreshWorkOrders()
    },
    onProductionWorkOrderUpdated: (workOrder) => {
      console.log('生产订单页面收到工单更新事件:', workOrder)
      debouncedRefreshWorkOrders()
    },
    onProductionWorkOrderDeleted: (workOrderId) => {
      console.log('生产订单页面收到工单删除事件:', workOrderId)
      // 刷新工单数据，不强制刷新（避免清除缓存）
      loadProductionWorkOrders(false)
    }
  })

  // 初始化数据
  useEffect(() => {
    const initializeData = async () => {
      setLoading(true)
      try {
        // 🔧 修复：页面初始化时重置工位状态，清除之前的测试数据
        await refreshData(true, true) // forceRefresh=true, isInitialLoad=true

        // PendingPoolSyncService已删除，不再需要同步服务

        message.success('数据初始化完成')
      } catch (error) {
        message.error('数据初始化失败')
      } finally {
        setLoading(false)
      }
    }

    initializeData()
  }, [])

  // 刷新数据
  const handleRefresh = async () => {
    setLoading(true)
    try {
      console.log('🔄 用户手动刷新数据')
      await refreshData(true) // 强制刷新
      message.success('数据刷新完成')
    } catch (error) {
      console.error('数据刷新失败:', error)
      message.error('数据刷新失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  // 批量排产功能已删除

  // 订单详情查看
  const handlePendingOrderDetail = (order: ProductionOrder) => {
    setSelectedOrder(order)
    setOrderDetailVisible(true)
  }

  // 订单状态变更 - 使用dataAccessManager和标准错误处理
  const handleOrderStatusChange = async (orderId: string, newStatus: string) => {
    const updatedOrder = await handleApiResponse(
      () => dataAccessManager.productionOrders.update(orderId, { status: newStatus as any }),
      '更新订单状态'
    )

    if (updatedOrder) {
      message.success('订单状态更新成功')
      await refreshData()
    } else {
      message.error('订单状态更新失败')
    }
  }







  // 同步数据
  const handleSyncData = async () => {
    setLoading(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))
      message.success('数据同步完成')
    } catch (error) {
      message.error('数据同步失败')
    } finally {
      setLoading(false)
    }
  }

  // 生产工单相关处理函数

  const handleWorkOrderStatusChange = async (workOrderId: string, newStatus: string) => {
    // 获取当前工单信息
    const currentWorkOrderResponse = await dataAccessManager.productionWorkOrders.getById(workOrderId)
    if (currentWorkOrderResponse.status !== 'success' || !currentWorkOrderResponse.data) {
      message.error('获取工单信息失败')
      return
    }

    const currentWorkOrder = currentWorkOrderResponse.data
    const oldStatus = currentWorkOrder.status

    // 更新工单状态
    const updatedWorkOrder = await handleApiResponse(
      () => dataAccessManager.productionWorkOrders.update(workOrderId, { status: newStatus as any }),
      '更新工单状态'
    )

    if (updatedWorkOrder && updatedWorkOrder.sourceOrderId) {
      // ✅ 重构：使用统一状态转换接口，消除重复代码
      await unifiedStateTransition.handleStatusChange({
        workOrderId,
        oldStatus: oldStatus as any,
        newStatus: newStatus as any,
        sourceOrderId: updatedWorkOrder.sourceOrderId
      }, {
        showSuccessMessage: true,
        successMessage: '工单状态更新成功'
      })

      await refreshData()
    } else if (updatedWorkOrder) {
      message.success('工单状态更新成功')
      await refreshData()
    } else {
      message.error('工单状态更新失败')
    }
  }

  // 工单编辑功能待实现

  const handleWorkOrderDelete = async (workOrderId: string) => {
    const success = await handleApiResponse(
      () => dataAccessManager.productionWorkOrders.delete(workOrderId),
      '删除工单'
    )

    if (success) {
      message.success('工单删除成功')
      await refreshData()
    } else {
      message.error('工单删除失败')
    }
  }

  // 工单导出功能
  const handleWorkOrderExport = () => {
    message.info('导出工单数据')
  }

  // 开始排单功能
  const handleStartScheduling = (selectedWorkOrderIds: string[]) => {
    if (selectedWorkOrderIds.length === 0) {
      message.warning('请先选择要排程的工单')
      return
    }

    // 筛选出"待开始"状态的工单
    const pendingWorkOrders = productionWorkOrders.filter(wo =>
      selectedWorkOrderIds.includes(wo.id) && wo.status === 'pending'
    )

    if (pendingWorkOrders.length === 0) {
      message.warning('所选工单中没有"待开始"状态的工单')
      return
    }

    if (pendingWorkOrders.length !== selectedWorkOrderIds.length) {
      message.warning(`已过滤掉 ${selectedWorkOrderIds.length - pendingWorkOrders.length} 个非"待开始"状态的工单`)
    }

    setSchedulingConfigVisible(true)
    setSelectedWorkOrdersForScheduling(pendingWorkOrders)
  }

  // 排程配置确认（第一阶段：纯计算）
  const handleSchedulingConfigConfirm = async (config: SchedulingConfig) => {
    try {
      setSchedulingLoading(true)

      // 获取选中工单的ID列表
      const selectedIds = selectedWorkOrdersForScheduling.map(wo => wo.id)


      // 第一阶段：执行排程计算（不修改任何实际状态）
      const result = await SameMoldPrioritySchedulingService.calculateSchedulingResults(selectedIds, {
        sameMoldDeliveryToleranceDays: config.sameMoldDeliveryToleranceDays
      })

      // 保存排程结果用于预览
      setSchedulingResult(result)

      // 关闭配置弹窗，显示结果预览弹窗
      setSchedulingConfigVisible(false)
      setSchedulingResultVisible(true)

      message.success(`排程计算完成！生成 ${result.schedulingResults.length} 个工单的排程预览`)
      message.info('当前为预览模式，工位状态和工单状态尚未实际更新，请确认后点击"确认应用"')

    } catch (error) {
      message.error(`排程计算失败: ${error instanceof Error ? error.message : '未知错误'}`)
    } finally {
      setSchedulingLoading(false)
    }
  }

  // 排程配置取消
  const handleSchedulingConfigCancel = () => {
    setSchedulingConfigVisible(false)
    setSelectedWorkOrdersForScheduling([])
  }

  // 排程结果确认应用（第二阶段：完整应用）
  const handleSchedulingResultConfirm = async () => {
    if (!schedulingResult) return

    try {
      setSchedulingLoading(true)


      // 第二阶段：应用排程结果到数据库（工单状态 + 工位状态）
      await SameMoldPrioritySchedulingService.applySchedulingResults(schedulingResult.schedulingResults)

      // 显示成功消息
      message.success('排程结果已完整应用！工单状态和工位状态已同步更新')
      message.info('所有工单状态已更新为"已排程"，工位队列任务已同步')

      // 关闭结果弹窗并清理状态
      setSchedulingResultVisible(false)
      setSchedulingResult(null)
      setSelectedWorkOrdersForScheduling([])

      // 🔧 修复：刷新页面数据以显示最新状态
      await refreshData(true) // 强制刷新，清除缓存并重新加载数据

    } catch (error) {
      message.error(`应用排程结果失败: ${error instanceof Error ? error.message : '未知错误'}`)
    } finally {
      setSchedulingLoading(false)
    }
  }

  // 排程结果取消（取消预览，不应用任何更改）
  const handleSchedulingResultCancel = () => {
    message.info('已取消排程应用，工位状态和工单状态保持不变')

    setSchedulingResultVisible(false)
    setSchedulingResult(null)
    setSelectedWorkOrdersForScheduling([])
  }

  // 从生产订单生成工单 - 使用dataAccessManager和标准错误处理
  const handleGenerateWorkOrder = async (orderId: string) => {
    try {
      const order = await handleApiResponse(
        () => dataAccessManager.productionOrders.getById(orderId),
        '获取生产订单详情'
      )

      if (!order) {
        message.error('未找到对应的生产订单，请检查订单是否存在')
        return
      }

      // 验证生产订单数据完整性
      if (!order.productCode || !order.productName || !order.plannedQuantity) {
        message.error('生产订单数据不完整，无法创建工单')
        return
      }

      // 生成工单数据
      const workOrderData = await generateWorkOrderFromOrder(order, 130)

      // 创建工单
      const createdWorkOrder = await handleApiResponse(
        () => dataAccessManager.productionWorkOrders.create(workOrderData),
        '创建生产工单'
      )

      if (createdWorkOrder) {
        // ✅ 重构：使用统一状态转换接口处理工单创建
        await unifiedStateTransition.handleCreation(
          order.id,
          order.status as any,
          {
            showSuccessMessage: false, // 避免重复消息
            showErrorMessage: true
          }
        )

        message.success(`成功生成工单: ${createdWorkOrder.batchNumber}`)
        await refreshData()
      } else {
        message.error('生成工单失败，请检查数据格式或联系系统管理员')
      }
    } catch (error) {
      console.error('创建工单过程中发生错误:', error)
      message.error(`创建工单失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  // 批量创建工单 - 使用并发控制优化
  const handleCreateWorkOrders = async (orderIds: string[], hourlyCapacity: number) => {
    try {
      message.loading('正在批量创建工单...', 0)

      // 预先验证所有订单是否存在
      message.info('正在验证订单数据...')
      const validationErrors: string[] = []
      const validOrders: ProductionOrder[] = []

      for (const orderId of orderIds) {
        try {
          const order = await handleApiResponse(
            () => dataAccessManager.productionOrders.getById(orderId),
            `验证生产订单 (${orderId})`
          )

          if (!order) {
            validationErrors.push(`订单ID ${orderId} 不存在`)
            continue
          }

          // ✅ 使用统一的业务规则验证订单状态
          if (!ProductionOrderBusinessRules.canCreateWorkOrder(order)) {
            const statusDisplayName = order.status === 'planned' ? '已计划' :
                                    order.status === 'in_progress' ? '生产中' :
                                    order.status === 'completed' ? '已完成' :
                                    order.status === 'cancelled' ? '已取消' : order.status
            validationErrors.push(`订单 ${order.orderNumber} 状态为"${statusDisplayName}"，只能为"计划中"状态的订单创建工单`)
            continue
          }

          // 验证生产订单数据完整性
          if (!order.productCode || !order.productName || !order.plannedQuantity) {
            validationErrors.push(`订单 ${order.orderNumber} 数据不完整，缺少必要字段`)
            continue
          }

          // 检查是否已存在工单
          const existingWorkOrdersResponse = await dataAccessManager.productionWorkOrders.getAll()
          if (existingWorkOrdersResponse.status === 'success' && existingWorkOrdersResponse.data) {
            const existingWorkOrder = existingWorkOrdersResponse.data.items.find(
              wo => wo.sourceOrderId === orderId
            )
            if (existingWorkOrder) {
              validationErrors.push(`订单 ${order.orderNumber} 已存在工单 ${existingWorkOrder.batchNumber}`)
              continue
            }
          }

          validOrders.push(order)
        } catch (error) {
          validationErrors.push(`验证订单 ${orderId} 时发生错误: ${error instanceof Error ? error.message : '未知错误'}`)
        }
      }

      // 如果有验证错误，显示详细信息并终止操作
      if (validationErrors.length > 0) {
        message.destroy()
        const errorMessage = `数据验证失败，无法创建工单：\n${validationErrors.join('\n')}`
        message.error(errorMessage)
        console.error('工单创建验证失败:', validationErrors)
        return
      }

      if (validOrders.length === 0) {
        message.destroy()
        message.warning('没有有效的订单可以创建工单')
        return
      }

      message.info(`验证通过，开始创建 ${validOrders.length} 个工单...`)

      // 创建任务数组
      const tasks = validOrders.map(order => async () => {
        // 生成工单数据
        const workOrderData = await generateWorkOrderFromOrder(order, hourlyCapacity)

        // 创建工单
        const createdWorkOrder = await handleApiResponse(
          () => dataAccessManager.productionWorkOrders.create(workOrderData),
          `创建生产工单 (${order.orderNumber})`
        )

        if (!createdWorkOrder) {
          throw new Error(`创建工单失败: ${order.orderNumber}，请检查数据格式`)
        }

        // ✅ 重构：使用统一状态转换接口处理批量工单创建
        const creationResult = await unifiedStateTransition.handleCreation(
          order.id,
          order.status as any,
          {
            silent: true // 批量操作使用静默模式
          }
        )

        return {
          workOrder: createdWorkOrder,
          order: {
            ...order,
            status: creationResult.orderStatusChanged
              ? creationResult.newOrderStatus
              : order.status
          }, // 返回实际转换后的状态
          orderNumber: order.orderNumber
        }
      })

      // 生成任务名称
      const taskNames = validOrders.map(order => `创建工单-${order.orderNumber}`)

      // 使用并发控制器执行批量任务
      const batchResult = await batchOperationController.executeBatch(tasks, taskNames)

      // 关闭loading消息
      message.destroy()

      // 处理结果
      const { successful, failed, successRate, totalDuration } = batchResult

      if (successful.length > 0) {
        message.success(
          `成功创建 ${successful.length} 个工单${failed.length > 0 ? `，${failed.length} 个失败` : ''} (耗时: ${totalDuration}ms)`
        )

        // 刷新数据
        await refreshData()
      }

      if (failed.length > 0) {
        const errorDetails = failed.map((f: any) => `• ${f.taskName || f.name || '未知任务'}: ${f.error}`).join('\n')
        const errorMessage = `${failed.length} 个工单创建失败：\n${errorDetails}`

        // 显示详细的错误信息
        message.error({
          content: errorMessage,
          duration: 8, // 延长显示时间以便用户阅读
        })
        console.error('批量创建工单失败详情:', failed)
      }

      if (successful.length === 0) {
        const errorMsg = '没有成功创建任何工单，请检查订单数据或联系系统管理员'
        message.error(errorMsg)
        throw new Error(errorMsg)
      }

      // 记录统计信息但不返回
      console.log('批量创建工单完成:', {
        success: successful.length,
        failed: failed.length,
        successRate,
        totalDuration
      })

    } catch (error) {
      message.destroy()
      const errorMessage = error instanceof Error ? error.message : '批量创建工单失败'
      message.error(errorMessage)
      console.error('批量创建工单错误:', error)
      throw error
    }
  }

  // 排产看板数据（简化版本，仅用于统计显示）
  const assignedTasks = productionWorkOrders.filter(workOrder =>
    workOrder.status === 'scheduled' ||  // 已排程状态
    workOrder.status === 'in_progress' ||
    workOrder.status === 'completed'
  )
  const unassignedTasks = productionWorkOrders.filter(workOrder =>
    workOrder.status === 'pending'
  )

  // 操作菜单
  const operationMenu = {
    items: [
      {
        key: 'sync',
        icon: <SyncOutlined />,
        label: '同步数据',
        onClick: handleSyncData
      },
      {
        key: 'config',
        icon: <SettingOutlined />,
        label: '排单配置',
        onClick: () => message.info('排单配置功能开发中')
      }
    ]
  }

  return (
    <div style={{ padding: 0 }}>
      <Spin spinning={loading}>
        {/* 页面标题和操作栏 */}
        <Card size="small" style={{ marginBottom: '16px' }}>
          <Row justify="space-between" align="middle">
            <Col>
              <Space size="large">
                <div>
                  <h2 style={{ margin: 0 }}>
                    <UnorderedListOutlined style={{ marginRight: '8px' }} />
                    生产订单管理
                  </h2>
                  <div style={{ marginTop: '4px' }}>
                    <Tag color="blue">当前策略: 默认智能拆单配置</Tag>
                    <Tag color="orange" style={{ marginLeft: '8px' }}>
                      生产订单只能通过MRP流程创建
                    </Tag>
                  </div>
                </div>
              </Space>
            </Col>
            <Col>
              <Space>
                <MessageQueueDebugger />
                <Button
                  icon={<ReloadOutlined />}
                  onClick={handleRefresh}
                >
                  刷新
                </Button>
                <Dropdown menu={operationMenu} trigger={['click']}>
                  <Button>
                    更多操作 <DownOutlined />
                  </Button>
                </Dropdown>
              </Space>
            </Col>
          </Row>
        </Card>

        {/* 统计卡片 */}
        <Row gutter={16} style={{ marginBottom: '16px' }}>
          <Col xs={24} sm={12} md={6}>
            <Card size="small">
              <Statistic
                title="生产订单"
                value={statistics?.totalOrders || 0}
                prefix={<FileTextOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>

          <Col xs={24} sm={12} md={6}>
            <Card size="small">
              <Statistic
                title="待分配任务"
                value={unassignedTasks.length}
                prefix={<ClockCircleOutlined />}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card size="small">
              <Statistic
                title="工位利用率"
                value={75}
                suffix="%"
                prefix={<BarChartOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 🔧 P3内存管理完善：添加内存监控卡片 */}
        <Row gutter={16} style={{ marginBottom: '16px' }}>
          <Col xs={24} sm={12} md={8}>
            <DataAccessMonitorCard
              metrics={metrics ? {
                averageResponseTime: metrics.averageResponseTime,
                totalRequests: metrics.totalCalls || 0,
                successfulRequests: metrics.successCalls || 0,
                errorRate: metrics.totalCalls > 0 ? (metrics.errorCalls / metrics.totalCalls) : 0
              } : null}
              cacheStats={cacheStats}
              isMonitoring={isMonitoring}
              lastUpdateTime={lastUpdateTime}
              error={error}
              onClearCache={clearCache}
              formatMemorySize={formatMemorySize}
              formatPercentage={formatPercentage}
            />
          </Col>
        </Row>

        {/* 主要内容区域 */}
        <Card
          extra={
            <Space>
              <Button
                icon={<BarChartOutlined />}
                onClick={() => setDataAccessMonitorVisible(true)}
                type="text"
                size="small"
              >
                DataAccessManager监控
              </Button>
            </Space>
          }
        >
          <Tabs
            defaultActiveKey="pending-orders"
            size="large"
            onChange={handleTabChange}
            activeKey={activeTab}
            items={[
              {
                key: 'pending-orders',
                label: (
                  <span>
                    <FileTextOutlined />
                    生产订单
                    {(statistics?.totalOrders || 0) > 0 && (
                      <Badge
                        count={statistics?.totalOrders || 0}
                        style={{ marginLeft: '8px' }}
                        showZero
                      />
                    )}
                  </span>
                ),
                children: (
                  <ProductionOrdersList
                    orders={allProductionOrders}
                    loading={loading}
                    onRefresh={handleRefresh}
                    onOrderDetail={handlePendingOrderDetail}
                    onStatusChange={handleOrderStatusChange}
                    onCreateWorkOrders={handleCreateWorkOrders}
                    onTabChange={handleTabChange}
                    workstations={workstations}
                    currentSplitConfig={null}
                  />
                )
              },

              {
                key: 'production-work-orders',
                label: (
                  <span>
                    <ToolOutlined />
                    生产工单
                    {productionWorkOrders.length > 0 && (
                      <Badge
                        count={productionWorkOrders.length}
                        style={{ marginLeft: '8px' }}
                        showZero
                      />
                    )}
                  </span>
                ),
                children: (
                  <ProductionWorkOrdersList
                    workOrders={productionWorkOrders}
                    loading={loading}
                    onRefresh={handleRefresh}
                    onStatusChange={handleWorkOrderStatusChange}
                    onDelete={handleWorkOrderDelete}
                    onExport={handleWorkOrderExport}
                    onStartScheduling={handleStartScheduling}
                  />
                )
              },
              {
                key: 'scheduling-board',
                label: (
                  <span>
                    <CalendarOutlined />
                    排产看板
                    {assignedTasks.length > 0 && (
                      <Badge
                        count={assignedTasks.length}
                        style={{ marginLeft: '8px' }}
                        showZero
                      />
                    )}
                  </span>
                ),
                children: (
                  <ProductionSchedulingBoard
                    tasks={assignedTasks}
                    workstations={workstations}
                    onTaskClick={(task: any) => {
                      message.info(`查看任务详情: ${task.id || '未知任务'}`)
                    }}
                    loading={loading}
                  />
                )
              },
              {
                key: 'workstation-management',
                label: (
                  <span>
                    <SettingOutlined />
                    工位管理
                    {workstations.length > 0 && (
                      <Badge
                        count={workstations.length}
                        style={{ marginLeft: '8px' }}
                        showZero
                      />
                    )}
                  </span>
                ),
                children: (
                  <WorkstationManagementTab
                    loading={loading}
                  />
                )
              },
              {
                key: 'work-time-management',
                label: (
                  <span>
                    <ClockCircleOutlined />
                    工作时间
                  </span>
                ),
                children: (
                  <WorkTimeManagementTab
                    loading={loading}
                  />
                )
              }
            ]}
          />
        </Card>

        {/* 订单详情弹窗 */}
        <ProductionOrderDetailModal
          open={orderDetailVisible}
          order={selectedOrder}
          onClose={() => {
            setOrderDetailVisible(false)
            setSelectedOrder(null)
          }}
          onStatusChange={handleOrderStatusChange}
        />

        {/* 排程配置弹窗 */}
        <SchedulingConfigModal
          open={schedulingConfigVisible}
          onCancel={handleSchedulingConfigCancel}
          onConfirm={handleSchedulingConfigConfirm}
          selectedWorkOrders={selectedWorkOrdersForScheduling}
          loading={schedulingLoading}
        />

        {/* 排程结果弹窗 */}
        <SchedulingResultModal
          open={schedulingResultVisible}
          onCancel={handleSchedulingResultCancel}
          onConfirm={handleSchedulingResultConfirm}
          result={schedulingResult}
          loading={schedulingLoading}
        />

        {/* ✅ DataAccessManager监控组件 - 架构合规版 */}
        {dataAccessMonitorVisible && (
          <DataAccessMonitorCard
            metrics={metrics ? {
              averageResponseTime: metrics.averageResponseTime,
              totalRequests: metrics.totalCalls || 0,
              successfulRequests: metrics.successCalls || 0,
              errorRate: metrics.totalCalls > 0 ? (metrics.errorCalls / metrics.totalCalls) : 0
            } : null}
            cacheStats={cacheStats}
            isMonitoring={isMonitoring}
            lastUpdateTime={lastUpdateTime}
            error={error}
            onClearCache={clearCache}
            formatMemorySize={formatMemorySize}
            formatPercentage={formatPercentage}
          />
        )}
      </Spin>
    </div>
  )
}

// 用App组件包裹以提供message等上下文
export default function ProductionOrderManagementPage() {
  return (
    <App>
      <ProductionOrderManagement />
    </App>
  )
}
