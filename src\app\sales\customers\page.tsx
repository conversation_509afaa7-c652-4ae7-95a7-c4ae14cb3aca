'use client'

import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Input,
  Select,
  Modal,
  Form,
  Row,
  Col,
  Statistic,
  Tabs,
  Descriptions,
  Badge,
  Popconfirm,
  Progress,
  App
} from 'antd'
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  ExportOutlined,
  UserOutlined,
  PhoneOutlined,
  MailOutlined,
  TrophyOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import { Customer, CustomerLevel, CustomerCategory } from '@/types'
import { styleHelpers } from '@/utils/styles/antdHelpers'
// ✅ 架构合规：移除违规的useCustomerStore，使用DataAccessManager统一数据访问
import { dataAccessManager } from '@/services/dataAccess/DataAccessManager'
import { handleApiResponse } from '@/utils/dataAccessErrorHandler'

const { Option } = Select
const { TextArea } = Input

function CustomerManagement() {
  const { message } = App.useApp()

  // ✅ 架构合规：使用本地状态管理替代Store
  const [customers, setCustomers] = useState<Customer[]>([])
  const [loading, setLoading] = useState(false)
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null)

  const [isModalVisible, setIsModalVisible] = useState(false)
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false)
  const [editingCustomer, setEditingCustomer] = useState<Customer | null>(null)
  const [activeTabKey, setActiveTabKey] = useState('basic')
  const [form] = Form.useForm()
  const [searchText, setSearchText] = useState('')
  const [filterLevel, setFilterLevel] = useState<CustomerLevel | ''>('')
  const [filterCategory, setFilterCategory] = useState<CustomerCategory | ''>('')

  // 生成下一个客户编码（KH格式）
  const generateNextCustomerCode = (): string => {
    const existingCodes = customers.map(customer => customer.customerCode)
    const khCodes = existingCodes.filter(code => /^KH\d{4,5}$/.test(code))

    if (khCodes.length === 0) {
      return 'KH0001'
    }

    const maxNumber = Math.max(...khCodes.map(code => {
      const match = code.match(/^KH(\d{4,5})$/)
      return match ? parseInt(match[1], 10) : 0
    }))

    const nextNumber = maxNumber + 1

    // 动态位数扩展：4位数(0001-9999) → 5位数(10000-99999)
    if (nextNumber > 99999) {
      message.warning('编码已达到最大值KH99999，请手动输入编码')
      return ''
    }

    // 4位数范围：KH0001 到 KH9999
    if (nextNumber <= 9999) {
      return `KH${nextNumber.toString().padStart(4, '0')}`
    }

    // 5位数范围：KH10000 到 KH99999
    return `KH${nextNumber.toString()}`
  }

  // 检查客户编码唯一性
  const checkCustomerCodeUniqueness = (code: string, excludeId?: string): boolean => {
    return !customers.some(customer =>
      customer.customerCode === code && customer.id !== excludeId
    )
  }

  // 数据初始化
  useEffect(() => {
    // 数据已通过Store和持久化机制自动加载
    console.log('客户数据已加载', {
      customers: customers.length
    })
  }, [customers.length])

  // 获取客户等级颜色
  const getLevelColor = (level: CustomerLevel): string => {
    switch (level) {
      case 'A': return 'gold'
      case 'B': return 'blue'
      case 'C': return 'green'
      default: return 'default'
    }
  }

  // 获取客户分类颜色
  const getCategoryColor = (category: CustomerCategory): string => {
    switch (category) {
      case 'important': return 'red'
      case 'general': return 'orange'
      case 'small': return 'cyan'
      default: return 'default'
    }
  }

  // 获取客户分类名称
  const getCategoryName = (category: CustomerCategory): string => {
    switch (category) {
      case 'important': return '重要客户'
      case 'general': return '一般客户'
      case 'small': return '小客户'
      default: return '未分类'
    }
  }

  // 计算信用使用率
  const getCreditUsageRate = (used: number, limit: number): number => {
    return limit > 0 ? (used / limit) * 100 : 0
  }

  // 表格列定义
  const columns: ColumnsType<Customer> = [
    {
      title: '客户编码',
      dataIndex: 'customerCode',
      key: 'customerCode',
      width: 120,
      fixed: 'left'
    },
    {
      title: '客户名称',
      dataIndex: 'customerName',
      key: 'customerName',
      width: 200,
      fixed: 'left'
    },
    {
      title: '客户等级',
      dataIndex: 'customerLevel',
      key: 'customerLevel',
      width: 100,
      render: (level: CustomerLevel) => (
        <Tag color={getLevelColor(level)} icon={<TrophyOutlined />}>
          {level}类
        </Tag>
      )
    },
    {
      title: '客户分类',
      dataIndex: 'customerCategory',
      key: 'customerCategory',
      width: 120,
      render: (category: CustomerCategory) => (
        <Tag color={getCategoryColor(category)}>
          {getCategoryName(category)}
        </Tag>
      )
    },
    {
      title: '联系人',
      dataIndex: 'contactPerson',
      key: 'contactPerson',
      width: 100
    },
    {
      title: '联系电话',
      dataIndex: 'contactPhone',
      key: 'contactPhone',
      width: 130
    },
    {
      title: '年销售额',
      dataIndex: 'annualSalesAmount',
      key: 'annualSalesAmount',
      width: 120,
      render: (amount: number) => `¥${(amount || 0).toLocaleString()}`
    },
    {
      title: '信用额度',
      key: 'creditInfo',
      width: 150,
      render: (_, record) => {
        const creditLimit = record.creditLimit || 0
        const usedCredit = record.usedCredit || 0
        const usageRate = getCreditUsageRate(usedCredit, creditLimit)
        return (
          <div>
            <div>¥{creditLimit.toLocaleString()}</div>
            <Progress
              percent={usageRate}
              size="small"
              status={usageRate > 80 ? 'exception' : 'normal'}
              showInfo={false}
            />
            <div style={{ fontSize: '12px', color: '#666' }}>
              已用: ¥{usedCredit.toLocaleString()}
            </div>
          </div>
        )
      }
    },
    {
      title: '所属业务员',
      dataIndex: 'salesRepresentative',
      key: 'salesRepresentative',
      width: 120
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => (
        <Badge 
          status={status === 'active' ? 'success' : 'default'} 
          text={status === 'active' ? '正常' : '停用'} 
        />
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button 
            type="link" 
            icon={<EyeOutlined />} 
            onClick={() => handleViewDetail(record)}
          >
            详情
          </Button>
          <Button 
            type="link" 
            icon={<EditOutlined />} 
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个客户吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ]

  // 过滤后的客户数据
  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = !searchText || 
      customer.customerName.toLowerCase().includes(searchText.toLowerCase()) ||
      customer.customerCode.toLowerCase().includes(searchText.toLowerCase()) ||
      (customer.contactPerson || '').toLowerCase().includes(searchText.toLowerCase())
    
    const matchesLevel = !filterLevel || customer.customerLevel === filterLevel
    const matchesCategory = !filterCategory || customer.customerCategory === filterCategory
    
    return matchesSearch && matchesLevel && matchesCategory
  })

  // ✅ 架构合规：使用DataAccessManager加载客户数据
  const refreshCustomers = async () => {
    try {
      setLoading(true)
      const result = await handleApiResponse(
        () => dataAccessManager.customers.getAll(),
        '获取客户数据'
      )
      
      if (result && result.items) {
        setCustomers(result.items)
      }
    } catch (error) {
      console.error('刷新客户数据失败:', error)
      message.error('刷新客户数据失败')
    } finally {
      setLoading(false)
    }
  }

  // 初始化数据
  useEffect(() => {
    refreshCustomers()
  }, [])

  // 统计数据
  const stats = {
    total: customers.length,
    aLevel: customers.filter(c => c.customerLevel === 'A').length,
    bLevel: customers.filter(c => c.customerLevel === 'B').length,
    cLevel: customers.filter(c => c.customerLevel === 'C').length,
    totalSales: customers.reduce((sum, c) => sum + (c.annualSalesAmount || 0), 0),
    totalCredit: customers.reduce((sum, c) => sum + (c.creditLimit || 0), 0),
    usedCredit: customers.reduce((sum, c) => sum + (c.usedCredit || 0), 0)
  }

  const handleCreate = () => {
    setEditingCustomer(null)
    setIsModalVisible(true)
    setActiveTabKey('basic')
    form.resetFields()

    // 自动生成客户编码
    const nextCode = generateNextCustomerCode()
    if (nextCode) {
      form.setFieldsValue({ customerCode: nextCode })
    }
  }

  const handleEdit = (customer: Customer) => {
    setEditingCustomer(customer)
    setIsModalVisible(true)
    setActiveTabKey('basic')
    form.setFieldsValue(customer)
  }

  const handleViewDetail = (customer: Customer) => {
    setSelectedCustomer(customer)
    setIsDetailModalVisible(true)
  }

  // ✅ 架构合规：删除客户
  const handleDelete = async (id: string) => {
    try {
      setLoading(true)
      const result = await handleApiResponse(
        () => dataAccessManager.customers.delete(id),
        '删除客户'
      )

      if (result) {
        await refreshCustomers()
        message.success('客户删除成功')
      }
    } catch (error) {
      console.error('删除客户失败:', error)
      message.error('删除客户失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields()
      setLoading(true)

      if (editingCustomer) {
        // ✅ 架构合规：更新客户
        const result = await handleApiResponse(
          () => dataAccessManager.customers.update(editingCustomer.id, values),
          '更新客户信息'
        )

        if (result) {
          await refreshCustomers()
          message.success('客户信息更新成功')
        } else {
          return
        }
      } else {
        // ✅ 架构合规：创建新客户
        const newCustomerData = {
          ...values,
          // 确保数字字段有默认值
          creditLimit: values.creditLimit || 0,
          usedCredit: values.usedCredit || 0,
          annualSalesAmount: values.annualSalesAmount || 0,
          discountRate: values.discountRate || 0
        }

        const result = await handleApiResponse(
          () => dataAccessManager.customers.create(newCustomerData),
          '创建客户'
        )

        if (result) {
          await refreshCustomers()
          message.success('客户创建成功')
        } else {
          return
        }
      }

      setIsModalVisible(false)
      setActiveTabKey('basic')
      form.resetFields()
    } catch (error) {
      message.error('操作失败，请稍后重试')
      console.error('客户操作失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleModalCancel = () => {
    setIsModalVisible(false)
    setActiveTabKey('basic')
    form.resetFields()
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: styleHelpers.spacing.lg }}>
      {/* 页面标题 */}
      <div className="page-header">
        <h1 className="page-title">客户管理</h1>
        <p className="page-description">管理客户档案、信用额度和客户关系</p>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="客户总数"
              value={stats.total}
              suffix="个"
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="年销售总额"
              value={stats.totalSales}
              precision={0}
              prefix="¥"
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="信用总额度"
              value={stats.totalCredit}
              precision={0}
              prefix="¥"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="信用使用率"
              value={stats.totalCredit > 0 ? (stats.usedCredit / stats.totalCredit) * 100 : 0}
              precision={1}
              suffix="%"
              valueStyle={{ color: stats.totalCredit > 0 && (stats.usedCredit / stats.totalCredit) > 0.8 ? '#cf1322' : '#1890ff' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 客户等级分布 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="A级客户"
              value={stats.aLevel}
              suffix="个"
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="B级客户"
              value={stats.bLevel}
              suffix="个"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="C级客户"
              value={stats.cLevel}
              suffix="个"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 操作区域 */}
      <Card>
        <Row gutter={[16, 16]} align="middle" justify="space-between">
          <Col xs={24} lg={18}>
            <Space wrap size="middle">
            <Input
              placeholder="搜索客户名称、编码或联系人"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: '256px' }}
            />
            <Select
              placeholder="客户等级"
              value={filterLevel}
              onChange={setFilterLevel}
              style={{ width: '128px' }}
              allowClear
            >
              <Option value="A">A级</Option>
              <Option value="B">B级</Option>
              <Option value="C">C级</Option>
            </Select>
            <Select
              placeholder="客户分类"
              value={filterCategory}
              onChange={setFilterCategory}
              style={{ width: '128px' }}
              allowClear
            >
              <Option value="important">重要客户</Option>
              <Option value="general">一般客户</Option>
              <Option value="small">小客户</Option>
            </Select>
            </Space>
          </Col>
          <Col xs={24} lg={6}>
            <Space>
              <Button icon={<ExportOutlined />}>导出</Button>
              <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
                新建客户
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 客户列表 */}
      <Card title="客户列表">
        <Table
          columns={columns}
          dataSource={filteredCustomers}
          rowKey="id"
          loading={loading}
          pagination={{
            total: filteredCustomers.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          scroll={{ x: 1400 }}
        />
      </Card>

      {/* 新建/编辑客户模态框 */}
      <Modal
        title={editingCustomer ? '编辑客户' : '新建客户'}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={900}
        okText="确认"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            status: 'active'
          }}
        >
          <Tabs
            activeKey={activeTabKey}
            onChange={setActiveTabKey}
            items={[
              {
                key: 'basic',
                label: '基础信息',
                children: (
                  <div style={{ display: 'flex', flexDirection: 'column', gap: styleHelpers.spacing.md }}>
                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          name="customerCode"
                          label="客户编码"
                          rules={[
                            { required: true, message: '请输入客户编码' },
                            { pattern: /^KH\d{4,5}$/, message: '格式：KHXXXX或KHXXXXX（如：KH0001）' },
                            {
                              validator: (_, value) => {
                                if (!value) return Promise.resolve()
                                if (!checkCustomerCodeUniqueness(value, editingCustomer?.id)) {
                                  return Promise.reject(new Error('客户编码已存在，请使用其他编码'))
                                }
                                return Promise.resolve()
                              }
                            }
                          ]}
                        >
                          <Input placeholder="如：KH0001（自动生成）" />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          name="customerName"
                          label="客户名称"
                          rules={[{ required: true, message: '请输入客户名称' }]}
                        >
                          <Input placeholder="请输入客户名称" />
                        </Form.Item>
                      </Col>
                    </Row>

                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          name="contactPerson"
                          label="联系人"
                        >
                          <Input placeholder="请输入联系人" />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          name="contactPhone"
                          label="联系电话"
                          rules={[
                            {
                              pattern: /^1[3-9]\d{9}$/,
                              message: '请输入正确的手机号格式'
                            }
                          ]}
                        >
                          <Input placeholder="请输入联系电话" />
                        </Form.Item>
                      </Col>
                    </Row>

                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          name="referrer"
                          label="介绍人"
                        >
                          <Input placeholder="请输入介绍人" />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          name="salesRepresentative"
                          label="所属业务员"
                          rules={[{ required: true, message: '请输入所属业务员' }]}
                        >
                          <Input placeholder="请输入所属业务员" />
                        </Form.Item>
                      </Col>
                    </Row>
                  </div>
                )
              },
              {
                key: 'financial',
                label: '财务信息',
                children: (
                  <div style={{ display: 'flex', flexDirection: 'column', gap: styleHelpers.spacing.md }}>
                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          name="bankName"
                          label="开户行"
                        >
                          <Input placeholder="请输入开户行" />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          name="bankAccount"
                          label="开户账号"
                        >
                          <Input placeholder="请输入开户账号" />
                        </Form.Item>
                      </Col>
                    </Row>

                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          name="taxNumber"
                          label="单位税号"
                        >
                          <Input placeholder="请输入单位税号" />
                        </Form.Item>
                      </Col>
                    </Row>

                    <div style={{ color: '#9ca3af', textAlign: 'center', padding: '32px 0' }}>
                      <p>更多财务信息功能开发中...</p>
                    </div>
                  </div>
                )
              }
            ]}
          />
        </Form>
      </Modal>

      {/* 客户详情模态框 */}
      <Modal
        title="客户详情"
        open={isDetailModalVisible}
        onCancel={() => setIsDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setIsDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {selectedCustomer && selectedCustomer.customerCode && selectedCustomer.customerName && (
          <Tabs
            defaultActiveKey="basic"
            items={[
              {
                key: 'basic',
                label: '基本信息',
                children: (
                  <Descriptions column={2} bordered>
                    <Descriptions.Item label="客户编码">{selectedCustomer.customerCode}</Descriptions.Item>
                    <Descriptions.Item label="客户名称">{selectedCustomer.customerName}</Descriptions.Item>
                    <Descriptions.Item label="客户等级">
                      <Tag color={getLevelColor(selectedCustomer.customerLevel || 'C')}>
                        {selectedCustomer.customerLevel}类
                      </Tag>
                    </Descriptions.Item>
                    <Descriptions.Item label="客户分类">
                      <Tag color={getCategoryColor(selectedCustomer.customerCategory || 'general')}>
                        {getCategoryName(selectedCustomer.customerCategory || 'general')}
                      </Tag>
                    </Descriptions.Item>
                    <Descriptions.Item label="联系人">
                      <Space>
                        <UserOutlined />
                        {selectedCustomer.contactPerson}
                      </Space>
                    </Descriptions.Item>
                    <Descriptions.Item label="联系电话">
                      <Space>
                        <PhoneOutlined />
                        {selectedCustomer.contactPhone}
                      </Space>
                    </Descriptions.Item>
                    <Descriptions.Item label="联系邮箱">
                      <Space>
                        <MailOutlined />
                        {selectedCustomer.contactEmail || '未填写'}
                      </Space>
                    </Descriptions.Item>
                    <Descriptions.Item label="地址">{selectedCustomer.address}</Descriptions.Item>
                    <Descriptions.Item label="税号">{selectedCustomer.taxNumber || '未填写'}</Descriptions.Item>
                    <Descriptions.Item label="付款条件">{selectedCustomer.paymentTerms}</Descriptions.Item>
                    <Descriptions.Item label="年销售额">
                      ¥{(selectedCustomer.annualSalesAmount || 0).toLocaleString()}
                    </Descriptions.Item>
                    <Descriptions.Item label="专属折扣率">
                      {((selectedCustomer.discountRate || 0) * 100).toFixed(1)}%
                    </Descriptions.Item>
                    <Descriptions.Item label="所属业务员">{selectedCustomer.salesRepresentative}</Descriptions.Item>
                    <Descriptions.Item label="状态">
                      <Badge
                        status={selectedCustomer.status === 'active' ? 'success' : 'default'}
                        text={selectedCustomer.status === 'active' ? '正常' : '停用'}
                      />
                    </Descriptions.Item>
                    <Descriptions.Item label="创建时间" span={2}>{selectedCustomer.createdAt}</Descriptions.Item>
                    <Descriptions.Item label="更新时间" span={2}>{selectedCustomer.updatedAt}</Descriptions.Item>
                    <Descriptions.Item label="备注" span={2}>
                      {selectedCustomer.remark || '无'}
                    </Descriptions.Item>
                  </Descriptions>
                )
              },
              {
                key: 'credit',
                label: '信用信息',
                children: (
                  <>
                    <Row gutter={16}>
                      <Col span={12}>
                        <Card>
                          <Statistic
                            title="信用额度"
                            value={selectedCustomer.creditLimit || 0}
                            precision={0}
                            prefix="¥"
                            valueStyle={{ color: '#1890ff' }}
                          />
                        </Card>
                      </Col>
                      <Col span={12}>
                        <Card>
                          <Statistic
                            title="已用额度"
                            value={selectedCustomer.usedCredit || 0}
                            precision={0}
                            prefix="¥"
                            valueStyle={{ color: '#cf1322' }}
                          />
                        </Card>
                      </Col>
                    </Row>
                    <div style={{ marginTop: 16 }}>
                      <h4>信用使用情况</h4>
                      <Progress
                        percent={getCreditUsageRate(selectedCustomer.usedCredit || 0, selectedCustomer.creditLimit || 0)}
                        status={getCreditUsageRate(selectedCustomer.usedCredit || 0, selectedCustomer.creditLimit || 0) > 80 ? 'exception' : 'normal'}
                        strokeColor={{
                          '0%': '#108ee9',
                          '100%': '#87d068',
                        }}
                      />
                      <p style={{ marginTop: 8, color: '#666' }}>
                        可用额度: ¥{((selectedCustomer.creditLimit || 0) - (selectedCustomer.usedCredit || 0)).toLocaleString()}
                      </p>
                    </div>
                  </>
                )
              },
              {
                key: 'products',
                label: '产品偏好',
                children: (
                  <div>
                    <h4>常购产品型号</h4>
                    <div style={{ marginTop: 16 }}>
                      {(selectedCustomer.preferredProducts || []).map(product => (
                        <Tag key={product} color="blue" style={{ marginBottom: 8 }}>
                          {product}
                        </Tag>
                      ))}
                      {(!selectedCustomer.preferredProducts || selectedCustomer.preferredProducts.length === 0) && (
                        <span style={{ color: '#999' }}>暂无产品偏好数据</span>
                      )}
                    </div>
                  </div>
                )
              }
            ]}
          />
        )}
      </Modal>
    </div>
  )
}

// 用App组件包裹以提供message等上下文
export default function CustomerManagementPage() {
  return (
    <App>
      <CustomerManagement />
    </App>
  )
}
