'use client'

import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Form,
  Modal,
  Row,
  Col,
  Statistic,
  Alert,
  Tabs,
  Progress,
  DatePicker,
  InputNumber,
  List,
  Badge,
  App
} from 'antd'
import {
  CalculatorOutlined,
  DollarOutlined,
  Pie<PERSON>hartOutlined,
  FileTextOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  ReloadOutlined,
  DownloadOutlined,
  Bar<PERSON>hartOutlined,
  ArrowUpOutlined} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import {
  CostCalculation,
  CostReconciliation,
  ProductModel,
  WorkReportRecord,
  EmployeeBinding
} from '@/types'
import { CostCalculationEngine } from './utils/costCalculationEngine'
import { styleHelpers } from '@/utils/styles/antdHelpers'

const { Option } = Select
const { RangePicker } = DatePicker

const CostCalculationPage: React.FC = () => {
  const { modal } = App.useApp()
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [calculationModalVisible, setCalculationModalVisible] = useState(false)
  const [reconciliationModalVisible, setReconciliationModalVisible] = useState(false)
  const [activeTab, setActiveTab] = useState('calculations')

  // 模拟数据
  const [productModels] = useState<ProductModel[]>([
    {
      id: '1',
      modelCode: 'JJS-0001',
      modelName: '精密机械组件A',
      formingMold: 'M-JX-05',
      formingMoldQuantity: 4,
      hotPressMold: 'M-RY-12',
      hotPressMoldQuantity: 2,
      piecesPerMold: 4,
      formingPiecePrice: 8.0,
      hotPressPiecePrice: 2.5,
      productPrice: 0.150,
      productWeight: 12.50,
      boxSpecification: '30×20×15 cm',
      packingQuantity: 100,
      status: 'active',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: '2',
      modelCode: 'JJS-0002',
      modelName: '电子控制器B',
      formingMold: 'M-JX-08',
      formingMoldQuantity: 6,
      hotPressMold: 'M-RY-15',
      hotPressMoldQuantity: 3,
      piecesPerMold: 6,
      formingPiecePrice: 12.0,
      hotPressPiecePrice: 3.5,
      productPrice: 0.258,
      productWeight: 18.60,
      boxSpecification: '25×15×8 cm',
      packingQuantity: 80,
      status: 'active',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    }
  ])

  const [costCalculations, setCostCalculations] = useState<CostCalculation[]>([
    {
      id: 'cost-001',
      calculationDate: '2024-01-15',
      productionOrderId: 'order-001',
      productModelCode: 'JJS-0001',
      totalMolds: 1500,
      formingCost: {
        totalMolds: 1500,
        pieceRate: 8.0,
        totalWage: 12000,
        operatorRecords: [
          {
            operatorId: 'OP001',
            operatorName: '张师傅',
            completedMolds: 800,
            pieceRate: 8.0,
            wage: 6400
          },
          {
            operatorId: 'OP002',
            operatorName: '李师傅',
            completedMolds: 700,
            pieceRate: 8.0,
            wage: 5600
          }
        ]
      },
      hotPressCost: {
        totalMolds: 1500,
        pieceRate: 2.5,
        totalWage: 3750,
        employeeBindings: [
          {
            employeeId: 'EMP001',
            employeeName: '王师傅',
            bindingMolds: 800,
            pieceRate: 2.5,
            wage: 2000
          },
          {
            employeeId: 'EMP002',
            employeeName: '赵师傅',
            bindingMolds: 700,
            pieceRate: 2.5,
            wage: 1750
          }
        ]
      },
      materialCost: {
        materialCode: 'MAT-CP-202',
        materialName: 'CP-202原料',
        usedQuantity: 0.375, // 吨
        unitPrice: 1500,
        totalCost: 562.5
      },
      totalCost: 16312.5,
      unitCost: 10.875,
      createdAt: '2024-01-15T18:00:00Z'
    }
  ])

  const [costReconciliations, setCostReconciliations] = useState<CostReconciliation[]>([
    {
      id: 'reconciliation-001',
      reconciliationDate: '2024-01-15',
      productModelCode: 'JJS-0001',
      hotPressQuantityTotal: 1500,
      warehouseQuantity: 1450,
      onSiteQuantity: 45,
      bindingQuantityTotal: 1500,
      quantityVariance: 5,
      variancePercentage: 0.33,
      isVarianceAcceptable: true,
      materialTheoreticalUsage: 375,
      materialActualUsage: 380,
      materialVariance: 5,
      status: 'pending',
      createdAt: '2024-01-15T18:00:00Z'
    }
  ])

  // 生成成本分析报告
  const costAnalysisReport = CostCalculationEngine.generateCostAnalysisReport(costCalculations)

  // 执行成本计算
  const handleCostCalculation = async (values: any) => {
    setLoading(true)

    try {
      const productModel = productModels.find(pm => pm.modelCode === values.productModelCode)
      if (!productModel) {
        throw new Error('产品型号不存在')
      }

      // 模拟报工记录和员工绑定数据
      const mockWorkReports: WorkReportRecord[] = [
        {
          id: 'report-001',
          workstationId: 'W1',

          operatorId: values.operatorId || 'OP001',
          operatorName: values.operatorName || '操作员',
          reportTime: new Date().toISOString(),
          completedMolds: values.totalMolds,
          reportType: 'normal',
          isValidated: true,
          createdAt: new Date().toISOString()
        }
      ]

      const mockEmployeeBindings: EmployeeBinding[] = [
        {
          id: 'binding-001',
          employeeId: values.employeeId || 'EMP001',
          employeeName: values.employeeName || '员工',
          employeeCode: 'E001',
          taskId: 'task-001',
          batchNumber: 'BATCH-001',
          bindingTime: new Date().toISOString(),
          scanMethod: 'qr_code',
          bindingMolds: values.totalMolds,
          isActive: true,
          createdAt: new Date().toISOString()
        }
      ]

      // 执行成本计算
      const costCalculation = CostCalculationEngine.executeFullCostCalculation(
        values.productionOrderId,
        productModel,
        mockWorkReports,
        mockEmployeeBindings,
        values.materialUsageRate || 250,
        values.materialUnitPrice || 1500
      )

      setCostCalculations(prev => [costCalculation, ...prev])
      setCalculationModalVisible(false)
      form.resetFields()

    } catch (error) {
      modal.error({
        title: '计算失败',
        content: '成本计算过程中发生错误，请重试'
      })
    } finally {
      setLoading(false)
    }
  }

  // 执行成本对账
  const handleCostReconciliation = async (values: any) => {
    setLoading(true)

    try {
      // 模拟员工绑定数据
      const mockBindings: EmployeeBinding[] = [
        {
          id: 'binding-001',
          employeeId: 'EMP001',
          employeeName: '员工1',
          employeeCode: 'E001',
          taskId: 'task-001',
          batchNumber: 'BATCH-001',
          bindingTime: new Date().toISOString(),
          scanMethod: 'qr_code',
          bindingMolds: values.hotPressQuantity,
          isActive: true,
          createdAt: new Date().toISOString()
        }
      ]

      const reconciliation = CostCalculationEngine.performCostReconciliation(
        values.productModelCode,
        mockBindings,
        values.warehouseQuantity,
        values.onSiteQuantity,
        values.theoreticalMaterialUsage,
        values.actualMaterialUsage
      )

      setCostReconciliations(prev => [reconciliation, ...prev])
      setReconciliationModalVisible(false)
      form.resetFields()

    } catch (error) {
      modal.error({
        title: '对账失败',
        content: '成本对账过程中发生错误，请重试'
      })
    } finally {
      setLoading(false)
    }
  }

  // 成本计算表格列定义
  const costCalculationColumns: ColumnsType<CostCalculation> = [
    {
      title: '计算日期',
      dataIndex: 'calculationDate',
      key: 'calculationDate',
      width: 100,
    },
    {
      title: '产品型号',
      dataIndex: 'productModelCode',
      key: 'productModelCode',
      width: 100,
    },
    {
      title: '总模数',
      dataIndex: 'totalMolds',
      key: 'totalMolds',
      width: 80,
      render: (molds) => `${molds} 模`
    },
    {
      title: '成型成本',
      key: 'formingCost',
      width: 100,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>¥{record.formingCost.totalWage.toFixed(2)}</div>
          <div style={{ fontSize: '12px', color: '#9ca3af' }}>¥{record.formingCost.pieceRate}/模</div>
        </div>
      )
    },
    {
      title: '热压成本',
      key: 'hotPressCost',
      width: 100,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>¥{record.hotPressCost.totalWage.toFixed(2)}</div>
          <div style={{ fontSize: '12px', color: '#9ca3af' }}>¥{record.hotPressCost.pieceRate}/模</div>
        </div>
      )
    },
    {
      title: '材料成本',
      key: 'materialCost',
      width: 100,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>¥{record.materialCost.totalCost.toFixed(2)}</div>
          <div style={{ fontSize: '12px', color: '#9ca3af' }}>{record.materialCost.usedQuantity.toFixed(3)}吨</div>
        </div>
      )
    },
    {
      title: '总成本',
      dataIndex: 'totalCost',
      key: 'totalCost',
      width: 100,
      render: (cost) => (
        <div style={{ fontWeight: 500, color: '#1890ff' }}>¥{cost.toFixed(2)}</div>
      )
    },
    {
      title: '总收入',
      dataIndex: 'totalRevenue',
      key: 'totalRevenue',
      width: 100,
      render: (revenue) => (
        <div style={{ fontWeight: 500, color: '#52c41a' }}>
          {revenue ? `¥${revenue.toFixed(3)}` : '-'}
        </div>
      )
    },
    {
      title: '总利润',
      dataIndex: 'totalProfit',
      key: 'totalProfit',
      width: 100,
      render: (profit) => (
        <div style={{ fontWeight: 500, color: profit && profit >= 0 ? '#52c41a' : '#ff4d4f' }}>
          {profit ? `¥${profit.toFixed(3)}` : '-'}
        </div>
      )
    },
    {
      title: '利润率',
      dataIndex: 'profitMargin',
      key: 'profitMargin',
      width: 100,
      render: (margin) => (
        <div style={{ fontWeight: 500, color: margin && margin >= 0 ? '#52c41a' : '#ff4d4f' }}>
          {margin ? `${margin.toFixed(1)}%` : '-'}
        </div>
      )
    },
    {
      title: '单位成本',
      dataIndex: 'unitCost',
      key: 'unitCost',
      width: 100,
      render: (cost) => (
        <div style={{ fontWeight: 500, color: '#722ed1' }}>¥{cost.toFixed(3)}/模</div>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_, record) => (
        <Space size="small">
          <Button type="text" size="small" icon={<FileTextOutlined />}>
            详情
          </Button>
        </Space>
      ),
    },
  ]

  // 对账记录表格列定义
  const reconciliationColumns: ColumnsType<CostReconciliation> = [
    {
      title: '对账日期',
      dataIndex: 'reconciliationDate',
      key: 'reconciliationDate',
      width: 100,
    },
    {
      title: '产品型号',
      dataIndex: 'productModelCode',
      key: 'productModelCode',
      width: 100,
    },
    {
      title: '数量对账',
      key: 'quantityReconciliation',
      width: 150,
      render: (_, record) => (
        <div>
          <div style={{ fontSize: '14px' }}>
            <span>热压: {record.hotPressQuantityTotal}</span>
            <span style={{ margin: '0 8px' }}>|</span>
            <span>入库: {record.warehouseQuantity}</span>
          </div>
          <div style={{ fontSize: '12px', color: '#9ca3af' }}>
            差异: {record.quantityVariance} ({record.variancePercentage}%)
          </div>
        </div>
      )
    },
    {
      title: '材料对账',
      key: 'materialReconciliation',
      width: 120,
      render: (_, record) => (
        <div>
          <div style={{ fontSize: '14px' }}>
            差异: {record.materialVariance}kg
          </div>
          <div style={{ fontSize: '12px', color: '#9ca3af' }}>
            实际: {record.materialActualUsage}kg
          </div>
        </div>
      )
    },
    {
      title: '对账状态',
      key: 'reconciliationStatus',
      width: 120,
      render: (_, record) => (
        <div>
          <Badge
            status={record.isVarianceAcceptable ? 'success' : 'error'}
            text={record.isVarianceAcceptable ? '正常' : '异常'}
          />
          <div style={{ fontSize: '12px', color: '#9ca3af', marginTop: '4px' }}>
            {record.status === 'pending' ? '待审核' :
             record.status === 'reviewed' ? '已审核' : '已批准'}
          </div>
        </div>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_, record) => (
        <Space size="small">
          <Button type="text" size="small" icon={<CheckCircleOutlined />}>
            审核
          </Button>
        </Space>
      ),
    },
  ]

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: styleHelpers.spacing.lg }}>
      {/* 页面标题 */}
      <div className="page-header">
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <CalculatorOutlined style={{ fontSize: '24px', color: '#722ed1', marginRight: '12px' }} />
            <div>
              <h1 className="page-title">成本计算</h1>
              <p className="page-description">生产成本计算和对账系统</p>
            </div>
          </div>
          <Space>
            <Button icon={<DownloadOutlined />}>导出报表</Button>
            <Button icon={<ReloadOutlined />} onClick={() => window.location.reload()}>
              刷新数据
            </Button>
          </Space>
        </div>
      </div>

      {/* 成本概览 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="总生产量"
              value={costAnalysisReport.totalProduction}
              suffix="模"
              valueStyle={{ color: '#1890ff' }}
              prefix={<BarChartOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="总成本"
              value={costAnalysisReport.totalCost}
              suffix="元"
              valueStyle={{ color: '#52c41a' }}
              prefix={<DollarOutlined />}
              precision={2}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="平均单位成本"
              value={costAnalysisReport.averageUnitCost}
              suffix="元/模"
              valueStyle={{ color: '#722ed1' }}
              prefix={<ArrowUpOutlined />}
              precision={3}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="待审核对账"
              value={costReconciliations.filter(r => r.status === 'pending').length}
              suffix="项"
              valueStyle={{ color: '#fa8c16' }}
              prefix={<ExclamationCircleOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* 成本结构分析 */}
      <Card title="成本结构分析">
        <Row gutter={[16, 16]}>
          <Col xs={24} lg={12}>
            <div style={{ display: 'flex', flexDirection: 'column', gap: styleHelpers.spacing.md }}>
              <div>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: styleHelpers.spacing.sm }}>
                  <span>成型成本</span>
                  <span style={{ fontWeight: 500 }}>{costAnalysisReport.costBreakdown.formingCostRatio.toFixed(1)}%</span>
                </div>
                <Progress
                  percent={costAnalysisReport.costBreakdown.formingCostRatio}
                  strokeColor="#1890ff"
                  showInfo={false}
                />
              </div>
              <div>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: styleHelpers.spacing.sm }}>
                  <span>热压成本</span>
                  <span style={{ fontWeight: 500 }}>{costAnalysisReport.costBreakdown.hotPressCostRatio.toFixed(1)}%</span>
                </div>
                <Progress
                  percent={costAnalysisReport.costBreakdown.hotPressCostRatio}
                  strokeColor="#52c41a"
                  showInfo={false}
                />
              </div>
              <div>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: styleHelpers.spacing.sm }}>
                  <span>材料成本</span>
                  <span style={{ fontWeight: 500 }}>{costAnalysisReport.costBreakdown.materialCostRatio.toFixed(1)}%</span>
                </div>
                <Progress
                  percent={costAnalysisReport.costBreakdown.materialCostRatio}
                  strokeColor="#faad14"
                  showInfo={false}
                />
              </div>
            </div>
          </Col>
          <Col xs={24} lg={12}>
            <div style={{ backgroundColor: '#f9fafb', padding: styleHelpers.spacing.md, borderRadius: '6px' }}>
              <h4 style={{ fontWeight: 500, marginBottom: '12px' }}>成本分析建议</h4>
              <ul style={{ display: 'flex', flexDirection: 'column', gap: styleHelpers.spacing.sm, fontSize: '14px' }}>
                <li>• 成型成本占比最高，建议优化生产效率</li>
                <li>• 材料成本相对稳定，关注原料价格波动</li>
                <li>• 热压成本占比较低，工艺效率良好</li>
                <li>• 建议定期对比历史数据，识别成本趋势</li>
              </ul>
            </div>
          </Col>
        </Row>
      </Card>

      {/* 主要功能标签页 */}
      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          tabBarExtraContent={
            <Space>
              <Button
                type="primary"
                icon={<CalculatorOutlined />}
                onClick={() => setCalculationModalVisible(true)}
              >
                新建成本计算
              </Button>
              <Button
                icon={<PieChartOutlined />}
                onClick={() => setReconciliationModalVisible(true)}
              >
                成本对账
              </Button>
            </Space>
          }
          items={[
            {
              key: 'calculations',
              label: '成本计算记录',
              children: (
                <Table
                  columns={costCalculationColumns}
                  dataSource={costCalculations}
                  rowKey="id"
                  pagination={{ pageSize: 10 }}
                  scroll={{ x: 1000 }}
                />
              )
            },
            {
              key: 'reconciliations',
              label: '对账记录',
              children: (
                <Table
                  columns={reconciliationColumns}
                  dataSource={costReconciliations}
                  rowKey="id"
                  pagination={{ pageSize: 10 }}
                  scroll={{ x: 800 }}
                />
              )
            },
            {
              key: 'trends',
              label: '成本趋势',
              children: (
                <div style={{ display: 'flex', flexDirection: 'column', gap: styleHelpers.spacing.md }}>
                  <Alert
                    message="成本趋势分析"
                    description="基于历史数据分析成本变化趋势，帮助优化生产成本控制"
                    type="info"
                    showIcon
                  />

                  <div style={{ backgroundColor: '#f9fafb', padding: styleHelpers.spacing.md, borderRadius: '6px' }}>
                    <h4 style={{ fontWeight: 500, marginBottom: '12px' }}>趋势数据</h4>
                    <List
                      dataSource={costAnalysisReport.trends}
                      renderItem={(trend) => (
                        <List.Item>
                          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
                            <span>{trend.period}</span>
                            <div style={{ textAlign: 'right' }}>
                              <div style={{ fontWeight: 500 }}>¥{trend.unitCost.toFixed(3)}/模</div>
                              <div style={{ fontSize: '14px', color: '#9ca3af' }}>{trend.totalMolds} 模</div>
                            </div>
                          </div>
                        </List.Item>
                      )}
                    />
                  </div>
                </div>
              )
            }
          ]}
        />
      </Card>

      {/* 成本计算模态框 */}
      <Modal
        title="新建成本计算"
        open={calculationModalVisible}
        onCancel={() => setCalculationModalVisible(false)}
        onOk={() => form.submit()}
        confirmLoading={loading}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCostCalculation}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="生产订单ID"
                name="productionOrderId"
                rules={[{ required: true, message: '请输入生产订单ID' }]}
              >
                <Input placeholder="请输入生产订单ID" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="产品型号"
                name="productModelCode"
                rules={[{ required: true, message: '请选择产品型号' }]}
              >
                <Select placeholder="请选择产品型号">
                  {productModels.map(model => (
                    <Option key={model.modelCode} value={model.modelCode}>
                      {model.modelCode} - {model.modelName}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="总模数"
                name="totalMolds"
                rules={[{ required: true, message: '请输入总模数' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="请输入总模数"
                  min={1}
                  addonAfter="模"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="材料使用率"
                name="materialUsageRate"
                initialValue={250}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="材料使用率"
                  min={1}
                  addonAfter="克/模"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="材料单价"
                name="materialUnitPrice"
                initialValue={1500}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="材料单价"
                  min={1}
                  addonAfter="元/吨"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="操作员姓名"
                name="operatorName"
              >
                <Input placeholder="操作员姓名（可选）" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>

      {/* 成本对账模态框 */}
      <Modal
        title="成本对账"
        open={reconciliationModalVisible}
        onCancel={() => setReconciliationModalVisible(false)}
        onOk={() => form.submit()}
        confirmLoading={loading}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCostReconciliation}
        >
          <Form.Item
            label="产品型号"
            name="productModelCode"
            rules={[{ required: true, message: '请选择产品型号' }]}
          >
            <Select placeholder="请选择产品型号">
              {productModels.map(model => (
                <Option key={model.modelCode} value={model.modelCode}>
                  {model.modelCode} - {model.modelName}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="热压数量"
                name="hotPressQuantity"
                rules={[{ required: true, message: '请输入热压数量' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="热压数量"
                  min={1}
                  addonAfter="模"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="入库数量"
                name="warehouseQuantity"
                rules={[{ required: true, message: '请输入入库数量' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="入库数量"
                  min={0}
                  addonAfter="模"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="场地暂存"
                name="onSiteQuantity"
                rules={[{ required: true, message: '请输入场地暂存数量' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="场地暂存数量"
                  min={0}
                  addonAfter="模"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="理论材料用量"
                name="theoreticalMaterialUsage"
                rules={[{ required: true, message: '请输入理论材料用量' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="理论材料用量"
                  min={0}
                  addonAfter="kg"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="实际材料用量"
            name="actualMaterialUsage"
            rules={[{ required: true, message: '请输入实际材料用量' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="实际材料用量"
              min={0}
              addonAfter="kg"
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default CostCalculationPage
