'use client'

import React, { useEffect, useState } from 'react'
import { Layout, Menu, Drawer } from 'antd'
import { useRouter, usePathname } from 'next/navigation'
import {
  DashboardOutlined,
  ShoppingCartOutlined,
  ShopOutlined,
  InboxOutlined,
  ToolOutlined,
  DollarOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DatabaseOutlined,
  AppstoreOutlined,
  BarcodeOutlined,
  UserOutlined,
  TeamOutlined
} from '@ant-design/icons'
import { useAppStore } from '@/store/useAppStore'
import { styleHelpers } from '@/utils/styles/antdHelpers'
import { MenuItem } from '@/types'

const { Sider } = Layout

const menuItems: MenuItem[] = [
  {
    key: 'dashboard',
    label: '仪表板',
    icon: <DashboardOutlined />,
    path: '/dashboard'
  },
  {
    key: 'sales',
    label: '销售管理',
    icon: <ShoppingCartOutlined />,
    children: [
      {
        key: 'sales-orders',
        label: '订单管理',
        path: '/sales/orders'
      },
      {
        key: 'sales-credit',
        label: '信用管理',
        path: '/sales/credit'
      },
      {
        key: 'sales-delivery',
        label: '发货管理',
        path: '/sales/delivery'
      },
      {
        key: 'sales-finance',
        label: '财务协同',
        children: [
          {
            key: 'sales-invoices',
            label: '开票管理',
            path: '/sales/invoices'
          },
          {
            key: 'sales-payments',
            label: '收款管理',
            path: '/sales/payments'
          }
        ]
      },
      {
        key: 'sales-after-sales',
        label: '售后服务',
        path: '/sales/after-sales'
      },
      {
        key: 'sales-analytics',
        label: '决策分析',
        path: '/sales/analytics'
      }
    ]
  },
  {
    key: 'procurement',
    label: '采购管理',
    icon: <ShopOutlined />,
    path: '/procurement'
  },
  {
    key: 'warehouse',
    label: '仓库管理',
    icon: <InboxOutlined />,
    children: [
      {
        key: 'warehouse-products',
        label: '产品库存',
        path: '/warehouse/products'
      },
      {
        key: 'warehouse-materials',
        label: '原料库存',
        path: '/warehouse/materials'
      },
      {
        key: 'warehouse-overview',
        label: '库存总览',
        path: '/warehouse'
      }
    ]
  },
  {
    key: 'production',
    label: '生产管理',
    icon: <ToolOutlined />,
    children: [
      {
        key: 'production-orders',
        label: '生产订单管理',
        path: '/production/orders'
      }
    ]
  },
  {
    key: 'finance',
    label: '财务管理',
    icon: <DollarOutlined />,
    path: '/finance'
  },
  {
    key: 'master-data',
    label: '主数据管理',
    icon: <DatabaseOutlined />,
    children: [
      {
        key: 'employees',
        label: '员工信息',
        icon: <UserOutlined />,
        path: '/master-data/employees'
      },
      {
        key: 'master-data-customers',
        label: '客户档案',
        icon: <TeamOutlined />,
        path: '/sales/customers'
      },
      {
        key: 'materials',
        label: '原料数据',
        icon: <AppstoreOutlined />,
        path: '/master-data/materials'
      },
      {
        key: 'product-models',
        label: '产品数据',
        icon: <BarcodeOutlined />,
        path: '/master-data/product-models'
      }
    ]
  }
]

export const Sidebar: React.FC = () => {
  const router = useRouter()
  const pathname = usePathname()
  const { sidebarCollapsed, toggleSidebar, setCurrentModule } = useAppStore()
  const [isMobile, setIsMobile] = useState(false)
  const [mobileDrawerVisible, setMobileDrawerVisible] = useState(false)

  // 检测屏幕尺寸
  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 768)
    }

    checkScreenSize()
    window.addEventListener('resize', checkScreenSize)

    return () => window.removeEventListener('resize', checkScreenSize)
  }, [])

  // 监听移动端菜单切换事件
  useEffect(() => {
    const handleMobileMenuToggle = () => {
      if (isMobile) {
        setMobileDrawerVisible(!mobileDrawerVisible)
      }
    }

    window.addEventListener('toggleMobileSidebar', handleMobileMenuToggle)

    return () => window.removeEventListener('toggleMobileSidebar', handleMobileMenuToggle)
  }, [isMobile, mobileDrawerVisible])

  const handleMenuClick = (item: MenuItem) => {
    if (item.path) {
      router.push(item.path)
      setCurrentModule(item.key)
      // 移动端点击菜单后关闭抽屉
      if (isMobile) {
        setMobileDrawerVisible(false)
      }
    }
  }

  // 递归处理菜单项，包括子菜单
  const processMenuItems = (items: MenuItem[]): any[] => {
    return items.map(item => {
      if (item.children) {
        return {
          key: item.key,
          icon: item.icon,
          label: item.label,
          children: processMenuItems(item.children)
        }
      } else {
        return {
          key: item.key,
          icon: item.icon,
          label: item.label,
          onClick: () => handleMenuClick(item)
        }
      }
    })
  }

  const getSelectedKey = () => {
    const currentPath = pathname
    // 递归查找匹配的菜单项
    const findMenuItem = (items: MenuItem[]): MenuItem | undefined => {
      for (const item of items) {
        if (item.path === currentPath) {
          return item
        }
        if (item.children) {
          const found = findMenuItem(item.children)
          if (found) return found
        }
      }
      return undefined
    }

    const currentItem = findMenuItem(menuItems)
    return currentItem ? [currentItem.key] : ['dashboard']
  }

  const handleToggleSidebar = () => {
    if (isMobile) {
      setMobileDrawerVisible(!mobileDrawerVisible)
    } else {
      toggleSidebar()
    }
  }

  // 菜单内容组件
  const MenuContent = () => (
    <>
      {/* Logo区域 */}
      <div style={{
        height: 64,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        borderBottom: '1px solid #f0f0f0'
      }}>
        {(isMobile || !sidebarCollapsed) ? (
          <div style={{
            fontSize: '20px',
            fontWeight: 'bold',
            color: styleHelpers.colors.primary[600]
          }}>ERP系统</div>
        ) : (
          <div style={{
            fontSize: '24px',
            fontWeight: 'bold',
            color: styleHelpers.colors.primary[600]
          }}>E</div>
        )}
      </div>

      {/* 折叠按钮 - 仅桌面端显示 */}
      {!isMobile && (
        <div style={{
          padding: styleHelpers.spacing.md,
          borderBottom: '1px solid #f0f0f0'
        }}>
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: 32,
              height: 32,
              borderRadius: styleHelpers.borderRadius.lg,
              cursor: 'pointer',
              transition: 'background-color 0.2s ease',
              backgroundColor: 'transparent'
            }}
            onClick={handleToggleSidebar}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = styleHelpers.colors.gray[100]
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent'
            }}
          >
            {sidebarCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
          </div>
        </div>
      )}

      {/* 菜单 */}
      <Menu
        mode="inline"
        selectedKeys={getSelectedKey()}
        style={{ border: 'none' }}
        items={processMenuItems(menuItems)}
      />
    </>
  )

  // 移动端使用Drawer，桌面端使用Sider
  if (isMobile) {
    return (
      <Drawer
        title={null}
        placement="left"
        closable={false}
        onClose={() => setMobileDrawerVisible(false)}
        open={mobileDrawerVisible}
        styles={{ body: { padding: 0 } }}
        width={256}
      >
        <MenuContent />
      </Drawer>
    )
  }

  return (
    <Sider
      trigger={null}
      collapsible
      collapsed={sidebarCollapsed}
      width={256}
      collapsedWidth={80}
      style={{
        position: 'fixed',
        left: 0,
        top: 0,
        bottom: 0,
        zIndex: 50,
        boxShadow: styleHelpers.shadows.lg,
        background: '#ffffff',
        borderRight: '1px solid #f0f0f0',
        display: isMobile ? 'none' : 'block'
      }}
    >
      <MenuContent />
    </Sider>
  )
}
