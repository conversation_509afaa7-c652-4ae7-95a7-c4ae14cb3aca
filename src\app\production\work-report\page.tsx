'use client'

import React, { useState } from 'react'
import { Card, Row, Col, Statistic, Table, Button, Modal, Form, Select, InputNumber, Alert, message, Badge, Tag, Typography } from 'antd'
import { PlayCircleOutlined, PauseCircleOutlined, CheckCircleOutlined, TrophyOutlined } from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import {
  WorkReportRecord,
  Workstation
} from '@/types'
import { styleHelpers } from '@/utils/styles/antdHelpers'

const { Option } = Select
const { Title } = Typography

const WorkReportPage: React.FC = () => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [reportModalVisible, setReportModalVisible] = useState(false)
  const [selectedWorkstation, setSelectedWorkstation] = useState<string>('')
  const [reportMolds, setReportMolds] = useState<number>(0)

  // 模拟数据
  const [workstations] = useState<Workstation[]>([
    {
      id: 'W1',
      code: 'A1',
      name: '成型机A线-1号工位',
      description: '主要用于圆形餐盘生产',
      status: 'active',
      // 生产状态字段
      currentMoldNumber: 'M001',
      currentBatchNumber: 'PC202412050001',
      batchNumberQueue: ['PC202412050003'],
      lastEndTime: '2024-12-05T18:00:00Z',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-15T14:00:00Z',
      version: 1,
      lastModifiedBy: 'system',
      lastModifiedAt: '2024-01-15T14:00:00Z'
    },
    {
      id: 'W2',
      code: 'B1',
      name: '成型机B线-1号工位',
      description: '主要用于方形餐盒生产',
      status: 'active',
      // 生产状态字段
      currentMoldNumber: 'M002',
      currentBatchNumber: 'PC202412050002',
      batchNumberQueue: [],
      lastEndTime: '2024-12-05T16:30:00Z',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-15T15:00:00Z',
      version: 1,
      lastModifiedBy: 'system',
      lastModifiedAt: '2024-01-15T15:00:00Z'
    },
    {
      id: 'W3',
      code: 'C1',
      name: '成型机C线-1号工位',
      description: '备用工位',
      status: 'inactive',
      // 生产状态字段（停用状态）
      currentMoldNumber: null,
      currentBatchNumber: null,
      batchNumberQueue: [],
      lastEndTime: null,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-10T08:00:00Z',
      version: 1,
      lastModifiedBy: 'system',
      lastModifiedAt: '2024-01-10T08:00:00Z'
    }
  ])

  const [workReports, setWorkReports] = useState<WorkReportRecord[]>([
    {
      id: 'report-001',
      workstationId: 'W1',

      operatorId: 'OP001',
      operatorName: '张师傅',
      reportTime: '2024-01-15T14:00:00Z',
      completedMolds: 300,
      reportType: 'normal',
      isValidated: true,
      remark: '正常生产',
      createdAt: '2024-01-15T14:00:00Z'
    },
    {
      id: 'report-002',
      workstationId: 'W2',

      operatorId: 'OP002',
      operatorName: '李师傅',
      reportTime: '2024-01-15T15:00:00Z',
      completedMolds: 400,
      reportType: 'normal',
      isValidated: true,
      remark: '正常生产',
      createdAt: '2024-01-15T15:00:00Z'
    }
  ])

  // 获取今日产量
  const getTodayOutput = (workstationId: string): number => {
    const today = new Date().toDateString()
    return workReports
      .filter(report => 
        report.workstationId === workstationId &&
        new Date(report.reportTime).toDateString() === today
      )
      .reduce((sum, report) => sum + report.completedMolds, 0)
  }

  // 获取今日报工次数
  const getTodayReports = (workstationId: string): number => {
    const today = new Date().toDateString()
    return workReports.filter(report => 
      report.workstationId === workstationId &&
      new Date(report.reportTime).toDateString() === today
    ).length
  }

  // 处理工位选择
  const handleWorkstationSelect = (workstationId: string) => {
    setSelectedWorkstation(workstationId)
    setReportModalVisible(true)
  }

  // 提交报工
  const handleSubmitReport = async () => {
    if (!selectedWorkstation) return

    setLoading(true)

    try {
      const workstation = workstations.find(ws => ws.id === selectedWorkstation)
      if (!workstation) throw new Error('工位不存在')

      // 创建报工记录
      const reportRecord: WorkReportRecord = {
        id: `report-${Date.now()}`,
        workstationId: selectedWorkstation,

        operatorId: 'unknown',
        operatorName: '未知操作员',
        reportTime: new Date().toISOString(),
        completedMolds: reportMolds,
        reportType: 'normal',
        isValidated: false,
        remark: '手动报工',
        createdAt: new Date().toISOString()
      }

      // 更新状态
      setWorkReports(prev => [...prev, reportRecord])

      message.success('报工成功！')
      setReportModalVisible(false)
      setReportMolds(0)
      form.resetFields()

    } catch (error) {
      message.error('报工失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  // 工位卡片渲染
  const renderWorkstationCard = (workstation: Workstation) => {
    const todayOutput = getTodayOutput(workstation.id)
    const todayReports = getTodayReports(workstation.id)

    return (
      <Col xs={24} lg={8} key={workstation.id}>
        <Card
          title={
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span>{workstation.name}</span>
              <Badge
                status={workstation.status === 'active' ? 'processing' : 'default'}
                text={workstation.status === 'active' ? '运行中' : '空闲'}
              />
            </div>
          }
          extra={
            <Button 
              type="primary" 
              size="small"
              icon={<PlayCircleOutlined />}
              onClick={() => handleWorkstationSelect(workstation.id)}
              disabled={workstation.status !== 'active'}
            >
              报工
            </Button>
          }
          style={{ marginBottom: styleHelpers.spacing.md }}
        >
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <span style={{ color: '#6b7280' }}>负责人:</span>
              <span style={{ fontWeight: 500 }}>未分配</span>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <span style={{ color: '#6b7280' }}>当前批次:</span>
              <span style={{ fontWeight: 500 }}>{workstation.currentBatchNumber || '无'}</span>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <span style={{ color: '#6b7280' }}>今日产量:</span>
              <span style={{ fontWeight: 500, color: '#1890ff' }}>{todayOutput} 模</span>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <span style={{ color: '#6b7280' }}>报工次数:</span>
              <span style={{ fontWeight: 500 }}>{todayReports} 次</span>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <span style={{ color: '#6b7280' }}>状态:</span>
              <span style={{ fontWeight: 500, color: workstation.status === 'active' ? '#52c41a' : '#ff4d4f' }}>
                {workstation.status === 'active' ? '运行中' : '停用'}
              </span>
            </div>
          </div>
        </Card>
      </Col>
    )
  }

  // 报工记录表格列定义
  const reportColumns: ColumnsType<WorkReportRecord> = [
    {
      title: '报工时间',
      dataIndex: 'reportTime',
      key: 'reportTime',
      width: 180,
      render: (time) => new Date(time).toLocaleString()
    },
    {
      title: '工位',
      dataIndex: 'workstationId',
      key: 'workstationId',
      width: 120,
      render: (id) => {
        const ws = workstations.find(w => w.id === id)
        return ws ? ws.name : id
      }
    },
    {
      title: '操作员',
      dataIndex: 'operatorName',
      key: 'operatorName',
      width: 100,
    },
    {
      title: '报工模数',
      dataIndex: 'completedMolds',
      key: 'completedMolds',
      width: 120,
      render: (molds) => <span style={{ fontWeight: 500, color: '#1890ff' }}>{molds} 模</span>
    },
    {
      title: '类型',
      dataIndex: 'reportType',
      key: 'reportType',
      width: 100,
      render: (type) => (
        <Tag color={type === 'normal' ? 'green' : 'orange'}>
          {type === 'normal' ? '正常' : '异常'}
        </Tag>
      )
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      ellipsis: true
    }
  ]

  return (
    <div style={{ padding: styleHelpers.spacing.lg }}>
      <div style={{ marginBottom: styleHelpers.spacing.lg }}>
        <Title level={2}>工作报告</Title>
        <p style={{ color: '#6b7280' }}>实时监控生产进度，记录工作完成情况</p>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: styleHelpers.spacing.lg }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="活跃工位"
              value={workstations.filter(ws => ws.status === 'active').length}
              suffix="个"
              valueStyle={{ color: '#52c41a' }}
              prefix={<PlayCircleOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="今日报工"
              value={workReports.filter(report => 
                new Date(report.reportTime).toDateString() === new Date().toDateString()
              ).length}
              suffix="次"
              valueStyle={{ color: '#1890ff' }}
              prefix={<CheckCircleOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="今日产量"
              value={workReports
                .filter(report => 
                  new Date(report.reportTime).toDateString() === new Date().toDateString()
                )
                .reduce((sum, report) => sum + report.completedMolds, 0)
              }
              suffix="模"
              valueStyle={{ color: '#722ed1' }}
              prefix={<TrophyOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="空闲工位"
              value={workstations.filter(ws => ws.status === 'inactive').length}
              suffix="个"
              valueStyle={{ color: '#fa8c16' }}
              prefix={<PauseCircleOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* 工位状态 */}
      <Card title="工位状态" style={{ marginBottom: styleHelpers.spacing.lg }}>
        <Row gutter={[16, 16]}>
          {workstations.map(renderWorkstationCard)}
        </Row>
      </Card>

      {/* 报工记录 */}
      <Card title="报工记录">
        <Table
          columns={reportColumns}
          dataSource={workReports}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
        />
      </Card>

      {/* 报工模态框 */}
      <Modal
        title="工作报告"
        open={reportModalVisible}
        onCancel={() => setReportModalVisible(false)}
        onOk={handleSubmitReport}
        confirmLoading={loading}
        width={600}
      >
        {selectedWorkstation && (
          <div style={{ display: 'flex', flexDirection: 'column', gap: styleHelpers.spacing.md }}>
            <Alert
              message="报工信息确认"
              description="请确认报工信息后提交"
              type="info"
              showIcon
            />

            <Row gutter={16}>
              <Col span={12}>
                <div style={{ backgroundColor: '#f9fafb', padding: styleHelpers.spacing.md, borderRadius: '6px' }}>
                  <h4 style={{ fontWeight: 500, marginBottom: styleHelpers.spacing.sm }}>工位信息</h4>
                  <p><strong>工位:</strong> {workstations.find(ws => ws.id === selectedWorkstation)?.name}</p>
                  <p><strong>负责人:</strong> 未分配</p>
                  <p><strong>设备:</strong> {workstations.find(ws => ws.id === selectedWorkstation)?.name}</p>
                </div>
              </Col>
              <Col span={12}>
                <div style={{ backgroundColor: '#f9fafb', padding: styleHelpers.spacing.md, borderRadius: '6px' }}>
                  <h4 style={{ fontWeight: 500, marginBottom: styleHelpers.spacing.sm }}>生产信息</h4>
                  <p><strong>当前批次:</strong> {workstations.find(ws => ws.id === selectedWorkstation)?.currentBatchNumber || '无'}</p>
                  <p><strong>今日产量:</strong> {getTodayOutput(selectedWorkstation)} 模</p>
                  <p><strong>报工次数:</strong> {getTodayReports(selectedWorkstation)} 次</p>
                </div>
              </Col>
            </Row>

            <div>
              <h4 style={{ fontWeight: 500, marginBottom: styleHelpers.spacing.sm }}>报工模数</h4>
              <InputNumber
                value={reportMolds}
                onChange={(value) => setReportMolds(value || 0)}
                min={1}
                max={2000}
                style={{ width: '100%' }}
                placeholder="请输入完成的模数"
                addonAfter="模"
              />
            </div>
          </div>
        )}
      </Modal>
    </div>
  )
}

export default WorkReportPage
