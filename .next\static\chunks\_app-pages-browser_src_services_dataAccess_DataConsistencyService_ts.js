"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_services_dataAccess_DataConsistencyService_ts"],{

/***/ "(app-pages-browser)/./src/services/dataAccess/DataConsistencyService.ts":
/*!***********************************************************!*\
  !*** ./src/services/dataAccess/DataConsistencyService.ts ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DataConsistencyService: function() { return /* binding */ DataConsistencyService; },\n/* harmony export */   dataConsistencyService: function() { return /* binding */ dataConsistencyService; }\n/* harmony export */ });\n/* harmony import */ var _DataAccessManager__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./DataAccessManager */ \"(app-pages-browser)/./src/services/dataAccess/DataAccessManager.ts\");\n/* harmony import */ var _utils_business__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/business */ \"(app-pages-browser)/./src/utils/business/index.ts\");\n/**\n * 数据一致性验证服务\n * 检查和维护跨模块数据的一致性\n * 符合\"模块间的接口和数据交互标准\"文件要求\n */ \n\n/**\n * 数据一致性验证服务\n */ class DataConsistencyService {\n    static getInstance() {\n        if (!DataConsistencyService.instance) {\n            DataConsistencyService.instance = new DataConsistencyService();\n        }\n        return DataConsistencyService.instance;\n    }\n    /**\n   * 执行全面的数据一致性检查\n   */ async performFullConsistencyCheck() {\n        const results = [];\n        // 检查产品-库存一致性\n        const productInventoryCheck = await this.checkProductInventoryConsistency();\n        results.push(productInventoryCheck);\n        // 检查订单-客户一致性\n        const orderCustomerCheck = await this.checkOrderCustomerConsistency();\n        results.push(orderCustomerCheck);\n        // 检查订单-员工一致性\n        const orderEmployeeCheck = await this.checkOrderEmployeeConsistency();\n        results.push(orderEmployeeCheck);\n        // 计算总体统计\n        const totalIssues = results.reduce((sum, result)=>sum + result.inconsistentItems, 0);\n        const criticalIssues = results.reduce((sum, result)=>sum + result.issues.filter((issue)=>issue.severity === \"critical\").length, 0);\n        const overall = {\n            totalIssues,\n            criticalIssues,\n            // ✅ 使用统一时间戳生成器\n            timestamp: _utils_business__WEBPACK_IMPORTED_MODULE_1__.timestampGenerator.now()\n        };\n        return {\n            overall,\n            results\n        };\n    }\n    /**\n   * 检查产品-库存数据一致性\n   */ async checkProductInventoryConsistency() {\n        const issues = [];\n        let totalChecked = 0;\n        try {\n            // 获取所有库存记录\n            const inventoryResponse = await _DataAccessManager__WEBPACK_IMPORTED_MODULE_0__.dataAccessManager.inventory.getAll();\n            if (inventoryResponse.status === \"success\" && inventoryResponse.data) {\n                const inventoryItems = inventoryResponse.data.items;\n                totalChecked = inventoryItems.length;\n                for (const inventory of inventoryItems){\n                    // 检查对应的产品是否存在\n                    const productResponse = await _DataAccessManager__WEBPACK_IMPORTED_MODULE_0__.dataAccessManager.products.getByCode(inventory.productCode);\n                    if (productResponse.status !== \"success\" || !productResponse.data) {\n                        issues.push({\n                            type: \"missing_reference\",\n                            severity: \"high\",\n                            entityId: inventory.id,\n                            description: \"库存记录引用的产品编码 \".concat(inventory.productCode, \" 不存在\"),\n                            suggestedAction: \"删除孤立的库存记录或创建对应的产品记录\"\n                        });\n                        continue;\n                    }\n                    const product = productResponse.data;\n                    // 检查产品名称是否一致\n                    if (inventory.productName !== product.modelName) {\n                        issues.push({\n                            type: \"data_mismatch\",\n                            severity: \"medium\",\n                            entityId: inventory.id,\n                            description: \"库存记录中的产品名称与产品数据不一致\",\n                            expectedValue: product.modelName,\n                            actualValue: inventory.productName,\n                            suggestedAction: \"同步产品名称到库存记录\"\n                        });\n                    }\n                    // 检查产品状态\n                    if (product.status !== \"active\") {\n                        issues.push({\n                            type: \"invalid_status\",\n                            severity: \"low\",\n                            entityId: inventory.id,\n                            description: \"库存记录对应的产品状态为非活跃状态\",\n                            actualValue: product.status,\n                            suggestedAction: \"考虑清理非活跃产品的库存记录\"\n                        });\n                    }\n                }\n            }\n        } catch (error) {\n            issues.push({\n                type: \"missing_reference\",\n                severity: \"critical\",\n                entityId: \"system\",\n                description: \"检查产品-库存一致性时发生错误: \".concat(error),\n                suggestedAction: \"检查系统配置和网络连接\"\n            });\n        }\n        return {\n            module: \"inventory\",\n            entityType: \"product_inventory\",\n            totalChecked,\n            inconsistentItems: issues.length,\n            issues,\n            // ✅ 使用统一时间戳生成器\n            timestamp: _utils_business__WEBPACK_IMPORTED_MODULE_1__.timestampGenerator.now()\n        };\n    }\n    /**\n   * 检查订单-客户数据一致性\n   */ async checkOrderCustomerConsistency() {\n        const issues = [];\n        let totalChecked = 0;\n        try {\n        // 这里需要访问订单数据，暂时使用模拟数据\n        // 在实际实现中，需要添加订单数据访问接口\n        } catch (error) {}\n        return {\n            module: \"sales\",\n            entityType: \"order_customer\",\n            totalChecked,\n            inconsistentItems: issues.length,\n            issues,\n            // ✅ 使用统一时间戳生成器\n            timestamp: _utils_business__WEBPACK_IMPORTED_MODULE_1__.timestampGenerator.now()\n        };\n    }\n    /**\n   * 检查订单-员工数据一致性\n   */ async checkOrderEmployeeConsistency() {\n        const issues = [];\n        let totalChecked = 0;\n        try {\n        // 这里需要访问订单数据，暂时使用模拟数据\n        // 在实际实现中，需要添加订单数据访问接口\n        } catch (error) {}\n        return {\n            module: \"sales\",\n            entityType: \"order_employee\",\n            totalChecked,\n            inconsistentItems: issues.length,\n            issues,\n            // ✅ 使用统一时间戳生成器\n            timestamp: _utils_business__WEBPACK_IMPORTED_MODULE_1__.timestampGenerator.now()\n        };\n    }\n    /**\n   * 自动修复数据一致性问题\n   */ async autoRepairConsistencyIssues(issues) {\n        let repairedItems = 0;\n        let failedItems = 0;\n        const errors = [];\n        for (const issue of issues){\n            try {\n                switch(issue.type){\n                    case \"data_mismatch\":\n                        if (issue.entityId && issue.expectedValue) {\n                            await this.repairDataMismatch(issue);\n                            repairedItems++;\n                        }\n                        break;\n                    case \"orphaned_record\":\n                        await this.removeOrphanedRecord(issue);\n                        repairedItems++;\n                        break;\n                    default:\n                }\n            } catch (error) {\n                failedItems++;\n                errors.push(\"修复问题 \".concat(issue.entityId, \" 失败: \").concat(error));\n            }\n        }\n        const result = {\n            success: failedItems === 0,\n            repairedItems,\n            failedItems,\n            errors,\n            // ✅ 使用统一时间戳生成器\n            timestamp: _utils_business__WEBPACK_IMPORTED_MODULE_1__.timestampGenerator.now()\n        };\n        return result;\n    }\n    /**\n   * 修复数据不匹配问题\n   */ async repairDataMismatch(issue) {\n        if (issue.description.includes(\"产品名称\")) {\n            // 修复库存记录中的产品名称\n            const inventoryResponse = await _DataAccessManager__WEBPACK_IMPORTED_MODULE_0__.dataAccessManager.inventory.getByProductCode(issue.entityId);\n            if (inventoryResponse.status === \"success\" && inventoryResponse.data) {\n                await _DataAccessManager__WEBPACK_IMPORTED_MODULE_0__.dataAccessManager.inventory.update(issue.entityId, {\n                    productName: issue.expectedValue\n                });\n            }\n        }\n    }\n    /**\n   * 移除孤立记录\n   */ async removeOrphanedRecord(issue) {\n    // 这里需要根据具体的业务逻辑实现孤立记录的移除\n    }\n    /**\n   * 验证特定实体的数据一致性\n   */ async validateEntityConsistency(module, entityType, entityId) {\n        const issues = [];\n        try {\n            switch(\"\".concat(module, \".\").concat(entityType)){\n                case \"inventory.product_inventory\":\n                    const inventoryIssues = await this.validateInventoryItemConsistency(entityId);\n                    issues.push(...inventoryIssues);\n                    break;\n                default:\n            }\n        } catch (error) {\n            issues.push({\n                type: \"missing_reference\",\n                severity: \"critical\",\n                entityId,\n                description: \"验证实体一致性时发生错误: \".concat(error),\n                suggestedAction: \"检查系统配置\"\n            });\n        }\n        return issues;\n    }\n    /**\n   * 验证单个库存项的一致性\n   */ async validateInventoryItemConsistency(inventoryId) {\n        const issues = [];\n        try {\n            // 获取库存记录\n            const inventoryResponse = await _DataAccessManager__WEBPACK_IMPORTED_MODULE_0__.dataAccessManager.inventory.getByProductCode(inventoryId);\n            if (inventoryResponse.status !== \"success\" || !inventoryResponse.data) {\n                issues.push({\n                    type: \"missing_reference\",\n                    severity: \"high\",\n                    entityId: inventoryId,\n                    description: \"库存记录不存在\",\n                    suggestedAction: \"检查库存记录ID是否正确\"\n                });\n                return issues;\n            }\n            const inventory = inventoryResponse.data;\n            // 检查对应的产品\n            const productResponse = await _DataAccessManager__WEBPACK_IMPORTED_MODULE_0__.dataAccessManager.products.getByCode(inventory.productCode);\n            if (productResponse.status !== \"success\" || !productResponse.data) {\n                issues.push({\n                    type: \"missing_reference\",\n                    severity: \"high\",\n                    entityId: inventoryId,\n                    description: \"库存记录引用的产品编码 \".concat(inventory.productCode, \" 不存在\"),\n                    suggestedAction: \"创建对应的产品记录或删除库存记录\"\n                });\n            } else {\n                const product = productResponse.data;\n                // 检查数据一致性\n                if (inventory.productName !== product.modelName) {\n                    issues.push({\n                        type: \"data_mismatch\",\n                        severity: \"medium\",\n                        entityId: inventoryId,\n                        description: \"产品名称不一致\",\n                        expectedValue: product.modelName,\n                        actualValue: inventory.productName,\n                        suggestedAction: \"同步产品名称\"\n                    });\n                }\n            }\n        } catch (error) {}\n        return issues;\n    }\n    constructor(){}\n}\n// 导出默认实例\nconst dataConsistencyService = DataConsistencyService.getInstance();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zZXJ2aWNlcy9kYXRhQWNjZXNzL0RhdGFDb25zaXN0ZW5jeVNlcnZpY2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBOzs7O0NBSUMsR0FFc0Q7QUFDRjtBQXNDckQ7O0NBRUMsR0FDTSxNQUFNRTtJQUtYLE9BQWNDLGNBQXNDO1FBQ2xELElBQUksQ0FBQ0QsdUJBQXVCRSxRQUFRLEVBQUU7WUFDcENGLHVCQUF1QkUsUUFBUSxHQUFHLElBQUlGO1FBQ3hDO1FBQ0EsT0FBT0EsdUJBQXVCRSxRQUFRO0lBQ3hDO0lBRUE7O0dBRUMsR0FDRCxNQUFhQyw4QkFPVjtRQUVELE1BQU1DLFVBQW9DLEVBQUU7UUFFNUMsYUFBYTtRQUNiLE1BQU1DLHdCQUF3QixNQUFNLElBQUksQ0FBQ0MsZ0NBQWdDO1FBQ3pFRixRQUFRRyxJQUFJLENBQUNGO1FBRWIsYUFBYTtRQUNiLE1BQU1HLHFCQUFxQixNQUFNLElBQUksQ0FBQ0MsNkJBQTZCO1FBQ25FTCxRQUFRRyxJQUFJLENBQUNDO1FBRWIsYUFBYTtRQUNiLE1BQU1FLHFCQUFxQixNQUFNLElBQUksQ0FBQ0MsNkJBQTZCO1FBQ25FUCxRQUFRRyxJQUFJLENBQUNHO1FBRWIsU0FBUztRQUNULE1BQU1FLGNBQWNSLFFBQVFTLE1BQU0sQ0FBQyxDQUFDQyxLQUFLQyxTQUFXRCxNQUFNQyxPQUFPQyxpQkFBaUIsRUFBRTtRQUNwRixNQUFNQyxpQkFBaUJiLFFBQVFTLE1BQU0sQ0FBQyxDQUFDQyxLQUFLQyxTQUMxQ0QsTUFBTUMsT0FBT0csTUFBTSxDQUFDQyxNQUFNLENBQUNDLENBQUFBLFFBQVNBLE1BQU1DLFFBQVEsS0FBSyxZQUFZQyxNQUFNLEVBQUU7UUFHN0UsTUFBTUMsVUFBVTtZQUNkWDtZQUNBSztZQUNBLGVBQWU7WUFDZk8sV0FBV3pCLCtEQUFrQkEsQ0FBQzBCLEdBQUc7UUFDbkM7UUFFQSxPQUFPO1lBQUVGO1lBQVNuQjtRQUFRO0lBQzVCO0lBRUE7O0dBRUMsR0FDRCxNQUFjRSxtQ0FBb0U7UUFDaEYsTUFBTVksU0FBNkIsRUFBRTtRQUNyQyxJQUFJUSxlQUFlO1FBRW5CLElBQUk7WUFDRixXQUFXO1lBQ1gsTUFBTUMsb0JBQW9CLE1BQU03QixpRUFBaUJBLENBQUM4QixTQUFTLENBQUNDLE1BQU07WUFFbEUsSUFBSUYsa0JBQWtCRyxNQUFNLEtBQUssYUFBYUgsa0JBQWtCSSxJQUFJLEVBQUU7Z0JBQ3BFLE1BQU1DLGlCQUFpQkwsa0JBQWtCSSxJQUFJLENBQUNFLEtBQUs7Z0JBQ25EUCxlQUFlTSxlQUFlVixNQUFNO2dCQUVwQyxLQUFLLE1BQU1NLGFBQWFJLGVBQWdCO29CQUN0QyxjQUFjO29CQUNkLE1BQU1FLGtCQUFrQixNQUFNcEMsaUVBQWlCQSxDQUFDcUMsUUFBUSxDQUFDQyxTQUFTLENBQUNSLFVBQVVTLFdBQVc7b0JBRXhGLElBQUlILGdCQUFnQkosTUFBTSxLQUFLLGFBQWEsQ0FBQ0ksZ0JBQWdCSCxJQUFJLEVBQUU7d0JBQ2pFYixPQUFPWCxJQUFJLENBQUM7NEJBQ1YrQixNQUFNOzRCQUNOakIsVUFBVTs0QkFDVmtCLFVBQVVYLFVBQVVZLEVBQUU7NEJBQ3RCQyxhQUFhLGVBQXFDLE9BQXRCYixVQUFVUyxXQUFXLEVBQUM7NEJBQ2xESyxpQkFBaUI7d0JBQ25CO3dCQUNBO29CQUNGO29CQUVBLE1BQU1DLFVBQVVULGdCQUFnQkgsSUFBSTtvQkFFcEMsYUFBYTtvQkFDYixJQUFJSCxVQUFVZ0IsV0FBVyxLQUFLRCxRQUFRRSxTQUFTLEVBQUU7d0JBQy9DM0IsT0FBT1gsSUFBSSxDQUFDOzRCQUNWK0IsTUFBTTs0QkFDTmpCLFVBQVU7NEJBQ1ZrQixVQUFVWCxVQUFVWSxFQUFFOzRCQUN0QkMsYUFBYzs0QkFDZEssZUFBZUgsUUFBUUUsU0FBUzs0QkFDaENFLGFBQWFuQixVQUFVZ0IsV0FBVzs0QkFDbENGLGlCQUFpQjt3QkFDbkI7b0JBQ0Y7b0JBRUEsU0FBUztvQkFDVCxJQUFJQyxRQUFRYixNQUFNLEtBQUssVUFBVTt3QkFDL0JaLE9BQU9YLElBQUksQ0FBQzs0QkFDVitCLE1BQU07NEJBQ05qQixVQUFVOzRCQUNWa0IsVUFBVVgsVUFBVVksRUFBRTs0QkFDdEJDLGFBQWM7NEJBQ2RNLGFBQWFKLFFBQVFiLE1BQU07NEJBQzNCWSxpQkFBaUI7d0JBQ25CO29CQUNGO2dCQUNGO1lBQ0Y7UUFDRixFQUFFLE9BQU9NLE9BQU87WUFDZDlCLE9BQU9YLElBQUksQ0FBQztnQkFDVitCLE1BQU07Z0JBQ05qQixVQUFVO2dCQUNWa0IsVUFBVTtnQkFDVkUsYUFBYSxvQkFBMEIsT0FBTk87Z0JBQ2pDTixpQkFBaUI7WUFDbkI7UUFDRjtRQUVBLE9BQU87WUFDTE8sUUFBUTtZQUNSQyxZQUFZO1lBQ1p4QjtZQUNBVixtQkFBbUJFLE9BQU9JLE1BQU07WUFDaENKO1lBQ0EsZUFBZTtZQUNmTSxXQUFXekIsK0RBQWtCQSxDQUFDMEIsR0FBRztRQUNuQztJQUNGO0lBRUE7O0dBRUMsR0FDRCxNQUFjaEIsZ0NBQWlFO1FBQzdFLE1BQU1TLFNBQTZCLEVBQUU7UUFDckMsSUFBSVEsZUFBZTtRQUVuQixJQUFJO1FBQ0Ysc0JBQXNCO1FBQ3RCLHNCQUFzQjtRQUN4QixFQUFFLE9BQU9zQixPQUFPLENBQ2hCO1FBRUEsT0FBTztZQUNMQyxRQUFRO1lBQ1JDLFlBQVk7WUFDWnhCO1lBQ0FWLG1CQUFtQkUsT0FBT0ksTUFBTTtZQUNoQ0o7WUFDQSxlQUFlO1lBQ2ZNLFdBQVd6QiwrREFBa0JBLENBQUMwQixHQUFHO1FBQ25DO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQWNkLGdDQUFpRTtRQUM3RSxNQUFNTyxTQUE2QixFQUFFO1FBQ3JDLElBQUlRLGVBQWU7UUFFbkIsSUFBSTtRQUNGLHNCQUFzQjtRQUN0QixzQkFBc0I7UUFDeEIsRUFBRSxPQUFPc0IsT0FBTyxDQUNoQjtRQUVBLE9BQU87WUFDTEMsUUFBUTtZQUNSQyxZQUFZO1lBQ1p4QjtZQUNBVixtQkFBbUJFLE9BQU9JLE1BQU07WUFDaENKO1lBQ0EsZUFBZTtZQUNmTSxXQUFXekIsK0RBQWtCQSxDQUFDMEIsR0FBRztRQUNuQztJQUNGO0lBRUE7O0dBRUMsR0FDRCxNQUFhMEIsNEJBQTRCakMsTUFBMEIsRUFBeUI7UUFFMUYsSUFBSWtDLGdCQUFnQjtRQUNwQixJQUFJQyxjQUFjO1FBQ2xCLE1BQU1DLFNBQW1CLEVBQUU7UUFFM0IsS0FBSyxNQUFNbEMsU0FBU0YsT0FBUTtZQUMxQixJQUFJO2dCQUNGLE9BQVFFLE1BQU1rQixJQUFJO29CQUNoQixLQUFLO3dCQUNILElBQUlsQixNQUFNbUIsUUFBUSxJQUFJbkIsTUFBTTBCLGFBQWEsRUFBRTs0QkFDekMsTUFBTSxJQUFJLENBQUNTLGtCQUFrQixDQUFDbkM7NEJBQzlCZ0M7d0JBQ0Y7d0JBQ0E7b0JBRUYsS0FBSzt3QkFDSCxNQUFNLElBQUksQ0FBQ0ksb0JBQW9CLENBQUNwQzt3QkFDaENnQzt3QkFDQTtvQkFFRjtnQkFDRjtZQUNGLEVBQUUsT0FBT0osT0FBTztnQkFDZEs7Z0JBQ0FDLE9BQU8vQyxJQUFJLENBQUMsUUFBOEJ5QyxPQUF0QjVCLE1BQU1tQixRQUFRLEVBQUMsU0FBYSxPQUFOUztZQUM1QztRQUNGO1FBRUEsTUFBTWpDLFNBQXVCO1lBQzNCMEMsU0FBU0osZ0JBQWdCO1lBQ3pCRDtZQUNBQztZQUNBQztZQUNBLGVBQWU7WUFDZjlCLFdBQVd6QiwrREFBa0JBLENBQUMwQixHQUFHO1FBQ25DO1FBRUEsT0FBT1Y7SUFDVDtJQUVBOztHQUVDLEdBQ0QsTUFBY3dDLG1CQUFtQm5DLEtBQXVCLEVBQWlCO1FBQ3ZFLElBQUlBLE1BQU1xQixXQUFXLENBQUNpQixRQUFRLENBQUMsU0FBUztZQUN0QyxlQUFlO1lBQ2YsTUFBTS9CLG9CQUFvQixNQUFNN0IsaUVBQWlCQSxDQUFDOEIsU0FBUyxDQUFDK0IsZ0JBQWdCLENBQUN2QyxNQUFNbUIsUUFBUTtZQUUzRixJQUFJWixrQkFBa0JHLE1BQU0sS0FBSyxhQUFhSCxrQkFBa0JJLElBQUksRUFBRTtnQkFDcEUsTUFBTWpDLGlFQUFpQkEsQ0FBQzhCLFNBQVMsQ0FBQ2dDLE1BQU0sQ0FBQ3hDLE1BQU1tQixRQUFRLEVBQUU7b0JBQ3ZESyxhQUFheEIsTUFBTTBCLGFBQWE7Z0JBQ2xDO1lBQ0Y7UUFDRjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxNQUFjVSxxQkFBcUJwQyxLQUF1QixFQUFpQjtJQUN6RSx5QkFBeUI7SUFDM0I7SUFFQTs7R0FFQyxHQUNELE1BQWF5QywwQkFDWFosTUFBYyxFQUNkQyxVQUFrQixFQUNsQlgsUUFBZ0IsRUFDYTtRQUM3QixNQUFNckIsU0FBNkIsRUFBRTtRQUVyQyxJQUFJO1lBQ0YsT0FBUSxHQUFhZ0MsT0FBVkQsUUFBTyxLQUFjLE9BQVhDO2dCQUNuQixLQUFLO29CQUNILE1BQU1ZLGtCQUFrQixNQUFNLElBQUksQ0FBQ0MsZ0NBQWdDLENBQUN4QjtvQkFDcEVyQixPQUFPWCxJQUFJLElBQUl1RDtvQkFDZjtnQkFFRjtZQUNGO1FBQ0YsRUFBRSxPQUFPZCxPQUFPO1lBQ2Q5QixPQUFPWCxJQUFJLENBQUM7Z0JBQ1YrQixNQUFNO2dCQUNOakIsVUFBVTtnQkFDVmtCO2dCQUNBRSxhQUFhLGlCQUF1QixPQUFOTztnQkFDOUJOLGlCQUFpQjtZQUNuQjtRQUNGO1FBRUEsT0FBT3hCO0lBQ1Q7SUFFQTs7R0FFQyxHQUNELE1BQWM2QyxpQ0FBaUNDLFdBQW1CLEVBQStCO1FBQy9GLE1BQU05QyxTQUE2QixFQUFFO1FBRXJDLElBQUk7WUFDRixTQUFTO1lBQ1QsTUFBTVMsb0JBQW9CLE1BQU03QixpRUFBaUJBLENBQUM4QixTQUFTLENBQUMrQixnQkFBZ0IsQ0FBQ0s7WUFFN0UsSUFBSXJDLGtCQUFrQkcsTUFBTSxLQUFLLGFBQWEsQ0FBQ0gsa0JBQWtCSSxJQUFJLEVBQUU7Z0JBQ3JFYixPQUFPWCxJQUFJLENBQUM7b0JBQ1YrQixNQUFNO29CQUNOakIsVUFBVTtvQkFDVmtCLFVBQVV5QjtvQkFDVnZCLGFBQWE7b0JBQ2JDLGlCQUFpQjtnQkFDbkI7Z0JBQ0EsT0FBT3hCO1lBQ1Q7WUFFQSxNQUFNVSxZQUFZRCxrQkFBa0JJLElBQUk7WUFFeEMsVUFBVTtZQUNWLE1BQU1HLGtCQUFrQixNQUFNcEMsaUVBQWlCQSxDQUFDcUMsUUFBUSxDQUFDQyxTQUFTLENBQUNSLFVBQVVTLFdBQVc7WUFFeEYsSUFBSUgsZ0JBQWdCSixNQUFNLEtBQUssYUFBYSxDQUFDSSxnQkFBZ0JILElBQUksRUFBRTtnQkFDakViLE9BQU9YLElBQUksQ0FBQztvQkFDVitCLE1BQU07b0JBQ05qQixVQUFVO29CQUNWa0IsVUFBVXlCO29CQUNWdkIsYUFBYSxlQUFxQyxPQUF0QmIsVUFBVVMsV0FBVyxFQUFDO29CQUNsREssaUJBQWlCO2dCQUNuQjtZQUNGLE9BQU87Z0JBQ0wsTUFBTUMsVUFBVVQsZ0JBQWdCSCxJQUFJO2dCQUVwQyxVQUFVO2dCQUNWLElBQUlILFVBQVVnQixXQUFXLEtBQUtELFFBQVFFLFNBQVMsRUFBRTtvQkFDL0MzQixPQUFPWCxJQUFJLENBQUM7d0JBQ1YrQixNQUFNO3dCQUNOakIsVUFBVTt3QkFDVmtCLFVBQVV5Qjt3QkFDVnZCLGFBQWE7d0JBQ2JLLGVBQWVILFFBQVFFLFNBQVM7d0JBQ2hDRSxhQUFhbkIsVUFBVWdCLFdBQVc7d0JBQ2xDRixpQkFBaUI7b0JBQ25CO2dCQUNGO1lBQ0Y7UUFDRixFQUFFLE9BQU9NLE9BQU8sQ0FDaEI7UUFFQSxPQUFPOUI7SUFDVDtJQTNVQSxhQUFzQixDQUFDO0FBNFV6QjtBQUVBLFNBQVM7QUFDRixNQUFNK0MseUJBQXlCakUsdUJBQXVCQyxXQUFXLEdBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL3NlcnZpY2VzL2RhdGFBY2Nlc3MvRGF0YUNvbnNpc3RlbmN5U2VydmljZS50cz9lNzkwIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICog5pWw5o2u5LiA6Ie05oCn6aqM6K+B5pyN5YqhXG4gKiDmo4Dmn6Xlkoznu7TmiqTot6jmqKHlnZfmlbDmja7nmoTkuIDoh7TmgKdcbiAqIOespuWQiFwi5qih5Z2X6Ze055qE5o6l5Y+j5ZKM5pWw5o2u5Lqk5LqS5qCH5YeGXCLmlofku7bopoHmsYJcbiAqL1xuXG5pbXBvcnQgeyBkYXRhQWNjZXNzTWFuYWdlciB9IGZyb20gJy4vRGF0YUFjY2Vzc01hbmFnZXInXG5pbXBvcnQgeyB0aW1lc3RhbXBHZW5lcmF0b3IgfSBmcm9tICdAL3V0aWxzL2J1c2luZXNzJ1xuXG4vKipcbiAqIOS4gOiHtOaAp+ajgOafpee7k+aenOaOpeWPo1xuICovXG5leHBvcnQgaW50ZXJmYWNlIENvbnNpc3RlbmN5Q2hlY2tSZXN1bHQge1xuICBtb2R1bGU6IHN0cmluZ1xuICBlbnRpdHlUeXBlOiBzdHJpbmdcbiAgdG90YWxDaGVja2VkOiBudW1iZXJcbiAgaW5jb25zaXN0ZW50SXRlbXM6IG51bWJlclxuICBpc3N1ZXM6IENvbnNpc3RlbmN5SXNzdWVbXVxuICB0aW1lc3RhbXA6IHN0cmluZ1xufVxuXG4vKipcbiAqIOS4gOiHtOaAp+mXrumimOaOpeWPo1xuICovXG5leHBvcnQgaW50ZXJmYWNlIENvbnNpc3RlbmN5SXNzdWUge1xuICB0eXBlOiAnbWlzc2luZ19yZWZlcmVuY2UnIHwgJ2RhdGFfbWlzbWF0Y2gnIHwgJ29ycGhhbmVkX3JlY29yZCcgfCAnaW52YWxpZF9zdGF0dXMnXG4gIHNldmVyaXR5OiAnbG93JyB8ICdtZWRpdW0nIHwgJ2hpZ2gnIHwgJ2NyaXRpY2FsJ1xuICBlbnRpdHlJZDogc3RyaW5nXG4gIGRlc2NyaXB0aW9uOiBzdHJpbmdcbiAgZXhwZWN0ZWRWYWx1ZT86IGFueVxuICBhY3R1YWxWYWx1ZT86IGFueVxuICBzdWdnZXN0ZWRBY3Rpb246IHN0cmluZ1xufVxuXG4vKipcbiAqIOS/ruWkjee7k+aenOaOpeWPo1xuICovXG5leHBvcnQgaW50ZXJmYWNlIFJlcGFpclJlc3VsdCB7XG4gIHN1Y2Nlc3M6IGJvb2xlYW5cbiAgcmVwYWlyZWRJdGVtczogbnVtYmVyXG4gIGZhaWxlZEl0ZW1zOiBudW1iZXJcbiAgZXJyb3JzOiBzdHJpbmdbXVxuICB0aW1lc3RhbXA6IHN0cmluZ1xufVxuXG4vKipcbiAqIOaVsOaNruS4gOiHtOaAp+mqjOivgeacjeWKoVxuICovXG5leHBvcnQgY2xhc3MgRGF0YUNvbnNpc3RlbmN5U2VydmljZSB7XG4gIHByaXZhdGUgc3RhdGljIGluc3RhbmNlOiBEYXRhQ29uc2lzdGVuY3lTZXJ2aWNlXG5cbiAgcHJpdmF0ZSBjb25zdHJ1Y3RvcigpIHt9XG5cbiAgcHVibGljIHN0YXRpYyBnZXRJbnN0YW5jZSgpOiBEYXRhQ29uc2lzdGVuY3lTZXJ2aWNlIHtcbiAgICBpZiAoIURhdGFDb25zaXN0ZW5jeVNlcnZpY2UuaW5zdGFuY2UpIHtcbiAgICAgIERhdGFDb25zaXN0ZW5jeVNlcnZpY2UuaW5zdGFuY2UgPSBuZXcgRGF0YUNvbnNpc3RlbmN5U2VydmljZSgpXG4gICAgfVxuICAgIHJldHVybiBEYXRhQ29uc2lzdGVuY3lTZXJ2aWNlLmluc3RhbmNlXG4gIH1cblxuICAvKipcbiAgICog5omn6KGM5YWo6Z2i55qE5pWw5o2u5LiA6Ie05oCn5qOA5p+lXG4gICAqL1xuICBwdWJsaWMgYXN5bmMgcGVyZm9ybUZ1bGxDb25zaXN0ZW5jeUNoZWNrKCk6IFByb21pc2U8e1xuICAgIG92ZXJhbGw6IHtcbiAgICAgIHRvdGFsSXNzdWVzOiBudW1iZXJcbiAgICAgIGNyaXRpY2FsSXNzdWVzOiBudW1iZXJcbiAgICAgIHRpbWVzdGFtcDogc3RyaW5nXG4gICAgfVxuICAgIHJlc3VsdHM6IENvbnNpc3RlbmN5Q2hlY2tSZXN1bHRbXVxuICB9PiB7XG5cbiAgICBjb25zdCByZXN1bHRzOiBDb25zaXN0ZW5jeUNoZWNrUmVzdWx0W10gPSBbXVxuICAgIFxuICAgIC8vIOajgOafpeS6p+WTgS3lupPlrZjkuIDoh7TmgKdcbiAgICBjb25zdCBwcm9kdWN0SW52ZW50b3J5Q2hlY2sgPSBhd2FpdCB0aGlzLmNoZWNrUHJvZHVjdEludmVudG9yeUNvbnNpc3RlbmN5KClcbiAgICByZXN1bHRzLnB1c2gocHJvZHVjdEludmVudG9yeUNoZWNrKVxuXG4gICAgLy8g5qOA5p+l6K6i5Y2VLeWuouaIt+S4gOiHtOaAp1xuICAgIGNvbnN0IG9yZGVyQ3VzdG9tZXJDaGVjayA9IGF3YWl0IHRoaXMuY2hlY2tPcmRlckN1c3RvbWVyQ29uc2lzdGVuY3koKVxuICAgIHJlc3VsdHMucHVzaChvcmRlckN1c3RvbWVyQ2hlY2spXG5cbiAgICAvLyDmo4Dmn6XorqLljZUt5ZGY5bel5LiA6Ie05oCnXG4gICAgY29uc3Qgb3JkZXJFbXBsb3llZUNoZWNrID0gYXdhaXQgdGhpcy5jaGVja09yZGVyRW1wbG95ZWVDb25zaXN0ZW5jeSgpXG4gICAgcmVzdWx0cy5wdXNoKG9yZGVyRW1wbG95ZWVDaGVjaylcblxuICAgIC8vIOiuoeeul+aAu+S9k+e7n+iuoVxuICAgIGNvbnN0IHRvdGFsSXNzdWVzID0gcmVzdWx0cy5yZWR1Y2UoKHN1bSwgcmVzdWx0KSA9PiBzdW0gKyByZXN1bHQuaW5jb25zaXN0ZW50SXRlbXMsIDApXG4gICAgY29uc3QgY3JpdGljYWxJc3N1ZXMgPSByZXN1bHRzLnJlZHVjZSgoc3VtLCByZXN1bHQpID0+IFxuICAgICAgc3VtICsgcmVzdWx0Lmlzc3Vlcy5maWx0ZXIoaXNzdWUgPT4gaXNzdWUuc2V2ZXJpdHkgPT09ICdjcml0aWNhbCcpLmxlbmd0aCwgMFxuICAgIClcblxuICAgIGNvbnN0IG92ZXJhbGwgPSB7XG4gICAgICB0b3RhbElzc3VlcyxcbiAgICAgIGNyaXRpY2FsSXNzdWVzLFxuICAgICAgLy8g4pyFIOS9v+eUqOe7n+S4gOaXtumXtOaIs+eUn+aIkOWZqFxuICAgICAgdGltZXN0YW1wOiB0aW1lc3RhbXBHZW5lcmF0b3Iubm93KClcbiAgICB9XG5cbiAgICByZXR1cm4geyBvdmVyYWxsLCByZXN1bHRzIH1cbiAgfVxuXG4gIC8qKlxuICAgKiDmo4Dmn6Xkuqflk4Et5bqT5a2Y5pWw5o2u5LiA6Ie05oCnXG4gICAqL1xuICBwcml2YXRlIGFzeW5jIGNoZWNrUHJvZHVjdEludmVudG9yeUNvbnNpc3RlbmN5KCk6IFByb21pc2U8Q29uc2lzdGVuY3lDaGVja1Jlc3VsdD4ge1xuICAgIGNvbnN0IGlzc3VlczogQ29uc2lzdGVuY3lJc3N1ZVtdID0gW11cbiAgICBsZXQgdG90YWxDaGVja2VkID0gMFxuXG4gICAgdHJ5IHtcbiAgICAgIC8vIOiOt+WPluaJgOacieW6k+WtmOiusOW9lVxuICAgICAgY29uc3QgaW52ZW50b3J5UmVzcG9uc2UgPSBhd2FpdCBkYXRhQWNjZXNzTWFuYWdlci5pbnZlbnRvcnkuZ2V0QWxsKClcbiAgICAgIFxuICAgICAgaWYgKGludmVudG9yeVJlc3BvbnNlLnN0YXR1cyA9PT0gJ3N1Y2Nlc3MnICYmIGludmVudG9yeVJlc3BvbnNlLmRhdGEpIHtcbiAgICAgICAgY29uc3QgaW52ZW50b3J5SXRlbXMgPSBpbnZlbnRvcnlSZXNwb25zZS5kYXRhLml0ZW1zXG4gICAgICAgIHRvdGFsQ2hlY2tlZCA9IGludmVudG9yeUl0ZW1zLmxlbmd0aFxuXG4gICAgICAgIGZvciAoY29uc3QgaW52ZW50b3J5IG9mIGludmVudG9yeUl0ZW1zKSB7XG4gICAgICAgICAgLy8g5qOA5p+l5a+55bqU55qE5Lqn5ZOB5piv5ZCm5a2Y5ZyoXG4gICAgICAgICAgY29uc3QgcHJvZHVjdFJlc3BvbnNlID0gYXdhaXQgZGF0YUFjY2Vzc01hbmFnZXIucHJvZHVjdHMuZ2V0QnlDb2RlKGludmVudG9yeS5wcm9kdWN0Q29kZSlcbiAgICAgICAgICBcbiAgICAgICAgICBpZiAocHJvZHVjdFJlc3BvbnNlLnN0YXR1cyAhPT0gJ3N1Y2Nlc3MnIHx8ICFwcm9kdWN0UmVzcG9uc2UuZGF0YSkge1xuICAgICAgICAgICAgaXNzdWVzLnB1c2goe1xuICAgICAgICAgICAgICB0eXBlOiAnbWlzc2luZ19yZWZlcmVuY2UnLFxuICAgICAgICAgICAgICBzZXZlcml0eTogJ2hpZ2gnLFxuICAgICAgICAgICAgICBlbnRpdHlJZDogaW52ZW50b3J5LmlkLFxuICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogYOW6k+WtmOiusOW9leW8leeUqOeahOS6p+WTgee8lueggSAke2ludmVudG9yeS5wcm9kdWN0Q29kZX0g5LiN5a2Y5ZyoYCxcbiAgICAgICAgICAgICAgc3VnZ2VzdGVkQWN0aW9uOiAn5Yig6Zmk5a2k56uL55qE5bqT5a2Y6K6w5b2V5oiW5Yib5bu65a+55bqU55qE5Lqn5ZOB6K6w5b2VJ1xuICAgICAgICAgICAgfSlcbiAgICAgICAgICAgIGNvbnRpbnVlXG4gICAgICAgICAgfVxuXG4gICAgICAgICAgY29uc3QgcHJvZHVjdCA9IHByb2R1Y3RSZXNwb25zZS5kYXRhXG5cbiAgICAgICAgICAvLyDmo4Dmn6Xkuqflk4HlkI3np7DmmK/lkKbkuIDoh7RcbiAgICAgICAgICBpZiAoaW52ZW50b3J5LnByb2R1Y3ROYW1lICE9PSBwcm9kdWN0Lm1vZGVsTmFtZSkge1xuICAgICAgICAgICAgaXNzdWVzLnB1c2goe1xuICAgICAgICAgICAgICB0eXBlOiAnZGF0YV9taXNtYXRjaCcsXG4gICAgICAgICAgICAgIHNldmVyaXR5OiAnbWVkaXVtJyxcbiAgICAgICAgICAgICAgZW50aXR5SWQ6IGludmVudG9yeS5pZCxcbiAgICAgICAgICAgICAgZGVzY3JpcHRpb246IGDlupPlrZjorrDlvZXkuK3nmoTkuqflk4HlkI3np7DkuI7kuqflk4HmlbDmja7kuI3kuIDoh7RgLFxuICAgICAgICAgICAgICBleHBlY3RlZFZhbHVlOiBwcm9kdWN0Lm1vZGVsTmFtZSxcbiAgICAgICAgICAgICAgYWN0dWFsVmFsdWU6IGludmVudG9yeS5wcm9kdWN0TmFtZSxcbiAgICAgICAgICAgICAgc3VnZ2VzdGVkQWN0aW9uOiAn5ZCM5q2l5Lqn5ZOB5ZCN56ew5Yiw5bqT5a2Y6K6w5b2VJ1xuICAgICAgICAgICAgfSlcbiAgICAgICAgICB9XG5cbiAgICAgICAgICAvLyDmo4Dmn6Xkuqflk4HnirbmgIFcbiAgICAgICAgICBpZiAocHJvZHVjdC5zdGF0dXMgIT09ICdhY3RpdmUnKSB7XG4gICAgICAgICAgICBpc3N1ZXMucHVzaCh7XG4gICAgICAgICAgICAgIHR5cGU6ICdpbnZhbGlkX3N0YXR1cycsXG4gICAgICAgICAgICAgIHNldmVyaXR5OiAnbG93JyxcbiAgICAgICAgICAgICAgZW50aXR5SWQ6IGludmVudG9yeS5pZCxcbiAgICAgICAgICAgICAgZGVzY3JpcHRpb246IGDlupPlrZjorrDlvZXlr7nlupTnmoTkuqflk4HnirbmgIHkuLrpnZ7mtLvot4PnirbmgIFgLFxuICAgICAgICAgICAgICBhY3R1YWxWYWx1ZTogcHJvZHVjdC5zdGF0dXMsXG4gICAgICAgICAgICAgIHN1Z2dlc3RlZEFjdGlvbjogJ+iAg+iZkea4heeQhumdnua0u+i3g+S6p+WTgeeahOW6k+WtmOiusOW9lSdcbiAgICAgICAgICAgIH0pXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGlzc3Vlcy5wdXNoKHtcbiAgICAgICAgdHlwZTogJ21pc3NpbmdfcmVmZXJlbmNlJyxcbiAgICAgICAgc2V2ZXJpdHk6ICdjcml0aWNhbCcsXG4gICAgICAgIGVudGl0eUlkOiAnc3lzdGVtJyxcbiAgICAgICAgZGVzY3JpcHRpb246IGDmo4Dmn6Xkuqflk4Et5bqT5a2Y5LiA6Ie05oCn5pe25Y+R55Sf6ZSZ6K+vOiAke2Vycm9yfWAsXG4gICAgICAgIHN1Z2dlc3RlZEFjdGlvbjogJ+ajgOafpeezu+e7n+mFjee9ruWSjOe9kee7nOi/nuaOpSdcbiAgICAgIH0pXG4gICAgfVxuXG4gICAgcmV0dXJuIHtcbiAgICAgIG1vZHVsZTogJ2ludmVudG9yeScsXG4gICAgICBlbnRpdHlUeXBlOiAncHJvZHVjdF9pbnZlbnRvcnknLFxuICAgICAgdG90YWxDaGVja2VkLFxuICAgICAgaW5jb25zaXN0ZW50SXRlbXM6IGlzc3Vlcy5sZW5ndGgsXG4gICAgICBpc3N1ZXMsXG4gICAgICAvLyDinIUg5L2/55So57uf5LiA5pe26Ze05oiz55Sf5oiQ5ZmoXG4gICAgICB0aW1lc3RhbXA6IHRpbWVzdGFtcEdlbmVyYXRvci5ub3coKVxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiDmo4Dmn6XorqLljZUt5a6i5oi35pWw5o2u5LiA6Ie05oCnXG4gICAqL1xuICBwcml2YXRlIGFzeW5jIGNoZWNrT3JkZXJDdXN0b21lckNvbnNpc3RlbmN5KCk6IFByb21pc2U8Q29uc2lzdGVuY3lDaGVja1Jlc3VsdD4ge1xuICAgIGNvbnN0IGlzc3VlczogQ29uc2lzdGVuY3lJc3N1ZVtdID0gW11cbiAgICBsZXQgdG90YWxDaGVja2VkID0gMFxuXG4gICAgdHJ5IHtcbiAgICAgIC8vIOi/memHjOmcgOimgeiuv+mXruiuouWNleaVsOaNru+8jOaaguaXtuS9v+eUqOaooeaLn+aVsOaNrlxuICAgICAgLy8g5Zyo5a6e6ZmF5a6e546w5Lit77yM6ZyA6KaB5re75Yqg6K6i5Y2V5pWw5o2u6K6/6Zeu5o6l5Y+jXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICB9XG5cbiAgICByZXR1cm4ge1xuICAgICAgbW9kdWxlOiAnc2FsZXMnLFxuICAgICAgZW50aXR5VHlwZTogJ29yZGVyX2N1c3RvbWVyJyxcbiAgICAgIHRvdGFsQ2hlY2tlZCxcbiAgICAgIGluY29uc2lzdGVudEl0ZW1zOiBpc3N1ZXMubGVuZ3RoLFxuICAgICAgaXNzdWVzLFxuICAgICAgLy8g4pyFIOS9v+eUqOe7n+S4gOaXtumXtOaIs+eUn+aIkOWZqFxuICAgICAgdGltZXN0YW1wOiB0aW1lc3RhbXBHZW5lcmF0b3Iubm93KClcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICog5qOA5p+l6K6i5Y2VLeWRmOW3peaVsOaNruS4gOiHtOaAp1xuICAgKi9cbiAgcHJpdmF0ZSBhc3luYyBjaGVja09yZGVyRW1wbG95ZWVDb25zaXN0ZW5jeSgpOiBQcm9taXNlPENvbnNpc3RlbmN5Q2hlY2tSZXN1bHQ+IHtcbiAgICBjb25zdCBpc3N1ZXM6IENvbnNpc3RlbmN5SXNzdWVbXSA9IFtdXG4gICAgbGV0IHRvdGFsQ2hlY2tlZCA9IDBcblxuICAgIHRyeSB7XG4gICAgICAvLyDov5nph4zpnIDopoHorr/pl67orqLljZXmlbDmja7vvIzmmoLml7bkvb/nlKjmqKHmi5/mlbDmja5cbiAgICAgIC8vIOWcqOWunumZheWunueOsOS4re+8jOmcgOimgea3u+WKoOiuouWNleaVsOaNruiuv+mXruaOpeWPo1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgfVxuXG4gICAgcmV0dXJuIHtcbiAgICAgIG1vZHVsZTogJ3NhbGVzJyxcbiAgICAgIGVudGl0eVR5cGU6ICdvcmRlcl9lbXBsb3llZScsXG4gICAgICB0b3RhbENoZWNrZWQsXG4gICAgICBpbmNvbnNpc3RlbnRJdGVtczogaXNzdWVzLmxlbmd0aCxcbiAgICAgIGlzc3VlcyxcbiAgICAgIC8vIOKchSDkvb/nlKjnu5/kuIDml7bpl7TmiLPnlJ/miJDlmahcbiAgICAgIHRpbWVzdGFtcDogdGltZXN0YW1wR2VuZXJhdG9yLm5vdygpXG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIOiHquWKqOS/ruWkjeaVsOaNruS4gOiHtOaAp+mXrumimFxuICAgKi9cbiAgcHVibGljIGFzeW5jIGF1dG9SZXBhaXJDb25zaXN0ZW5jeUlzc3Vlcyhpc3N1ZXM6IENvbnNpc3RlbmN5SXNzdWVbXSk6IFByb21pc2U8UmVwYWlyUmVzdWx0PiB7XG5cbiAgICBsZXQgcmVwYWlyZWRJdGVtcyA9IDBcbiAgICBsZXQgZmFpbGVkSXRlbXMgPSAwXG4gICAgY29uc3QgZXJyb3JzOiBzdHJpbmdbXSA9IFtdXG5cbiAgICBmb3IgKGNvbnN0IGlzc3VlIG9mIGlzc3Vlcykge1xuICAgICAgdHJ5IHtcbiAgICAgICAgc3dpdGNoIChpc3N1ZS50eXBlKSB7XG4gICAgICAgICAgY2FzZSAnZGF0YV9taXNtYXRjaCc6XG4gICAgICAgICAgICBpZiAoaXNzdWUuZW50aXR5SWQgJiYgaXNzdWUuZXhwZWN0ZWRWYWx1ZSkge1xuICAgICAgICAgICAgICBhd2FpdCB0aGlzLnJlcGFpckRhdGFNaXNtYXRjaChpc3N1ZSlcbiAgICAgICAgICAgICAgcmVwYWlyZWRJdGVtcysrXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBicmVha1xuICAgICAgICAgIFxuICAgICAgICAgIGNhc2UgJ29ycGhhbmVkX3JlY29yZCc6XG4gICAgICAgICAgICBhd2FpdCB0aGlzLnJlbW92ZU9ycGhhbmVkUmVjb3JkKGlzc3VlKVxuICAgICAgICAgICAgcmVwYWlyZWRJdGVtcysrXG4gICAgICAgICAgICBicmVha1xuICAgICAgICAgIFxuICAgICAgICAgIGRlZmF1bHQ6XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGZhaWxlZEl0ZW1zKytcbiAgICAgICAgZXJyb3JzLnB1c2goYOS/ruWkjemXrumimCAke2lzc3VlLmVudGl0eUlkfSDlpLHotKU6ICR7ZXJyb3J9YClcbiAgICAgIH1cbiAgICB9XG5cbiAgICBjb25zdCByZXN1bHQ6IFJlcGFpclJlc3VsdCA9IHtcbiAgICAgIHN1Y2Nlc3M6IGZhaWxlZEl0ZW1zID09PSAwLFxuICAgICAgcmVwYWlyZWRJdGVtcyxcbiAgICAgIGZhaWxlZEl0ZW1zLFxuICAgICAgZXJyb3JzLFxuICAgICAgLy8g4pyFIOS9v+eUqOe7n+S4gOaXtumXtOaIs+eUn+aIkOWZqFxuICAgICAgdGltZXN0YW1wOiB0aW1lc3RhbXBHZW5lcmF0b3Iubm93KClcbiAgICB9XG5cbiAgICByZXR1cm4gcmVzdWx0XG4gIH1cblxuICAvKipcbiAgICog5L+u5aSN5pWw5o2u5LiN5Yy56YWN6Zeu6aKYXG4gICAqL1xuICBwcml2YXRlIGFzeW5jIHJlcGFpckRhdGFNaXNtYXRjaChpc3N1ZTogQ29uc2lzdGVuY3lJc3N1ZSk6IFByb21pc2U8dm9pZD4ge1xuICAgIGlmIChpc3N1ZS5kZXNjcmlwdGlvbi5pbmNsdWRlcygn5Lqn5ZOB5ZCN56ewJykpIHtcbiAgICAgIC8vIOS/ruWkjeW6k+WtmOiusOW9leS4reeahOS6p+WTgeWQjeensFxuICAgICAgY29uc3QgaW52ZW50b3J5UmVzcG9uc2UgPSBhd2FpdCBkYXRhQWNjZXNzTWFuYWdlci5pbnZlbnRvcnkuZ2V0QnlQcm9kdWN0Q29kZShpc3N1ZS5lbnRpdHlJZClcbiAgICAgIFxuICAgICAgaWYgKGludmVudG9yeVJlc3BvbnNlLnN0YXR1cyA9PT0gJ3N1Y2Nlc3MnICYmIGludmVudG9yeVJlc3BvbnNlLmRhdGEpIHtcbiAgICAgICAgYXdhaXQgZGF0YUFjY2Vzc01hbmFnZXIuaW52ZW50b3J5LnVwZGF0ZShpc3N1ZS5lbnRpdHlJZCwge1xuICAgICAgICAgIHByb2R1Y3ROYW1lOiBpc3N1ZS5leHBlY3RlZFZhbHVlXG4gICAgICAgIH0pXG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIOenu+mZpOWtpOeri+iusOW9lVxuICAgKi9cbiAgcHJpdmF0ZSBhc3luYyByZW1vdmVPcnBoYW5lZFJlY29yZChpc3N1ZTogQ29uc2lzdGVuY3lJc3N1ZSk6IFByb21pc2U8dm9pZD4ge1xuICAgIC8vIOi/memHjOmcgOimgeagueaNruWFt+S9k+eahOS4muWKoemAu+i+keWunueOsOWtpOeri+iusOW9leeahOenu+mZpFxuICB9XG5cbiAgLyoqXG4gICAqIOmqjOivgeeJueWumuWunuS9k+eahOaVsOaNruS4gOiHtOaAp1xuICAgKi9cbiAgcHVibGljIGFzeW5jIHZhbGlkYXRlRW50aXR5Q29uc2lzdGVuY3koXG4gICAgbW9kdWxlOiBzdHJpbmcsIFxuICAgIGVudGl0eVR5cGU6IHN0cmluZywgXG4gICAgZW50aXR5SWQ6IHN0cmluZ1xuICApOiBQcm9taXNlPENvbnNpc3RlbmN5SXNzdWVbXT4ge1xuICAgIGNvbnN0IGlzc3VlczogQ29uc2lzdGVuY3lJc3N1ZVtdID0gW11cblxuICAgIHRyeSB7XG4gICAgICBzd2l0Y2ggKGAke21vZHVsZX0uJHtlbnRpdHlUeXBlfWApIHtcbiAgICAgICAgY2FzZSAnaW52ZW50b3J5LnByb2R1Y3RfaW52ZW50b3J5JzpcbiAgICAgICAgICBjb25zdCBpbnZlbnRvcnlJc3N1ZXMgPSBhd2FpdCB0aGlzLnZhbGlkYXRlSW52ZW50b3J5SXRlbUNvbnNpc3RlbmN5KGVudGl0eUlkKVxuICAgICAgICAgIGlzc3Vlcy5wdXNoKC4uLmludmVudG9yeUlzc3VlcylcbiAgICAgICAgICBicmVha1xuICAgICAgICBcbiAgICAgICAgZGVmYXVsdDpcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgaXNzdWVzLnB1c2goe1xuICAgICAgICB0eXBlOiAnbWlzc2luZ19yZWZlcmVuY2UnLFxuICAgICAgICBzZXZlcml0eTogJ2NyaXRpY2FsJyxcbiAgICAgICAgZW50aXR5SWQsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBg6aqM6K+B5a6e5L2T5LiA6Ie05oCn5pe25Y+R55Sf6ZSZ6K+vOiAke2Vycm9yfWAsXG4gICAgICAgIHN1Z2dlc3RlZEFjdGlvbjogJ+ajgOafpeezu+e7n+mFjee9ridcbiAgICAgIH0pXG4gICAgfVxuXG4gICAgcmV0dXJuIGlzc3Vlc1xuICB9XG5cbiAgLyoqXG4gICAqIOmqjOivgeWNleS4quW6k+WtmOmhueeahOS4gOiHtOaAp1xuICAgKi9cbiAgcHJpdmF0ZSBhc3luYyB2YWxpZGF0ZUludmVudG9yeUl0ZW1Db25zaXN0ZW5jeShpbnZlbnRvcnlJZDogc3RyaW5nKTogUHJvbWlzZTxDb25zaXN0ZW5jeUlzc3VlW10+IHtcbiAgICBjb25zdCBpc3N1ZXM6IENvbnNpc3RlbmN5SXNzdWVbXSA9IFtdXG5cbiAgICB0cnkge1xuICAgICAgLy8g6I635Y+W5bqT5a2Y6K6w5b2VXG4gICAgICBjb25zdCBpbnZlbnRvcnlSZXNwb25zZSA9IGF3YWl0IGRhdGFBY2Nlc3NNYW5hZ2VyLmludmVudG9yeS5nZXRCeVByb2R1Y3RDb2RlKGludmVudG9yeUlkKVxuICAgICAgXG4gICAgICBpZiAoaW52ZW50b3J5UmVzcG9uc2Uuc3RhdHVzICE9PSAnc3VjY2VzcycgfHwgIWludmVudG9yeVJlc3BvbnNlLmRhdGEpIHtcbiAgICAgICAgaXNzdWVzLnB1c2goe1xuICAgICAgICAgIHR5cGU6ICdtaXNzaW5nX3JlZmVyZW5jZScsXG4gICAgICAgICAgc2V2ZXJpdHk6ICdoaWdoJyxcbiAgICAgICAgICBlbnRpdHlJZDogaW52ZW50b3J5SWQsXG4gICAgICAgICAgZGVzY3JpcHRpb246ICflupPlrZjorrDlvZXkuI3lrZjlnKgnLFxuICAgICAgICAgIHN1Z2dlc3RlZEFjdGlvbjogJ+ajgOafpeW6k+WtmOiusOW9lUlE5piv5ZCm5q2j56GuJ1xuICAgICAgICB9KVxuICAgICAgICByZXR1cm4gaXNzdWVzXG4gICAgICB9XG5cbiAgICAgIGNvbnN0IGludmVudG9yeSA9IGludmVudG9yeVJlc3BvbnNlLmRhdGFcblxuICAgICAgLy8g5qOA5p+l5a+55bqU55qE5Lqn5ZOBXG4gICAgICBjb25zdCBwcm9kdWN0UmVzcG9uc2UgPSBhd2FpdCBkYXRhQWNjZXNzTWFuYWdlci5wcm9kdWN0cy5nZXRCeUNvZGUoaW52ZW50b3J5LnByb2R1Y3RDb2RlKVxuICAgICAgXG4gICAgICBpZiAocHJvZHVjdFJlc3BvbnNlLnN0YXR1cyAhPT0gJ3N1Y2Nlc3MnIHx8ICFwcm9kdWN0UmVzcG9uc2UuZGF0YSkge1xuICAgICAgICBpc3N1ZXMucHVzaCh7XG4gICAgICAgICAgdHlwZTogJ21pc3NpbmdfcmVmZXJlbmNlJyxcbiAgICAgICAgICBzZXZlcml0eTogJ2hpZ2gnLFxuICAgICAgICAgIGVudGl0eUlkOiBpbnZlbnRvcnlJZCxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogYOW6k+WtmOiusOW9leW8leeUqOeahOS6p+WTgee8lueggSAke2ludmVudG9yeS5wcm9kdWN0Q29kZX0g5LiN5a2Y5ZyoYCxcbiAgICAgICAgICBzdWdnZXN0ZWRBY3Rpb246ICfliJvlu7rlr7nlupTnmoTkuqflk4HorrDlvZXmiJbliKDpmaTlupPlrZjorrDlvZUnXG4gICAgICAgIH0pXG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zdCBwcm9kdWN0ID0gcHJvZHVjdFJlc3BvbnNlLmRhdGFcblxuICAgICAgICAvLyDmo4Dmn6XmlbDmja7kuIDoh7TmgKdcbiAgICAgICAgaWYgKGludmVudG9yeS5wcm9kdWN0TmFtZSAhPT0gcHJvZHVjdC5tb2RlbE5hbWUpIHtcbiAgICAgICAgICBpc3N1ZXMucHVzaCh7XG4gICAgICAgICAgICB0eXBlOiAnZGF0YV9taXNtYXRjaCcsXG4gICAgICAgICAgICBzZXZlcml0eTogJ21lZGl1bScsXG4gICAgICAgICAgICBlbnRpdHlJZDogaW52ZW50b3J5SWQsXG4gICAgICAgICAgICBkZXNjcmlwdGlvbjogJ+S6p+WTgeWQjeensOS4jeS4gOiHtCcsXG4gICAgICAgICAgICBleHBlY3RlZFZhbHVlOiBwcm9kdWN0Lm1vZGVsTmFtZSxcbiAgICAgICAgICAgIGFjdHVhbFZhbHVlOiBpbnZlbnRvcnkucHJvZHVjdE5hbWUsXG4gICAgICAgICAgICBzdWdnZXN0ZWRBY3Rpb246ICflkIzmraXkuqflk4HlkI3np7AnXG4gICAgICAgICAgfSlcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgfVxuXG4gICAgcmV0dXJuIGlzc3Vlc1xuICB9XG59XG5cbi8vIOWvvOWHuum7mOiupOWunuS+i1xuZXhwb3J0IGNvbnN0IGRhdGFDb25zaXN0ZW5jeVNlcnZpY2UgPSBEYXRhQ29uc2lzdGVuY3lTZXJ2aWNlLmdldEluc3RhbmNlKClcbiJdLCJuYW1lcyI6WyJkYXRhQWNjZXNzTWFuYWdlciIsInRpbWVzdGFtcEdlbmVyYXRvciIsIkRhdGFDb25zaXN0ZW5jeVNlcnZpY2UiLCJnZXRJbnN0YW5jZSIsImluc3RhbmNlIiwicGVyZm9ybUZ1bGxDb25zaXN0ZW5jeUNoZWNrIiwicmVzdWx0cyIsInByb2R1Y3RJbnZlbnRvcnlDaGVjayIsImNoZWNrUHJvZHVjdEludmVudG9yeUNvbnNpc3RlbmN5IiwicHVzaCIsIm9yZGVyQ3VzdG9tZXJDaGVjayIsImNoZWNrT3JkZXJDdXN0b21lckNvbnNpc3RlbmN5Iiwib3JkZXJFbXBsb3llZUNoZWNrIiwiY2hlY2tPcmRlckVtcGxveWVlQ29uc2lzdGVuY3kiLCJ0b3RhbElzc3VlcyIsInJlZHVjZSIsInN1bSIsInJlc3VsdCIsImluY29uc2lzdGVudEl0ZW1zIiwiY3JpdGljYWxJc3N1ZXMiLCJpc3N1ZXMiLCJmaWx0ZXIiLCJpc3N1ZSIsInNldmVyaXR5IiwibGVuZ3RoIiwib3ZlcmFsbCIsInRpbWVzdGFtcCIsIm5vdyIsInRvdGFsQ2hlY2tlZCIsImludmVudG9yeVJlc3BvbnNlIiwiaW52ZW50b3J5IiwiZ2V0QWxsIiwic3RhdHVzIiwiZGF0YSIsImludmVudG9yeUl0ZW1zIiwiaXRlbXMiLCJwcm9kdWN0UmVzcG9uc2UiLCJwcm9kdWN0cyIsImdldEJ5Q29kZSIsInByb2R1Y3RDb2RlIiwidHlwZSIsImVudGl0eUlkIiwiaWQiLCJkZXNjcmlwdGlvbiIsInN1Z2dlc3RlZEFjdGlvbiIsInByb2R1Y3QiLCJwcm9kdWN0TmFtZSIsIm1vZGVsTmFtZSIsImV4cGVjdGVkVmFsdWUiLCJhY3R1YWxWYWx1ZSIsImVycm9yIiwibW9kdWxlIiwiZW50aXR5VHlwZSIsImF1dG9SZXBhaXJDb25zaXN0ZW5jeUlzc3VlcyIsInJlcGFpcmVkSXRlbXMiLCJmYWlsZWRJdGVtcyIsImVycm9ycyIsInJlcGFpckRhdGFNaXNtYXRjaCIsInJlbW92ZU9ycGhhbmVkUmVjb3JkIiwic3VjY2VzcyIsImluY2x1ZGVzIiwiZ2V0QnlQcm9kdWN0Q29kZSIsInVwZGF0ZSIsInZhbGlkYXRlRW50aXR5Q29uc2lzdGVuY3kiLCJpbnZlbnRvcnlJc3N1ZXMiLCJ2YWxpZGF0ZUludmVudG9yeUl0ZW1Db25zaXN0ZW5jeSIsImludmVudG9yeUlkIiwiZGF0YUNvbnNpc3RlbmN5U2VydmljZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/dataAccess/DataConsistencyService.ts\n"));

/***/ })

}]);