/**
 * 权限守卫组件
 * 
 * 提供组件级权限控制，根据用户权限决定是否显示特定组件
 * 遵循PRD文档中的权限验证组件设计
 */

'use client'

import React from 'react'
import { Tooltip } from 'antd'
import { LockOutlined } from '@ant-design/icons'
import { PermissionGuardProps } from '@/types/auth'
import { useAuth } from '@/hooks/useAuth'

/**
 * 权限守卫组件
 */
const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  permission,
  fallback
}) => {
  const { isAuthenticated, checkPermission } = useAuth()

  // 未认证或无权限时的处理
  if (!isAuthenticated || !checkPermission(permission)) {
    // 如果提供了fallback，显示fallback内容
    if (fallback !== undefined) {
      return <>{fallback}</>
    }

    // 默认fallback：显示权限提示
    return (
      <Tooltip title={`需要权限: ${permission}`}>
        <span className="inline-flex items-center text-gray-400 cursor-not-allowed">
          <LockOutlined className="mr-1" />
          权限不足
        </span>
      </Tooltip>
    )
  }

  // 有权限，显示子组件
  return <>{children}</>
}

/**
 * 权限守卫Hook版本
 * 用于在组件内部进行权限判断
 */
export const usePermissionGuard = (permission: string) => {
  const { isAuthenticated, checkPermission } = useAuth()
  
  return {
    hasPermission: isAuthenticated && checkPermission(permission),
    isAuthenticated
  }
}

/**
 * 多权限守卫组件
 * 支持检查多个权限，可以是AND或OR逻辑
 */
interface MultiPermissionGuardProps {
  children: React.ReactNode
  permissions: string[]
  mode?: 'and' | 'or' // 默认为'or'
  fallback?: React.ReactNode
}

export const MultiPermissionGuard: React.FC<MultiPermissionGuardProps> = ({
  children,
  permissions,
  mode = 'or',
  fallback
}) => {
  const { isAuthenticated, checkPermission } = useAuth()

  if (!isAuthenticated) {
    return fallback !== undefined ? <>{fallback}</> : null
  }

  const hasPermission = mode === 'and' 
    ? permissions.every(perm => checkPermission(perm))
    : permissions.some(perm => checkPermission(perm))

  if (!hasPermission) {
    if (fallback !== undefined) {
      return <>{fallback}</>
    }

    return (
      <Tooltip title={`需要权限: ${permissions.join(mode === 'and' ? ' 且 ' : ' 或 ')}`}>
        <span className="inline-flex items-center text-gray-400 cursor-not-allowed">
          <LockOutlined className="mr-1" />
          权限不足
        </span>
      </Tooltip>
    )
  }

  return <>{children}</>
}

/**
 * 角色守卫组件
 * 基于用户角色进行权限控制
 */
interface RoleGuardProps {
  children: React.ReactNode
  role: string
  fallback?: React.ReactNode
}

export const RoleGuard: React.FC<RoleGuardProps> = ({
  children,
  role,
  fallback
}) => {
  const { isAuthenticated, checkRole } = useAuth()

  if (!isAuthenticated || !checkRole(role)) {
    if (fallback !== undefined) {
      return <>{fallback}</>
    }

    return (
      <Tooltip title={`需要角色: ${role}`}>
        <span className="inline-flex items-center text-gray-400 cursor-not-allowed">
          <LockOutlined className="mr-1" />
          角色权限不足
        </span>
      </Tooltip>
    )
  }

  return <>{children}</>
}

export default PermissionGuard
