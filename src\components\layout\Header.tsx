'use client'

import React, { useState, useEffect } from 'react'
import { Layout, Avatar, Dropdown, Space, Badge, Button } from 'antd'
import {
  BellOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  DownOutlined,
  MenuOutlined
} from '@ant-design/icons'
import type { MenuProps } from 'antd'
import { useAuth } from '@/hooks/useAuth'
import { styleHelpers } from '@/utils/styles/antdHelpers'

const { Header: AntHeader } = Layout

export const Header: React.FC = () => {
  const { user, logout, isAuthenticated } = useAuth()
  const [isMobile, setIsMobile] = useState(false)

  // 检测屏幕尺寸
  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 768)
    }

    checkScreenSize()
    window.addEventListener('resize', checkScreenSize)

    return () => window.removeEventListener('resize', checkScreenSize)
  }, [])

  const userMenuItems: MenuProps['items'] = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '系统设置',
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: logout,
    },
  ]

  const handleMobileMenuToggle = () => {
    // 触发移动端侧边栏显示
    const event = new CustomEvent('toggleMobileSidebar')
    window.dispatchEvent(event)
  }

  return (
    <AntHeader style={{
      background: '#ffffff',
      borderBottom: '1px solid #f0f0f0',
      padding: `0 ${isMobile ? styleHelpers.spacing.sm : styleHelpers.spacing.lg}px`,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      boxShadow: styleHelpers.shadows.sm
    }}>
      {/* 左侧：移动端菜单按钮 + 标题 */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: styleHelpers.spacing.sm
      }}>
        {/* 移动端菜单按钮 */}
        {isMobile && (
          <Button
            type="text"
            icon={<MenuOutlined />}
            onClick={handleMobileMenuToggle}
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: 40,
              height: 40
            }}
          />
        )}

        <h1 style={{
          fontSize: isMobile ? '16px' : '18px',
          fontWeight: 600,
          color: styleHelpers.colors.gray[900],
          margin: 0,
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap'
        }}>
          {isMobile ? 'ERP系统' : '企业资源规划系统'}
        </h1>
      </div>

      {/* 右侧：用户操作区 */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: isMobile ? styleHelpers.spacing.xs : styleHelpers.spacing.md
      }}>
        {/* 通知铃铛 */}
        <Badge count={5} size="small">
          <Button
            type="text"
            icon={<BellOutlined />}
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: isMobile ? 32 : 40,
              height: isMobile ? 32 : 40
            }}
          />
        </Badge>

        {/* 用户信息 */}
        <Dropdown
          menu={{ items: userMenuItems }}
          trigger={['click']}
          placement="bottomRight"
        >
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: isMobile ? styleHelpers.spacing.xs : styleHelpers.spacing.sm,
            cursor: 'pointer',
            padding: `${styleHelpers.spacing.xs}px ${isMobile ? styleHelpers.spacing.xs : styleHelpers.spacing.sm}px`,
            borderRadius: styleHelpers.borderRadius.lg,
            transition: 'background-color 0.2s ease',
            backgroundColor: 'transparent'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = styleHelpers.colors.gray[50]
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = 'transparent'
          }}
          >
            <Avatar
              size={isMobile ? "small" : "default"}
              icon={<UserOutlined />}
              src={user?.avatarUrl}
            />
            {!isMobile && (
              <Space>
                <span style={{
                  color: styleHelpers.colors.gray[700],
                  fontWeight: 500
                }}>
                  {user?.fullName || user?.username || '用户'}
                </span>
                <DownOutlined style={{
                  color: styleHelpers.colors.gray[400],
                  fontSize: '12px'
                }} />
              </Space>
            )}
          </div>
        </Dropdown>
      </div>
    </AntHeader>
  )
}
