# 开发指南

## 📋 概述

本文档为开发人员提供了使用DataAccessManager系统进行开发的详细指南，包括开发环境搭建、编码规范、测试方法和最佳实践。

## 🛠️ 开发环境搭建

### 1. 环境要求
- **Node.js**: >= 16.0.0
- **npm**: >= 8.0.0 或 **yarn**: >= 1.22.0
- **TypeScript**: >= 4.5.0
- **React**: >= 18.0.0
- **IDE**: 推荐使用 VS Code

### 2. 项目初始化
```bash
# 克隆项目
git clone <repository-url>
cd erp-frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 运行类型检查
npm run type-check

# 运行测试
npm test
```

### 3. VS Code配置
```json
// .vscode/settings.json
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true,
    "source.organizeImports": true
  },
  "files.associations": {
    "*.tsx": "typescriptreact"
  }
}

// .vscode/extensions.json
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-eslint"
  ]
}
```

## 📝 编码规范

### 1. TypeScript规范

#### 类型定义
```typescript
// ✅ 推荐：明确的类型定义
interface ProductionOrder {
  id: string
  orderNumber: string
  productName: string
  quantity: number
  status: 'pending' | 'approved' | 'in_progress' | 'completed' | 'cancelled'
  createdAt: string
  updatedAt: string
}

// ✅ 推荐：泛型使用
interface ApiResponse<T> {
  status: 'success' | 'error'
  data: T
  message: string
  code: string
}

// ❌ 避免：使用any类型
const data: any = await fetchData()

// ✅ 推荐：使用具体类型
const data: ProductionOrder[] = await fetchData()
```

#### 函数定义
```typescript
// ⚠️ 已弃用：直接创建生产订单的方法
// 生产订单只能通过MRP流程创建
async function createProductionOrderDeprecated(
  orderData: CreateProductionOrderData
): Promise<ApiResponse<ProductionOrder>> {
  // 此方法已弃用，请使用MRP流程
  throw new Error('生产订单只能通过MRP流程创建')
}

// ✅ 推荐：通过MRP创建生产订单
async function createProductionOrderViaMRP(
  salesOrder: SalesOrder,
  executedBy: string
): Promise<MRPResult> {
  const { mrpService } = await import('@/services/mrpService')
  return await mrpService.executeMRP({
    salesOrder,
    executedBy,
    executionDate: new Date().toISOString()
  })
}

// ✅ 推荐：使用可选参数
function formatOrderNumber(
  prefix: string,
  number: number,
  suffix?: string
): string {
  return `${prefix}${number.toString().padStart(4, '0')}${suffix || ''}`
}
```

### 2. React组件规范

#### 函数组件
```typescript
// ✅ 推荐：函数组件with TypeScript
interface ProductionOrderListProps {
  orders: ProductionOrder[]
  loading?: boolean
  onOrderSelect?: (order: ProductionOrder) => void
}

const ProductionOrderList: React.FC<ProductionOrderListProps> = ({
  orders,
  loading = false,
  onOrderSelect
}) => {
  const handleOrderClick = useCallback((order: ProductionOrder) => {
    onOrderSelect?.(order)
  }, [onOrderSelect])

  if (loading) {
    return <Spin size="large" />
  }

  return (
    <Table
      dataSource={orders}
      columns={columns}
      onRow={(record) => ({
        onClick: () => handleOrderClick(record)
      })}
    />
  )
}

export default ProductionOrderList
```

#### Hook使用规范
```typescript
// ✅ 推荐：自定义Hook
function useProductionOrderOperations() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const createOrder = useCallback(async (orderData: CreateProductionOrderData) => {
    setLoading(true)
    setError(null)
    
    try {
      const result = await dataAccessManager.productionOrders.create(orderData)
      
      if (result.status === 'success') {
        message.success('订单创建成功')
        return result.data
      } else {
        setError(result.message)
        message.error(result.message)
        return null
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '创建失败'
      setError(errorMessage)
      message.error(errorMessage)
      return null
    } finally {
      setLoading(false)
    }
  }, [])

  return {
    createOrder,
    loading,
    error
  }
}
```

### 3. 命名规范

#### 文件命名
```
components/
  ProductionOrderList.tsx          # 组件文件：PascalCase
  ProductionOrderList.test.tsx     # 测试文件：添加.test后缀
  ProductionOrderList.stories.tsx  # Storybook文件：添加.stories后缀

hooks/
  useProductionDataAdapter.ts      # Hook文件：use前缀 + camelCase
  useProductionDataAdapter.test.ts # Hook测试文件

services/
  dataAccess/
    DataAccessManager.ts           # 服务类：PascalCase
    ProductionOrderDataAccessService.ts

utils/
  formatters.ts                    # 工具函数：camelCase
  constants.ts                     # 常量文件
```

#### 变量和函数命名
```typescript
// ✅ 推荐：描述性命名
const productionOrderList = orders.filter(order => order.status === 'in_progress')
const isOrderCompleted = (order: ProductionOrder) => order.status === 'completed'

// ❌ 避免：缩写和不清晰的命名
const pList = orders.filter(o => o.status === 'in_progress')
const isComp = (o: ProductionOrder) => o.status === 'completed'

// ✅ 推荐：常量使用UPPER_SNAKE_CASE
const MAX_RETRY_ATTEMPTS = 3
const DEFAULT_CACHE_TIMEOUT = 300000

// ✅ 推荐：布尔值使用is/has/can前缀
const isLoading = true
const hasError = false
const canEdit = user.permissions.includes('edit')
```

## 🧪 测试指南

### 1. 单元测试

#### 组件测试
```typescript
// ProductionOrderList.test.tsx
import { render, screen, fireEvent } from '@testing-library/react'
import { ProductionOrderList } from './ProductionOrderList'

const mockOrders: ProductionOrder[] = [
  {
    id: '1',
    orderNumber: 'PO-001',
    productName: '产品A',
    quantity: 100,
    status: 'pending',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  }
]

describe('ProductionOrderList', () => {
  test('应该渲染订单列表', () => {
    render(<ProductionOrderList orders={mockOrders} />)
    
    expect(screen.getByText('PO-001')).toBeInTheDocument()
    expect(screen.getByText('产品A')).toBeInTheDocument()
    expect(screen.getByText('100')).toBeInTheDocument()
  })

  test('应该处理订单点击事件', () => {
    const onOrderSelect = jest.fn()
    render(
      <ProductionOrderList 
        orders={mockOrders} 
        onOrderSelect={onOrderSelect} 
      />
    )
    
    fireEvent.click(screen.getByText('PO-001'))
    expect(onOrderSelect).toHaveBeenCalledWith(mockOrders[0])
  })

  test('加载状态应该显示Spin组件', () => {
    render(<ProductionOrderList orders={[]} loading={true} />)
    
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
  })
})
```

#### Hook测试
```typescript
// useProductionDataAdapter.test.ts
import { renderHook, act } from '@testing-library/react'
import { useProductionDataAdapter } from './useProductionDataAdapter'

// Mock DataAccessManager
jest.mock('@/services/dataAccess/DataAccessManager', () => ({
  dataAccessManager: {
    productionOrders: {
      getAll: jest.fn()
    }
  }
}))

describe('useProductionDataAdapter', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  test('应该初始化默认状态', () => {
    const { result } = renderHook(() => useProductionDataAdapter())
    
    expect(result.current.data).toEqual({})
    expect(result.current.loading).toEqual({})
    expect(result.current.error).toBeNull()
  })

  test('应该加载生产订单数据', async () => {
    const mockData = { items: mockOrders, total: 1 }
    const mockGetAll = jest.fn().mockResolvedValue({
      status: 'success',
      data: mockData
    })
    
    require('@/services/dataAccess/DataAccessManager').dataAccessManager.productionOrders.getAll = mockGetAll

    const { result } = renderHook(() => useProductionDataAdapter({
      dataTypes: ['productionOrders']
    }))

    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 100))
    })

    expect(result.current.data.productionOrders).toEqual(mockData)
    expect(result.current.loading.productionOrders).toBe(false)
  })
})
```

### 2. 集成测试

#### API集成测试
```typescript
// DataAccessManager.integration.test.ts
import { DataAccessManager } from '@/services/dataAccess/DataAccessManager'

describe('DataAccessManager集成测试', () => {
  let dataAccessManager: DataAccessManager

  beforeEach(() => {
    dataAccessManager = DataAccessManager.getInstance()
  })

  test('应该能够创建和获取生产订单', async () => {
    const orderData = {
      orderNumber: 'PO-TEST-001',
      productName: '测试产品',
      quantity: 100,
      priority: 'normal' as const,
      status: 'pending' as const,
      plannedStartDate: '2024-01-01T00:00:00Z',
      plannedEndDate: '2024-01-02T00:00:00Z'
    }

    // 创建订单
    const createResult = await dataAccessManager.productionOrders.create(orderData)
    expect(createResult.status).toBe('success')

    const orderId = createResult.data.id

    // 获取订单
    const getResult = await dataAccessManager.productionOrders.getById(orderId)
    expect(getResult.status).toBe('success')
    expect(getResult.data.orderNumber).toBe('PO-TEST-001')
  })
})
```

### 3. 端到端测试

#### Cypress测试
```typescript
// cypress/e2e/production-orders.cy.ts
describe('生产订单管理', () => {
  beforeEach(() => {
    cy.visit('/production/orders')
  })

  it('应该能够创建新订单', () => {
    cy.get('[data-testid="create-order-button"]').click()
    
    cy.get('[data-testid="order-number-input"]').type('PO-E2E-001')
    cy.get('[data-testid="product-name-input"]').type('E2E测试产品')
    cy.get('[data-testid="quantity-input"]').type('100')
    
    cy.get('[data-testid="submit-button"]').click()
    
    cy.get('[data-testid="success-message"]').should('be.visible')
    cy.get('[data-testid="order-list"]').should('contain', 'PO-E2E-001')
  })

  it('应该能够搜索订单', () => {
    cy.get('[data-testid="search-input"]').type('PO-001')
    cy.get('[data-testid="search-button"]').click()
    
    cy.get('[data-testid="order-list"]').should('contain', 'PO-001')
  })
})
```

## 🔧 开发工具和脚本

### 1. 开发脚本

#### package.json脚本
```json
{
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "eslint . --ext .ts,.tsx",
    "lint:fix": "eslint . --ext .ts,.tsx --fix",
    "type-check": "tsc --noEmit",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:e2e": "cypress run",
    "test:e2e:open": "cypress open",
    "storybook": "start-storybook -p 6006",
    "build-storybook": "build-storybook"
  }
}
```

#### 自定义开发脚本
```javascript
// scripts/dev-setup.js
const fs = require('fs')
const path = require('path')

console.log('🚀 设置开发环境...')

// 检查必要的环境变量
const requiredEnvVars = [
  'REACT_APP_API_BASE_URL',
  'REACT_APP_ENABLE_CACHE'
]

const missingVars = requiredEnvVars.filter(varName => !process.env[varName])

if (missingVars.length > 0) {
  console.error('❌ 缺少必要的环境变量:', missingVars.join(', '))
  console.log('请检查 .env.local 文件')
  process.exit(1)
}

// 创建必要的目录
const directories = ['logs', 'temp', 'reports']
directories.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true })
    console.log(`✅ 创建目录: ${dir}`)
  }
})

console.log('✅ 开发环境设置完成')
```

### 2. 代码生成工具

#### 组件生成器
```javascript
// scripts/generate-component.js
const fs = require('fs')
const path = require('path')

function generateComponent(componentName) {
  const componentDir = path.join('src/components', componentName)
  
  if (!fs.existsSync(componentDir)) {
    fs.mkdirSync(componentDir, { recursive: true })
  }

  // 组件文件
  const componentContent = `
import React from 'react'

interface ${componentName}Props {
  // 定义props类型
}

const ${componentName}: React.FC<${componentName}Props> = ({
  // props解构
}) => {
  return (
    <div>
      {/* 组件内容 */}
    </div>
  )
}

export default ${componentName}
`

  // 测试文件
  const testContent = `
import { render, screen } from '@testing-library/react'
import ${componentName} from './${componentName}'

describe('${componentName}', () => {
  test('应该正确渲染', () => {
    render(<${componentName} />)
    // 添加测试断言
  })
})
`

  // 写入文件
  fs.writeFileSync(path.join(componentDir, `${componentName}.tsx`), componentContent.trim())
  fs.writeFileSync(path.join(componentDir, `${componentName}.test.tsx`), testContent.trim())
  fs.writeFileSync(path.join(componentDir, 'index.ts'), `export { default } from './${componentName}'`)

  console.log(`✅ 组件 ${componentName} 生成完成`)
}

// 使用方式: node scripts/generate-component.js ComponentName
const componentName = process.argv[2]
if (componentName) {
  generateComponent(componentName)
} else {
  console.error('请提供组件名称')
}
```

## 📋 开发检查清单

### 开发前检查
- [ ] 确认需求和设计
- [ ] 检查相关API文档
- [ ] 设置开发环境
- [ ] 创建功能分支

### 开发中检查
- [ ] 遵循编码规范
- [ ] 添加类型定义
- [ ] 编写单元测试
- [ ] 处理错误情况
- [ ] 添加必要注释

### 开发后检查
- [ ] 运行所有测试
- [ ] 执行类型检查
- [ ] 代码格式化
- [ ] 性能检查
- [ ] 提交代码审查

### 发布前检查
- [ ] 集成测试通过
- [ ] 端到端测试通过
- [ ] 性能测试通过
- [ ] 文档更新
- [ ] 版本标记

## 🎯 最佳实践

### 1. 性能优化
- 使用React.memo优化组件渲染
- 使用useCallback和useMemo优化计算
- 实现虚拟滚动处理大列表
- 使用懒加载减少初始加载时间

### 2. 错误处理
- 实现全局错误边界
- 添加用户友好的错误信息
- 记录详细的错误日志
- 提供错误恢复机制

### 3. 代码质量
- 保持函数简洁，单一职责
- 使用有意义的变量和函数名
- 添加必要的注释和文档
- 定期重构和优化代码

### 4. 团队协作
- 遵循Git工作流规范
- 编写清晰的提交信息
- 进行代码审查
- 分享知识和最佳实践

## 📚 相关资源

### 文档链接
- [DataAccessManager架构文档 - 简化版](./DataAccessManager架构文档.md)
- [Hook系统使用指南](./Hook系统使用指南.md)
- [API参考文档](./API参考文档.md)
- [性能优化最佳实践 - 简化版](./性能优化最佳实践.md)
- [缓存架构整合方案 - 简化版](../技术方案/缓存架构整合方案.md)

### 外部资源
- [React官方文档](https://react.dev/)
- [TypeScript官方文档](https://www.typescriptlang.org/)
- [Ant Design组件库](https://ant.design/)
- [Jest测试框架](https://jestjs.io/)
