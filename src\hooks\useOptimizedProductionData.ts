/**
 * 优化的生产数据Hook
 * 统一管理所有生产相关数据，避免重复请求和多个定时器
 * 实现智能缓存、懒加载和内存管理
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import { ProductionOrder, ProductionWorkOrder, Workstation } from '@/types'
import { dataAccessManager } from '@/services/dataAccess/DataAccessManager'
import { ProductionStatistics } from '@/services/dataAccess/ProductionOrderDataAccessService'
import { WorkOrderStatistics } from '@/services/dataAccess/ProductionWorkOrderDataAccessService'
import { WorkstationUtilization } from '@/services/dataAccess/DataAccessLayer'
import { CostStatistics } from '@/services/dataAccess/CostCalculationDataAccessService'
import { useProductionRealTimeSync } from './useProductionRealTimeSync'
import { DataChangeEventType } from '@/services/realtime/RealtimeDataSyncService'
// ✅ 使用DataAccessManager统一监控

export interface OptimizedProductionDataOptions {
  /** 需要加载的数据类型 */
  dataTypes?: ('orders' | 'workOrders' | 'workstations' | 'statistics')[]
  /** 是否启用实时同步 */
  enableRealTimeSync?: boolean
  /** 是否启用懒加载 */
  enableLazyLoading?: boolean
  /** 缓存时间（毫秒） */
  cacheTimeout?: number
  /** 自动刷新间隔（毫秒），0表示禁用 */
  refreshInterval?: number
  /** 是否显示同步消息 */
  showSyncMessages?: boolean
  /** 数据变更回调 */
  onDataChange?: (eventType: DataChangeEventType, data: any) => void
  /** 性能监控回调 */
  onPerformanceUpdate?: (metrics: any) => void
}

export interface OptimizedProductionDataReturn {
  // 数据状态
  data: {
    productionOrders: ProductionOrder[]
    productionWorkOrders: ProductionWorkOrder[]
    workstations: Workstation[]
    statistics: {
      production: ProductionStatistics | null
      workOrder: WorkOrderStatistics | null
      workstation: WorkstationUtilization[]
      cost: CostStatistics | null
    }
  }
  
  // 状态管理
  loading: {
    orders: boolean
    workOrders: boolean
    workstations: boolean
    statistics: boolean
    global: boolean
  }
  
  error: string | null
  lastUpdated: Date | null
  
  // 实时同步状态
  realTimeSync: {
    isConnected: boolean
    isSyncing: boolean
    error: string | null
  }
  
  // 性能指标
  performance: {
    renderTime: number
    dataLoadTime: number
    memoryUsage: number
    cacheHitRate: number
  }
  
  // 操作方法
  refresh: (dataTypes?: string[]) => Promise<void>
  invalidateCache: (dataTypes?: string[]) => void
  optimizeMemory: () => void
}

/**
 * 优化的生产数据Hook
 */
export const useOptimizedProductionData = (
  options: OptimizedProductionDataOptions = {}
): OptimizedProductionDataReturn => {
  const {
    dataTypes = ['orders', 'workOrders', 'workstations', 'statistics'],
    enableRealTimeSync = true,
    enableLazyLoading = true,
    cacheTimeout = 5 * 60 * 1000, // 5分钟
    refreshInterval = 0, // 默认禁用自动刷新，依赖实时同步
    showSyncMessages = false,
    onDataChange,
    onPerformanceUpdate
  } = options

  // 数据状态
  const [productionOrders, setProductionOrders] = useState<ProductionOrder[]>([])
  const [productionWorkOrders, setProductionWorkOrders] = useState<ProductionWorkOrder[]>([])
  const [workstations, setWorkstations] = useState<Workstation[]>([])
  const [statistics, setStatistics] = useState({
    production: null as ProductionStatistics | null,
    workOrder: null as WorkOrderStatistics | null,
    workstation: [] as WorkstationUtilization[],
    cost: null as CostStatistics | null
  })

  // 加载状态
  const [loading, setLoading] = useState({
    orders: false,
    workOrders: false,
    workstations: false,
    statistics: false,
    global: false
  })

  const [error, setError] = useState<string | null>(null)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)

  // 性能指标
  const [performance, setPerformance] = useState({
    renderTime: 0,
    dataLoadTime: 0,
    memoryUsage: 0,
    cacheHitRate: 0
  })

  // 引用
  const refreshTimerRef = useRef<NodeJS.Timeout | null>(null)
  const loadingRequestsRef = useRef<Set<string>>(new Set())
  const componentIdRef = useRef(`production-data-${Date.now()}`)

  // 实时同步Hook
  const {
    isConnected: syncConnected,
    isSyncing,
    error: syncError,
    manualRefresh: syncManualRefresh
  } = useProductionRealTimeSync({
    enabled: enableRealTimeSync,
    showSyncMessages,
    onDataChange: useCallback((eventType: DataChangeEventType, data: any) => {
      // 根据事件类型智能刷新对应数据
      const refreshMap: Record<string, string[]> = {
        [DataChangeEventType.PRODUCTION_ORDER_CREATED]: ['orders'],
        [DataChangeEventType.PRODUCTION_ORDER_UPDATED]: ['orders'],
        [DataChangeEventType.PRODUCTION_ORDER_DELETED]: ['orders'],
        // 🔧 新增：工单事件映射
        [DataChangeEventType.PRODUCTION_WORK_ORDER_CREATED]: ['workOrders'],
        [DataChangeEventType.PRODUCTION_WORK_ORDER_UPDATED]: ['workOrders'],
        [DataChangeEventType.PRODUCTION_WORK_ORDER_DELETED]: ['workOrders']
      }

      const typesToRefresh = refreshMap[eventType] || []
      if (typesToRefresh.length > 0) {
        refresh(typesToRefresh)
      }

      if (onDataChange) {
        onDataChange(eventType, data)
      }
    }, [onDataChange])
  })

  // 内存管理
  useEffect(() => {
    const componentId = componentIdRef.current
    
    // 注册组件资源
    // 已替换为DataAccessManager统一监控
    
    return () => {
      // ✅ 使用DataAccessManager统一清理
    }
  }, [])

  // 防重复请求的加载函数
  const loadData = useCallback(async (
    dataType: string,
    loader: () => Promise<any>,
    setter: (data: any) => void
  ) => {
    const requestKey = `${dataType}-${Date.now()}`
    
    // 防止重复请求
    if (loadingRequestsRef.current.has(dataType)) {
      return
    }

    loadingRequestsRef.current.add(dataType)
    
    try {
      setLoading(prev => ({ ...prev, [dataType]: true, global: true }))
      setError(null)

      const startTime = Date.now()
      // ✅ 使用DataAccessManager统一性能监控
      const result = await loader()
      const endTime = Date.now()

      if (result.status === 'success' && result.data) {
        setter(result.data)
        setLastUpdated(new Date())
        
        // 更新性能指标
        setPerformance(prev => ({
          ...prev,
          dataLoadTime: endTime - startTime
        }))
      } else {
        throw new Error(result.message || `加载${dataType}失败`)
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : `加载${dataType}失败`
      setError(errorMessage)
    } finally {
      setLoading(prev => ({ 
        ...prev, 
        [dataType]: false,
        global: Object.values({ ...prev, [dataType]: false }).some(Boolean)
      }))
      loadingRequestsRef.current.delete(dataType)
    }
  }, [])

  // 加载生产订单
  const loadOrders = useCallback(() => {
    if (!dataTypes.includes('orders')) return Promise.resolve()
    
    return loadData(
      'orders',
      () => dataAccessManager.productionOrders.getAll(),
      (data) => setProductionOrders(data.items || [])
    )
  }, [dataTypes, loadData])

  // 加载工单
  const loadWorkOrders = useCallback(() => {
    if (!dataTypes.includes('workOrders')) return Promise.resolve()
    
    return loadData(
      'workOrders',
      () => dataAccessManager.productionWorkOrders.getAll(),
      (data) => setProductionWorkOrders(data.items || [])
    )
  }, [dataTypes, loadData])

  // 加载工位
  const loadWorkstations = useCallback(() => {
    if (!dataTypes.includes('workstations')) return Promise.resolve()
    
    return loadData(
      'workstations',
      () => dataAccessManager.workstations.getAll(),
      (data) => setWorkstations(data.items || [])
    )
  }, [dataTypes, loadData])

  // 加载统计数据
  const loadStatistics = useCallback(async () => {
    if (!dataTypes.includes('statistics')) return

    const requestKey = 'statistics'
    if (loadingRequestsRef.current.has(requestKey)) return

    loadingRequestsRef.current.add(requestKey)

    try {
      setLoading(prev => ({ ...prev, statistics: true, global: true }))
      setError(null)

      const startTime = Date.now()
      
      // 并行加载所有统计数据
      const [
        productionStatsResponse,
        workOrderStatsResponse
      ] = await Promise.all([
        dataAccessManager.productionOrders.getAll(),
        dataAccessManager.productionWorkOrders.getAll()
      ])

      const endTime = Date.now()

      // 简化统计数据，基于实际获取的数据计算
      const productionData = productionStatsResponse.status === 'success' ? productionStatsResponse.data?.items || [] : []
      const workOrderData = workOrderStatsResponse.status === 'success' ? workOrderStatsResponse.data?.items || [] : []

      // 构建符合ProductionStatistics接口的数据
      const productionStats: ProductionStatistics = {
        totalOrders: productionData.length,
        activeOrders: productionData.filter(o => o.status === 'in_progress').length,
        completedOrders: productionData.filter(o => o.status === 'completed').length,
        totalProduction: productionData.reduce((sum, o) => sum + (o.producedQuantity || 0), 0),
        averageEfficiency: productionData.length > 0 ?
          productionData.reduce((sum, o) => sum + ((o.producedQuantity || 0) / (o.plannedQuantity || 1) * 100), 0) / productionData.length : 0,
        ordersByStatus: {
          'in_plan': productionData.filter(o => o.status === 'in_plan').length,
          'in_progress': productionData.filter(o => o.status === 'in_progress').length,
          'completed': productionData.filter(o => o.status === 'completed').length,
          'cancelled': productionData.filter(o => o.status === 'cancelled').length
        },
        productionTrend: []
      }

      // 构建符合WorkOrderStatistics接口的数据
      const workOrderStats: WorkOrderStatistics = {
        totalWorkOrders: workOrderData.length,
        activeWorkOrders: workOrderData.filter(w => w.status === 'in_progress').length,
        completedWorkOrders: workOrderData.filter(w => w.status === 'completed').length,
        totalMolds: workOrderData.reduce((sum, w) => sum + (w.completedMoldCount || 0), 0),
        averageExecutionRate: workOrderData.length > 0 ?
          workOrderData.reduce((sum, w) => sum + (w.executionRate || 0), 0) / workOrderData.length : 0,
        workOrdersByStatus: {
          'pending': workOrderData.filter(w => w.status === 'pending').length,
          'in_progress': workOrderData.filter(w => w.status === 'in_progress').length,
          'completed': workOrderData.filter(w => w.status === 'completed').length
        },
        workOrdersByWorkstation: {},
        productionEfficiency: []
      }

      setStatistics({
        production: productionStats,
        workOrder: workOrderStats,
        workstation: [],
        cost: null
      })

      setLastUpdated(new Date())
      
      // 更新性能指标
      setPerformance(prev => ({
        ...prev,
        dataLoadTime: endTime - startTime
      }))

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '加载统计数据失败'
      setError(errorMessage)
    } finally {
      setLoading(prev => ({ 
        ...prev, 
        statistics: false,
        global: Object.values({ ...prev, statistics: false }).some(Boolean)
      }))
      loadingRequestsRef.current.delete(requestKey)
    }
  }, [dataTypes])

  // 统一刷新方法
  const refresh = useCallback(async (targetDataTypes?: string[]) => {
    const typesToRefresh = targetDataTypes || dataTypes
    
    const loadPromises: Promise<any>[] = []
    
    if (typesToRefresh.includes('orders')) {
      loadPromises.push(loadOrders())
    }
    if (typesToRefresh.includes('workOrders')) {
      loadPromises.push(loadWorkOrders())
    }
    if (typesToRefresh.includes('workstations')) {
      loadPromises.push(loadWorkstations())
    }
    if (typesToRefresh.includes('statistics')) {
      loadPromises.push(loadStatistics())
    }

    await Promise.all(loadPromises)

    // 如果启用了实时同步，也触发同步刷新
    if (enableRealTimeSync) {
      try {
        await syncManualRefresh()
      } catch (error) {
      }
    }
  }, [dataTypes, loadOrders, loadWorkOrders, loadWorkstations, loadStatistics, enableRealTimeSync, syncManualRefresh])

  // 缓存失效
  const invalidateCache = useCallback((targetDataTypes?: string[]) => {
    const typesToInvalidate = targetDataTypes || dataTypes

    if (typesToInvalidate.includes('orders')) {
      dataAccessManager.clearDataTypeCache('productionOrders')
    }
    if (typesToInvalidate.includes('workOrders')) {
      dataAccessManager.clearDataTypeCache('productionWorkOrders')
    }
    if (typesToInvalidate.includes('workstations')) {
      dataAccessManager.clearDataTypeCache('workstations')
    }
    if (typesToInvalidate.includes('statistics')) {
      dataAccessManager.clearAllCache()
    }
  }, [dataTypes])

  // 内存优化
  const optimizeMemory = useCallback(() => {
    // 清理缓存
    dataAccessManager.clearAllCache()
    
    // 执行内存管理器清理
    // ✅ 使用DataAccessManager统一内存管理
    
    // 更新内存使用指标
    // ✅ 使用DataAccessManager统一监控
    const cacheStats = dataAccessManager.getCacheStatistics()
    if (cacheStats) {
      setPerformance(prev => ({
        ...prev,
        memoryUsage: cacheStats.size // 使用缓存大小作为内存使用指标
      }))
    }
  }, [])

  // 初始化数据加载
  useEffect(() => {
    if (enableLazyLoading) {
      // 懒加载：只在需要时加载
      return
    }
    
    // 立即加载
    refresh()
  }, [enableLazyLoading, refresh])

  // 自动刷新设置
  useEffect(() => {
    if (refreshInterval <= 0) return

    refreshTimerRef.current = setInterval(() => {
      refresh()
    }, refreshInterval)

    return () => {
      if (refreshTimerRef.current) {
        clearInterval(refreshTimerRef.current)
        refreshTimerRef.current = null
      }
    }
  }, [refreshInterval, refresh])

  // 性能监控
  useEffect(() => {
    const updatePerformanceMetrics = () => {
      const cacheStats = dataAccessManager.getCacheStatistics()
      // ✅ 使用DataAccessManager统一监控
      
      const newMetrics = {
        renderTime: performance.renderTime || 0,
        dataLoadTime: performance.dataLoadTime,
        memoryUsage: cacheStats.size, // 使用缓存大小作为内存使用指标
        cacheHitRate: cacheStats ? cacheStats.hitRate * 100 : 0
      }
      
      setPerformance(newMetrics)
      
      if (onPerformanceUpdate) {
        onPerformanceUpdate(newMetrics)
      }
    }

    const interval = setInterval(updatePerformanceMetrics, 10000) // 每10秒更新一次
    
    return () => clearInterval(interval)
  }, [onPerformanceUpdate])

  // 返回优化后的数据和方法
  return {
    data: {
      productionOrders,
      productionWorkOrders,
      workstations,
      statistics
    },
    loading,
    error,
    lastUpdated,
    realTimeSync: {
      isConnected: syncConnected,
      isSyncing,
      error: syncError
    },
    performance,
    refresh,
    invalidateCache,
    optimizeMemory
  }
}

export default useOptimizedProductionData
