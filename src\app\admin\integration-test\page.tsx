'use client'

import React, { useState, useEffect } from 'react'
import {
  Card,
  Row,
  Col,
  Button,
  Space,
  Steps,
  Alert,
  App,
  Spin,
  Tag,
  Descriptions,
  Timeline,
  Progress,
  Divider
} from 'antd'
import {
  PlayCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined,
  BugOutlined
} from '@ant-design/icons'
import { SalesOrder} from '@/types'
// 移除useSalesStore依赖，使用dataAccessManager统一数据访问
import { dataAccessManager } from '@/services/dataAccess/DataAccessManager'
// MRP服务已删除
// 订单排产集成服务已删除
// 智能排产相关服务已删除

const { Step } = Steps

interface TestResult {
  step: string
  status: 'success' | 'error' | 'warning' | 'running'
  message: string
  data?: any
  duration?: number
}

const IntegrationTestPage: React.FC = () => {
  const { message } = App.useApp()
  const [testing, setTesting] = useState(false)
  const [currentStep, setCurrentStep] = useState(0)
  const [testResults, setTestResults] = useState<TestResult[]>([])
  const [testOrder, setTestOrder] = useState<SalesOrder | null>(null)

  // 使用本地状态管理替代useSalesStore
  const [orders, setOrders] = useState<SalesOrder[]>([])

  // 使用本地状态管理替代useAutoSchedulingStore
  const [pendingOrders, setPendingOrders] = useState<any[]>([])
  const [statistics, setStatistics] = useState({
    totalOrders: 0,
    totalTasks: 0,
    assignedTasks: 0,
    unassignedTasks: 0,
    averageUtilization: 0,
    conflictCount: 0
  })

  // 执行完整的集成测试
  const runIntegrationTest = async () => {
    setTesting(true)
    setCurrentStep(0)
    setTestResults([])
    setTestOrder(null)

    const results: TestResult[] = []

    try {
      // 步骤1: 创建测试订单
      setCurrentStep(1)
      const step1Start = Date.now()
      
      const newOrder: SalesOrder = {
        id: `test_${Date.now()}`,
        orderNumber: `SO${Date.now().toString().slice(-6)}`,
        customerId: 'TEST001',
        customerName: '测试客户',
        customerContact: '测试联系人',
        orderDate: new Date().toISOString().split('T')[0],
        deliveryDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        promisedDeliveryDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        status: 'pending',
        productionStatus: 'not_started',
        paymentStatus: 'unpaid',
        paymentTerms: '货到付款',
        salesRepresentative: '测试销售',
        totalAmount: 17500,
        discountAmount: 0,
        finalAmount: 17500,
        // MRP相关状态字段
        mrpStatus: 'not_started',
        mrpExecutedAt: undefined,
        mrpExecutedBy: undefined,
        mrpResultId: undefined,
        items: [
          {
            id: 'item1',
            orderNumber: `SO${Date.now().toString().slice(-6)}`,

            productName: '测试产品A',
            productCode: 'P00001',
            quantity: 1000,
            unit: '个',
            unitPrice: 10,
            totalPrice: 10000,
            deliveryQuantity: 0,
            remainingQuantity: 1000,
            moldCode: 'M001',
            productionWorkstation: '',
            batchNumber: '',
            remark: ''
          },
          {
            id: 'item2',
            orderNumber: `SO${Date.now().toString().slice(-6)}`,

            productName: '测试产品B',
            productCode: 'P00002',
            quantity: 500,
            unit: '个',
            unitPrice: 15,
            totalPrice: 7500,
            deliveryQuantity: 0,
            remainingQuantity: 500,
            moldCode: 'M002',
            productionWorkstation: '',
            batchNumber: '',
            remark: ''
          }
        ],
        changes: [],
        remark: '集成测试订单',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      // 使用dataAccessManager创建订单
      const createResponse = await dataAccessManager.orders.create({
        orderNumber: newOrder.orderNumber,
        customerId: newOrder.customerId,
        customerName: newOrder.customerName,
        customerContact: newOrder.customerContact,
        orderDate: newOrder.orderDate,
        deliveryDate: newOrder.deliveryDate,
        salesRepresentative: newOrder.salesRepresentative,
        items: newOrder.items,
        totalAmount: newOrder.totalAmount,
        finalAmount: newOrder.finalAmount,
        status: newOrder.status,
        paymentTerms: newOrder.paymentTerms,
        paymentStatus: newOrder.paymentStatus,
        remark: newOrder.remark
      })

      if (createResponse.status === 'success' && createResponse.data) {
        setTestOrder(createResponse.data)
        // 刷新订单列表
        const ordersResponse = await dataAccessManager.orders.getAll()
        if (ordersResponse.status === 'success' && ordersResponse.data) {
          setOrders(ordersResponse.data.items)
        }

        results.push({
          step: '创建测试订单',
          status: 'success',
          message: `成功创建订单 ${createResponse.data.orderNumber}`,
          data: createResponse.data,
          duration: Date.now() - step1Start
        })
      } else {
        throw new Error(createResponse.message || '创建订单失败')
      }

      // 步骤2: 审核订单
      setCurrentStep(2)
      const step2Start = Date.now()
      
      // 使用dataAccessManager更新订单状态
      const updateResponse = await dataAccessManager.orders.update(testOrder!.id, {
        status: 'confirmed' as const,
        updatedAt: new Date().toISOString()
      })

      if (updateResponse.status === 'success') {
        // 刷新订单列表
        const ordersResponse = await dataAccessManager.orders.getAll()
        if (ordersResponse.status === 'success' && ordersResponse.data) {
          setOrders(ordersResponse.data.items)
          const confirmedOrder = ordersResponse.data.items.find(o => o.id === testOrder!.id)

          if (confirmedOrder?.status === 'confirmed') {
            results.push({
              step: '审核订单',
              status: 'success',
              message: `订单 ${testOrder!.orderNumber} 审核成功`,
              data: confirmedOrder,
              duration: Date.now() - step2Start
            })
          } else {
            throw new Error('订单状态未正确更新')
          }
        }
      } else {
        throw new Error(updateResponse.message || '更新订单状态失败')
      }

      // 步骤3: MRP功能已移除
      setCurrentStep(3)
      const step3Start = Date.now()

      try {
        // 模拟MRP执行结果
        await new Promise(resolve => setTimeout(resolve, 1000))

        results.push({
          step: 'MRP执行',
          status: 'success',
          message: 'MRP功能已移除，跳过此步骤',
          data: null,
          duration: Date.now() - step3Start
        })

        // 步骤4: 自动排单模块测试
        setCurrentStep(4)
        const step4Start = Date.now()

        const transferResult = {
          success: false,
          message: '订单排产集成服务已删除',
          transferredCount: 0,
          errors: ['服务已删除']
        }

          if (transferResult.success) {
            results.push({
              step: '传输到自动排单',
              status: 'success',
              message: `成功传输 ${transferResult.transferredCount} 个待排产订单`,
              data: transferResult,
              duration: Date.now() - step4Start
            })

            // 步骤5: 执行智能排单
            setCurrentStep(5)
            const step5Start = Date.now()

            // 智能排产功能已删除，跳过此步骤
            const schedulingResult = {
              success: false,
              message: '智能排产功能已删除',
              scheduledTasks: [],
              totalOrders: 0
            }

            results.push({
              step: '智能排单执行',
              status: 'warning',
              message: `智能排单功能已删除`,
              data: schedulingResult,
              duration: Date.now() - step5Start
            })
          } else {
            results.push({
              step: '传输到自动排单',
              status: 'error',
              message: `传输失败: ${transferResult.errors.join(', ')}`,
              duration: Date.now() - step4Start
            })
          }

      } catch (error) {
        results.push({
          step: 'MRP执行',
          status: 'error',
          message: `MRP执行失败: ${error instanceof Error ? error.message : '未知错误'}`,
          duration: Date.now() - step3Start
        })
      }

    } catch (error) {
      results.push({
        step: '测试异常',
        status: 'error',
        message: `测试过程中发生异常: ${error instanceof Error ? error.message : '未知错误'}`,
        duration: 0
      })
    }

    setTestResults(results)
    setCurrentStep(results.length)
    setTesting(false)

    // 显示测试结果
    const successCount = results.filter(r => r.status === 'success').length
    const errorCount = results.filter(r => r.status === 'error').length
    
    if (errorCount === 0) {
      message.success(`集成测试完成！${successCount} 个步骤全部成功`)
    } else {
      message.error(`集成测试完成，${errorCount} 个步骤失败，${successCount} 个步骤成功`)
    }

    // 更新统计信息
    const integrationData = {
      totalOrders: 0,
      completedOrders: 0,
      pendingOrders: 0,
      lastUpdateTime: new Date().toISOString()
    }
    setStatistics({
      totalOrders: integrationData.totalOrders,
      totalTasks: 0,
      assignedTasks: 0,
      unassignedTasks: 0,
      averageUtilization: 0,
      conflictCount: 0
    })
  }

  // 获取步骤状态
  const getStepStatus = (index: number) => {
    if (index < currentStep) return 'finish'
    if (index === currentStep && testing) return 'process'
    if (index > currentStep) return 'wait'
    
    const result = testResults[index]
    if (result?.status === 'error') return 'error'
    return 'finish'
  }

  // 获取结果图标
  const getResultIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success': return <CheckCircleOutlined style={{ color: '#52c41a' }} />
      case 'error': return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
      case 'warning': return <ExclamationCircleOutlined style={{ color: '#faad14' }} />
      case 'running': return <Spin size="small" />
      default: return null
    }
  }

  return (
    <div className="integration-test-page" style={{ padding: '24px' }}>
      <Card>
        <Row justify="space-between" align="middle" style={{ marginBottom: '24px' }}>
          <Col>
            <h2 style={{ margin: 0 }}>
              <BugOutlined style={{ marginRight: '8px' }} />
              订单管理与自动排单集成测试
            </h2>
            <p style={{ margin: '8px 0 0 0', color: '#666' }}>
              验证从订单创建到自动排单的完整业务流程
            </p>
          </Col>
          <Col>
            <Space>
              <Button 
                type="primary" 
                icon={<PlayCircleOutlined />}
                loading={testing}
                onClick={runIntegrationTest}
              >
                开始测试
              </Button>
              <Button 
                icon={<ReloadOutlined />}
                onClick={() => {
                  setTestResults([])
                  setCurrentStep(0)
                  setTestOrder(null)
                }}
              >
                重置
              </Button>
            </Space>
          </Col>
        </Row>

        {/* 测试步骤 */}
        <Card title="测试步骤" size="small" style={{ marginBottom: '24px' }}>
          <Steps current={currentStep} status={testing ? 'process' : 'finish'}>
            <Step title="创建订单" description="创建测试销售订单" />
            <Step title="审核订单" description="将订单状态变更为已审核" />
            <Step title="触发MRP" description="自动执行物料需求计划" />
            <Step title="传输订单" description="将待排产订单传输到自动排单模块" />
            <Step title="智能排单" description="执行智能排单算法" />
          </Steps>
        </Card>

        {/* 测试结果 */}
        {testResults.length > 0 && (
          <Card title="测试结果" size="small" style={{ marginBottom: '24px' }}>
            <Timeline>
              {testResults.map((result, index) => (
                <Timeline.Item
                  key={index}
                  dot={getResultIcon(result.status)}
                  color={result.status === 'success' ? 'green' : result.status === 'error' ? 'red' : 'orange'}
                >
                  <div>
                    <strong>{result.step}</strong>
                    <Tag 
                      color={result.status === 'success' ? 'green' : result.status === 'error' ? 'red' : 'orange'}
                      style={{ marginLeft: '8px' }}
                    >
                      {result.status}
                    </Tag>
                    {result.duration && (
                      <Tag color="blue" style={{ marginLeft: '4px' }}>
                        {result.duration}ms
                      </Tag>
                    )}
                  </div>
                  <div style={{ marginTop: '4px', color: '#666' }}>
                    {result.message}
                  </div>
                </Timeline.Item>
              ))}
            </Timeline>
          </Card>
        )}

        {/* 当前状态 */}
        <Row gutter={16}>
          <Col span={12}>
            <Card title="订单管理状态" size="small">
              {testOrder ? (
                <Descriptions column={1} size="small">
                  <Descriptions.Item label="订单号">{testOrder.orderNumber}</Descriptions.Item>
                  <Descriptions.Item label="状态">
                    <Tag color={testOrder.status === 'confirmed' ? 'green' : 'orange'}>
                      {testOrder.status === 'confirmed' ? '已审核' : '未审核'}
                    </Tag>
                  </Descriptions.Item>
                  <Descriptions.Item label="产品数量">{testOrder.items.length}</Descriptions.Item>
                  <Descriptions.Item label="订单金额">¥{testOrder.totalAmount}</Descriptions.Item>
                </Descriptions>
              ) : (
                <div style={{ textAlign: 'center', color: '#999', padding: '20px' }}>
                  暂无测试订单
                </div>
              )}
            </Card>
          </Col>
          
          <Col span={12}>
            <Card title="自动排单状态" size="small">
              <Descriptions column={1} size="small">
                <Descriptions.Item label="待排产订单">{statistics.totalOrders}</Descriptions.Item>
                <Descriptions.Item label="总任务数">{statistics.totalTasks}</Descriptions.Item>
                <Descriptions.Item label="已分配任务">{statistics.assignedTasks}</Descriptions.Item>
                <Descriptions.Item label="未分配任务">{statistics.unassignedTasks}</Descriptions.Item>
                <Descriptions.Item label="平均利用率">
                  <Progress 
                    percent={Math.round(statistics.averageUtilization * 100)} 
                    size="small" 
                    style={{ width: '100px' }}
                  />
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Col>
        </Row>

        {/* 测试说明 */}
        <Divider />
        <Alert
          type="info"
          message="测试说明"
          description={
            <div>
              <p>此测试将验证以下业务流程的完整性：</p>
              <ul>
                <li>1. 在订单管理模块创建销售订单</li>
                <li>2. 审核订单，状态从&ldquo;未审核&rdquo;变更为&ldquo;已审核&rdquo;</li>
                <li>3. 系统自动触发MRP（物料需求计划）执行</li>
                <li>4. MRP检查库存，生成待排产订单</li>
                <li>5. 待排产订单自动传输到自动排单模块</li>
                <li>6. 执行智能排单算法，生成排产计划</li>
              </ul>
              <p><strong>预期结果：</strong>整个流程应该无缝衔接，数据完整传输，无错误发生。</p>
            </div>
          }
          showIcon
        />
      </Card>
    </div>
  )
}

// 用App组件包裹以提供message等上下文
export default function IntegrationTestPageWrapper() {
  return (
    <App>
      <IntegrationTestPage />
    </App>
  )
}
