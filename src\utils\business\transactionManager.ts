/**
 * 事务管理器
 * 统一处理订单管理模块中的事务处理逻辑，消除重复的事务处理模式
 * 
 * 重构目标：
 * - 提供通用的事务处理框架
 * - 统一原子性操作和回滚机制
 * - 消除OrderCancellationService和OrderQuantityChangeService中的重复模式
 * - 支持多种事务类型和自定义处理逻辑
 */

import { dataAccessManager } from '@/services/dataAccess/DataAccessManager'
import { dataSyncService } from '@/services/dataAccess/DataSyncService'
import { SalesOrder, ProductionOrder, ProductionWorkOrder } from '@/types'

/**
 * 事务操作接口
 */
export interface TransactionOperation {
  id: string
  type: 'create' | 'update' | 'delete'
  entity: 'salesOrder' | 'productionOrder' | 'workOrder'
  entityId: string
  data?: any
  originalData?: any
}

/**
 * 事务上下文接口
 */
export interface TransactionContext {
  transactionId: string
  operations: TransactionOperation[]
  metadata?: Record<string, any>
  createdAt: string
}

/**
 * 事务执行结果接口
 */
export interface TransactionResult {
  success: boolean
  transactionId: string
  completedOperations: number
  totalOperations: number
  errors: string[]
  rollbackData?: any
}

/**
 * 事务配置接口
 */
export interface TransactionConfig {
  enableRollback: boolean
  enableDataSync: boolean
  enableLogging: boolean
  timeoutMs: number
}

/**
 * 事务管理器类
 * 提供统一的事务处理框架
 */
export class TransactionManager {
  private static readonly DEFAULT_CONFIG: TransactionConfig = {
    enableRollback: true,
    enableDataSync: true,
    enableLogging: true,
    timeoutMs: 30000 // 30秒超时
  }

  private static activeTransactions = new Map<string, TransactionContext>()

  /**
   * 开始事务
   * 创建事务上下文并准备执行
   */
  static async beginTransaction(
    operations: TransactionOperation[],
    metadata?: Record<string, any>
  ): Promise<string> {
    const transactionId = `tx-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    
    const context: TransactionContext = {
      transactionId,
      operations,
      metadata,
      createdAt: new Date().toISOString()
    }

    this.activeTransactions.set(transactionId, context)
    
    console.log(`🔄 [TransactionManager] 开始事务: ${transactionId}, 操作数量: ${operations.length}`)
    
    return transactionId
  }

  /**
   * 执行事务
   * 按顺序执行所有操作，支持原子性和回滚
   */
  static async executeTransaction(
    transactionId: string,
    config: Partial<TransactionConfig> = {}
  ): Promise<TransactionResult> {
    const finalConfig = { ...this.DEFAULT_CONFIG, ...config }
    const context = this.activeTransactions.get(transactionId)

    if (!context) {
      throw new Error(`事务不存在: ${transactionId}`)
    }

    const result: TransactionResult = {
      success: false,
      transactionId,
      completedOperations: 0,
      totalOperations: context.operations.length,
      errors: []
    }

    const rollbackData: any[] = []

    try {
      // 设置超时
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('事务执行超时')), finalConfig.timeoutMs)
      })

      const executionPromise = this.executeOperations(context.operations, rollbackData, finalConfig)

      await Promise.race([executionPromise, timeoutPromise])

      result.completedOperations = context.operations.length
      result.success = true

      // 触发数据同步事件
      if (finalConfig.enableDataSync) {
        await this.triggerDataSyncEvents(context)
      }

      if (finalConfig.enableLogging) {
        console.log(`✅ [TransactionManager] 事务执行成功: ${transactionId}`)
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      result.errors.push(errorMessage)

      if (finalConfig.enableLogging) {
        console.error(`❌ [TransactionManager] 事务执行失败: ${transactionId}`, error)
      }

      // 执行回滚
      if (finalConfig.enableRollback && rollbackData.length > 0) {
        try {
          await this.executeRollback(rollbackData, finalConfig)
          if (finalConfig.enableLogging) {
            console.log(`🔄 [TransactionManager] 事务回滚成功: ${transactionId}`)
          }
        } catch (rollbackError) {
          const rollbackErrorMessage = rollbackError instanceof Error ? rollbackError.message : '回滚失败'
          result.errors.push(`回滚失败: ${rollbackErrorMessage}`)
          if (finalConfig.enableLogging) {
            console.error(`❌ [TransactionManager] 事务回滚失败: ${transactionId}`, rollbackError)
          }
        }
      }

      result.rollbackData = rollbackData
    } finally {
      // 清理事务上下文
      this.activeTransactions.delete(transactionId)
    }

    return result
  }

  /**
   * 执行操作列表
   */
  private static async executeOperations(
    operations: TransactionOperation[],
    rollbackData: any[],
    config: TransactionConfig
  ): Promise<void> {
    for (const operation of operations) {
      await this.executeOperation(operation, rollbackData, config)
    }
  }

  /**
   * 执行单个操作
   */
  private static async executeOperation(
    operation: TransactionOperation,
    rollbackData: any[],
    config: TransactionConfig
  ): Promise<void> {
    const { type, entity, entityId, data, originalData } = operation

    try {
      switch (entity) {
        case 'salesOrder':
          await this.executeSalesOrderOperation(type, entityId, data, originalData, rollbackData)
          break
        case 'productionOrder':
          await this.executeProductionOrderOperation(type, entityId, data, originalData, rollbackData)
          break
        case 'workOrder':
          await this.executeWorkOrderOperation(type, entityId, data, originalData, rollbackData)
          break
        default:
          throw new Error(`不支持的实体类型: ${entity}`)
      }

      if (config.enableLogging) {
        console.log(`✅ [TransactionManager] 操作执行成功: ${operation.id} (${type} ${entity})`)
      }

    } catch (error) {
      if (config.enableLogging) {
        console.error(`❌ [TransactionManager] 操作执行失败: ${operation.id}`, error)
      }
      throw error
    }
  }

  /**
   * 执行销售订单操作
   */
  private static async executeSalesOrderOperation(
    type: string,
    entityId: string,
    data: any,
    originalData: any,
    rollbackData: any[]
  ): Promise<void> {
    switch (type) {
      case 'update':
        const updateResponse = await dataAccessManager.orders.update(entityId, data)
        if (updateResponse.status !== 'success') {
          throw new Error(`销售订单更新失败: ${updateResponse.message}`)
        }
        // 保存回滚数据
        if (originalData) {
          rollbackData.push({
            type: 'update',
            entity: 'salesOrder',
            entityId,
            data: originalData
          })
        }
        break
      case 'delete':
        // 销售订单删除逻辑（如果需要）
        throw new Error('销售订单删除操作暂不支持')
      default:
        throw new Error(`不支持的销售订单操作: ${type}`)
    }
  }

  /**
   * 执行生产订单操作
   */
  private static async executeProductionOrderOperation(
    type: string,
    entityId: string,
    data: any,
    originalData: any,
    rollbackData: any[]
  ): Promise<void> {
    switch (type) {
      case 'update':
        const updateResponse = await dataAccessManager.productionOrders.update(entityId, data)
        if (updateResponse.status !== 'success') {
          throw new Error(`生产订单更新失败: ${updateResponse.message}`)
        }
        // 保存回滚数据
        if (originalData) {
          rollbackData.push({
            type: 'update',
            entity: 'productionOrder',
            entityId,
            data: originalData
          })
        }
        break
      default:
        throw new Error(`不支持的生产订单操作: ${type}`)
    }
  }

  /**
   * 执行工单操作
   */
  private static async executeWorkOrderOperation(
    type: string,
    entityId: string,
    data: any,
    originalData: any,
    rollbackData: any[]
  ): Promise<void> {
    switch (type) {
      case 'update':
        const updateResponse = await dataAccessManager.productionWorkOrders.update(entityId, data)
        if (updateResponse.status !== 'success') {
          throw new Error(`工单更新失败: ${updateResponse.message}`)
        }
        // 保存回滚数据
        if (originalData) {
          rollbackData.push({
            type: 'update',
            entity: 'workOrder',
            entityId,
            data: originalData
          })
        }
        break
      default:
        throw new Error(`不支持的工单操作: ${type}`)
    }
  }

  /**
   * 执行回滚操作
   */
  private static async executeRollback(
    rollbackData: any[],
    config: TransactionConfig
  ): Promise<void> {
    // 按相反顺序执行回滚
    for (let i = rollbackData.length - 1; i >= 0; i--) {
      const rollbackOperation = rollbackData[i]
      
      try {
        switch (rollbackOperation.entity) {
          case 'salesOrder':
            await dataAccessManager.orders.update(rollbackOperation.entityId, rollbackOperation.data)
            break
          case 'productionOrder':
            await dataAccessManager.productionOrders.update(rollbackOperation.entityId, rollbackOperation.data)
            break
          case 'workOrder':
            await dataAccessManager.productionWorkOrders.update(rollbackOperation.entityId, rollbackOperation.data)
            break
        }

        if (config.enableLogging) {
          console.log(`🔄 [TransactionManager] 回滚操作成功: ${rollbackOperation.entity} ${rollbackOperation.entityId}`)
        }

      } catch (error) {
        if (config.enableLogging) {
          console.error(`❌ [TransactionManager] 回滚操作失败: ${rollbackOperation.entity} ${rollbackOperation.entityId}`, error)
        }
        throw error
      }
    }
  }

  /**
   * 触发数据同步事件
   */
  private static async triggerDataSyncEvents(context: TransactionContext): Promise<void> {
    const affectedModules = new Set<string>()

    // 分析受影响的模块
    for (const operation of context.operations) {
      switch (operation.entity) {
        case 'salesOrder':
          affectedModules.add('orders')
          affectedModules.add('production')
          break
        case 'productionOrder':
          affectedModules.add('production')
          affectedModules.add('workOrders')
          break
        case 'workOrder':
          affectedModules.add('workOrders')
          affectedModules.add('scheduling')
          break
      }
    }

    // 触发同步事件
    // eslint-disable-next-line @next/next/no-assign-module-variable
    for (const module of affectedModules) {
      dataSyncService.addChangeEvent({
        module: module,
        entityType: 'transaction',
        entityId: context.transactionId,
        type: 'update', // 使用标准的变更类型
        newData: {
          transactionId: context.transactionId,
          operationCount: context.operations.length,
          affectedEntities: context.operations.map(op => ({
            entity: op.entity,
            entityId: op.entityId,
            type: op.type
          }))
        }
      })
    }
  }

  /**
   * 获取活跃事务统计
   */
  static getActiveTransactionStats(): {
    count: number
    transactions: Array<{
      id: string
      operationCount: number
      createdAt: string
      age: number
    }>
  } {
    const now = new Date().getTime()
    const transactions = Array.from(this.activeTransactions.values()).map(context => ({
      id: context.transactionId,
      operationCount: context.operations.length,
      createdAt: context.createdAt,
      age: now - new Date(context.createdAt).getTime()
    }))

    return {
      count: transactions.length,
      transactions
    }
  }

  /**
   * 清理超时事务
   */
  static cleanupTimeoutTransactions(timeoutMs: number = 300000): number {
    const now = new Date().getTime()
    let cleanedCount = 0

    for (const [transactionId, context] of this.activeTransactions.entries()) {
      const age = now - new Date(context.createdAt).getTime()
      if (age > timeoutMs) {
        this.activeTransactions.delete(transactionId)
        cleanedCount++
        console.warn(`🧹 [TransactionManager] 清理超时事务: ${transactionId}, 年龄: ${age}ms`)
      }
    }

    return cleanedCount
  }

  /**
   * 生成事务ID
   * 用于标识和跟踪事务操作
   */
  static generateTransactionId(): string {
    const timestamp = Date.now()
    const random = Math.random().toString(36).substring(2, 15)
    return `TXN-${timestamp}-${random.toUpperCase()}`
  }
}

/**
 * 事务管理器的便捷方法
 */
export const transactionManager = {
  /**
   * 开始事务
   */
  begin: TransactionManager.beginTransaction,

  /**
   * 执行事务
   */
  execute: TransactionManager.executeTransaction,

  /**
   * 获取统计信息
   */
  getStats: TransactionManager.getActiveTransactionStats,

  /**
   * 清理超时事务
   */
  cleanup: TransactionManager.cleanupTimeoutTransactions,

  /**
   * 生成事务ID
   */
  generateTransactionId: TransactionManager.generateTransactionId
}
