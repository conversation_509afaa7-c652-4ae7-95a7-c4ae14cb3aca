'use client'

import React, { useState, useEffect, useMemo, useCallback } from 'react'
import {
  Table,
  Button,
  Space,
  Tag,
  Typography,
  Card,
  Empty,
  Modal,
  InputNumber,
  Form,
  App
} from 'antd'
import {
  EyeOutlined,
  ReloadOutlined,
  ToolOutlined
} from '@ant-design/icons'
import { ProductionOrder, CustomerLevel } from '@/types'
import { sanitizeProductName, sanitizeCustomerName, sanitizeOrderNumber } from '@/utils/security/htmlSanitizer'
import { ProductionOrderBusinessRules } from '@/utils/business/productionOrderRules'
import { ProductionOrderStatusTag, CreditLevelTag } from '@/components/common/UnifiedTagRenderer'
import dayjs from 'dayjs'

const { Text } = Typography

// 创建工单Modal组件 - 使用React.memo优化性能
const CreateWorkOrderModal: React.FC<{
  open: boolean
  loading: boolean
  selectedOrders: ProductionOrder[]
  onConfirm: (values: any) => Promise<void>
  onCancel: () => void
}> = React.memo(({ open, loading, selectedOrders, onConfirm, onCancel }) => {
  const [form] = Form.useForm()

  // Modal打开时初始化表单
  useEffect(() => {
    if (open) {
      form.setFieldsValue({
        hourlyCapacity: 130 // 默认值
      })
    }
  }, [open, form])

  const handleOk = async () => {
    try {
      const values = await form.validateFields()
      await onConfirm(values)
      form.resetFields()
    } catch (error) {
      // 表单验证失败，不关闭Modal
    }
  }

  const handleCancel = () => {
    form.resetFields()
    onCancel()
  }

  return (
    <Modal
      title="创建生产工单"
      open={open}
      onOk={handleOk}
      onCancel={handleCancel}
      confirmLoading={loading}
      width={600}
      destroyOnHidden
    >
      <div style={{ marginBottom: 16 }}>
        <Text strong>已选择的生产订单：</Text>
        <div style={{ marginTop: 8, maxHeight: 200, overflowY: 'auto' }}>
          {selectedOrders.map(order => (
            <div key={order.id} style={{
              padding: '8px 12px',
              border: '1px solid #d9d9d9',
              borderRadius: 6,
              marginBottom: 8,
              backgroundColor: '#fafafa'
            }}>
              <Space direction="vertical" size={4}>
                <div>
                  <Text strong>订单号：</Text>
                  <Text>{order.orderNumber}</Text>
                </div>
                <div>
                  <Text strong>产品名称：</Text>
                  <Text>{order.productName}</Text>
                </div>
                <div>
                  <Text strong>计划数量：</Text>
                  <Text>{order.plannedQuantity}</Text>
                </div>
                <div>
                  <Text strong>客户：</Text>
                  <Text>{order.customerName}</Text>
                  {order.customerCreditLevel && (
                    <div style={{ marginLeft: 8 }}>
                      <CreditLevelTag level={order.customerCreditLevel} />
                    </div>
                  )}
                </div>
                <div>
                  <Text strong>信用等级：</Text>
                  {order.customerCreditLevel ? (
                    <CreditLevelTag level={order.customerCreditLevel} />
                  ) : (
                    <Tag color="default">未设置</Tag>
                  )}
                </div>
              </Space>
            </div>
          ))}
        </div>
      </div>

      <Form form={form} layout="vertical">
        <Form.Item
          name="hourlyCapacity"
          label="小时产能"
          rules={[
            { required: true, message: '请输入小时产能' },
            { type: 'number', min: 1, message: '产能必须大于0' }
          ]}
        >
          <InputNumber
            style={{ width: '100%' }}
            placeholder="请输入小时产能"
            min={1}
            max={1000}
            precision={0}
            addonAfter="模/小时"
          />
        </Form.Item>
      </Form>
    </Modal>
  )
})

CreateWorkOrderModal.displayName = 'CreateWorkOrderModal'

interface ProductionOrdersListProps {
  orders: ProductionOrder[]
  loading?: boolean
  onRefresh?: () => void
  // onBatchScheduling功能已删除
  onOrderDetail?: (order: ProductionOrder) => void
  onStatusChange?: (orderId: string, status: string) => void
  onOrderSelectionChange?: (orders: ProductionOrder[]) => void
  onCreateWorkOrders?: (orderIds: string[], hourlyCapacity: number) => Promise<void>
  onTabChange?: (key: string) => void
  workstations?: any[]
  currentSplitConfig?: any
  selectedOrdersCount?: number
}

const ProductionOrdersList: React.FC<ProductionOrdersListProps> = React.memo(({
  orders,
  loading = false,
  onRefresh,
  onOrderDetail,
  onStatusChange,
  onOrderSelectionChange,
  onCreateWorkOrders,
  onTabChange,
  workstations = [],
  currentSplitConfig,
  selectedOrdersCount = 0
}) => {
  const { message } = App.useApp()
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])
  const [selectedOrders, setSelectedOrders] = useState<ProductionOrder[]>([])
  const [isCreateWorkOrderModalVisible, setIsCreateWorkOrderModalVisible] = useState(false)
  const [createWorkOrderLoading, setCreateWorkOrderLoading] = useState(false)

  // 使用useCallback优化回调函数
  const handleRefresh = useCallback(() => {
    onRefresh?.()
  }, [onRefresh])

  const handleOrderSelectionChange = useCallback((selectedRows: ProductionOrder[]) => {
    onOrderSelectionChange?.(selectedRows)
  }, [onOrderSelectionChange])

  const handleCreateWorkOrder = useCallback(() => {
    setIsCreateWorkOrderModalVisible(true)
  }, [])

  const handleCreateWorkOrderCancel = useCallback(() => {
    setIsCreateWorkOrderModalVisible(false)
    setCreateWorkOrderLoading(false)
  }, [])

  const handleCreateWorkOrderConfirm = useCallback(async (values: any) => {
    try {
      setCreateWorkOrderLoading(true)
      const orderIds = selectedOrders.map(order => order.id).filter(Boolean) as string[]
      await onCreateWorkOrders?.(orderIds, values.hourlyCapacity)
      setIsCreateWorkOrderModalVisible(false)
      setSelectedRowKeys([])
      setSelectedOrders([])
      handleRefresh()
    } catch (error) {
      console.error('创建工单失败:', error)
    } finally {
      setCreateWorkOrderLoading(false)
    }
  }, [selectedOrders, onCreateWorkOrders, handleRefresh])

  // 使用useMemo优化列配置
  const columns = useMemo(() => [
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <ProductionOrderStatusTag status={status} />
      ),
    },
    {
      title: '生产订单号',
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      width: 150,
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      key: 'productName',
      width: 200,
      render: (productName: string, record: any) => {
        // 如果是共享模具订单，显示实际产品名称组合
        if (record.isSharedMold && record.productItems && record.productItems.length > 0) {
          return (
            <div style={{ lineHeight: '1.4' }}>
              {record.productItems.map((item: any, index: number) => (
                <div key={index} style={{ marginBottom: index < record.productItems.length - 1 ? '2px' : '0' }}>
                  <Text
                    style={{
                      fontSize: '13px',
                      display: 'block',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      maxWidth: '180px'
                    }}
                    title={sanitizeProductName(item.productName)} // 安全的鼠标悬停显示
                  >
                    {sanitizeProductName(item.productName)}
                  </Text>
                </div>
              ))}
            </div>
          )
        }
        // 传统订单保持原有显示方式
        return <Text>{sanitizeProductName(productName)}</Text>
      },
    },
    {
      title: '产品编码',
      dataIndex: 'productCode',
      key: 'productCode',
      width: 120,
      render: (productCode: string, record: any) => {
        // 如果是共享模具订单，显示所有产品编码
        if (record.isSharedMold && record.productItems && record.productItems.length > 0) {
          return (
            <div style={{ lineHeight: '1.4' }}>
              {record.productItems.map((item: any, index: number) => (
                <div key={index} style={{ marginBottom: index < record.productItems.length - 1 ? '2px' : '0' }}>
                  <Text style={{ fontSize: '13px' }}>{item.productCode}</Text>
                </div>
              ))}
            </div>
          )
        }
        // 传统订单保持原有显示方式
        return <Text>{productCode}</Text>
      },
    },
    {
      title: '计划数量',
      dataIndex: 'plannedQuantity',
      key: 'plannedQuantity',
      width: 100,
      render: (quantity: number) => (
        <Text strong>{quantity}</Text>
      ),
    },
    {
      title: '客户信息',
      key: 'customerInfo',
      width: 180,
      render: (_: any, record: ProductionOrder) => (
        <Space direction="vertical" size={2}>
          <Text strong>{record.customerName}</Text>
          {record.customerCreditLevel && (
            <CreditLevelTag level={record.customerCreditLevel} />
          )}
        </Space>
      ),
    },
    {
      title: '信用等级',
      dataIndex: 'customerCreditLevel',
      key: 'customerCreditLevel',
      width: 120,
      render: (creditLevel: CustomerLevel, record: ProductionOrder) => {
        if (!creditLevel) return <Tag color="default">未设置</Tag>

        return (
          <Space direction="vertical" size={2}>
            <CreditLevelTag level={creditLevel} />
            {record.prioritySource === 'manual' && (
              <Tag color="blue" style={{ fontSize: '12px' }}>手动</Tag>
            )}
          </Space>
        )
      },
    },
    {
      title: '交付日期',
      dataIndex: 'deliveryDate',
      key: 'deliveryDate',
      width: 120,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD'),
      sorter: (a: ProductionOrder, b: ProductionOrder) => dayjs(a.deliveryDate).unix() - dayjs(b.deliveryDate).unix(),
    },
    {
      title: '成型模具编号',
      dataIndex: 'formingMoldNumber',
      key: 'formingMoldNumber',
      width: 140,
      render: (moldNumber: string) => moldNumber || '-',
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 140,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm'),
      sorter: (a: ProductionOrder, b: ProductionOrder) => dayjs(a.createdAt).unix() - dayjs(b.createdAt).unix(),
      defaultSortOrder: 'descend' as const,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_: any, record: ProductionOrder) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => onOrderDetail?.(record)}
          >
            详情
          </Button>
        </Space>
      ),
    },
  ], [onOrderDetail, onStatusChange])

  // 使用useMemo优化行选择配置
  const rowSelection = useMemo(() => ({
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[], selectedRows: ProductionOrder[]) => {
      setSelectedRowKeys(newSelectedRowKeys)
      setSelectedOrders(selectedRows)
      handleOrderSelectionChange(selectedRows)
    },
    getCheckboxProps: (record: ProductionOrder) => ({
      // ✅ 使用统一的业务规则验证
      disabled: !ProductionOrderBusinessRules.canCreateWorkOrder(record),
    }),
  }), [selectedRowKeys, handleOrderSelectionChange])

  // 验证选中订单是否可创建工单
  const validateSelectedOrders = useCallback(() => {
    if (selectedOrders.length === 0) {
      message.warning('请选择要创建工单的生产订单')
      return false
    }

    // ✅ 使用统一的业务规则检查订单状态
    const invalidOrders = selectedOrders.filter(order => !ProductionOrderBusinessRules.canCreateWorkOrder(order))
    if (invalidOrders.length > 0) {
      message.error('只能为"计划中"状态的订单创建工单')
      return false
    }

    return true
  }, [selectedOrders, message])

  // 处理创建工单按钮点击
  const handleCreateWorkOrderClick = useCallback(() => {
    if (validateSelectedOrders()) {
      setIsCreateWorkOrderModalVisible(true)
    }
  }, [validateSelectedOrders])

  return (
    <Card
      title={
        <Space>
          <span>生产订单列表</span>
          <Text type="secondary">({orders.length} 个订单)</Text>
        </Space>
      }
      extra={
        <Space>
          <Button
            type="primary"
            icon={<ToolOutlined />}
            onClick={handleCreateWorkOrderClick}
            disabled={selectedOrders.length === 0}
          >
            创建工单
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
            loading={loading}
          >
            刷新
          </Button>
        </Space>
      }
    >
      {orders.length === 0 ? (
        <Empty
          description={
            <div>
              <div>暂无生产订单</div>
              <div style={{ fontSize: '12px', color: '#666', marginTop: '8px' }}>
                生产订单只能通过MRP流程创建，请前往销售订单管理页面启动MRP
              </div>
            </div>
          }
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      ) : (
        <Table
          rowSelection={rowSelection}
          columns={columns}
          dataSource={orders}
          rowKey="id"
          loading={loading}

          pagination={{
            total: orders.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          scroll={{ x: 1140 }}
        />
      )}

      {/* 创建工单Modal */}
      <CreateWorkOrderModal
        open={isCreateWorkOrderModalVisible}
        loading={createWorkOrderLoading}
        selectedOrders={selectedOrders}
        onConfirm={handleCreateWorkOrderConfirm}
        onCancel={handleCreateWorkOrderCancel}
      />
    </Card>
  )
})

ProductionOrdersList.displayName = 'ProductionOrdersList'

export default ProductionOrdersList
