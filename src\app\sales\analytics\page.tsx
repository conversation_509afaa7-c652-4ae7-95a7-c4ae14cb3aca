'use client'

import React, { useState, useEffect } from 'react'
import {
  Card,
  Row,
  Col,
  Statistic,
  Select,
  DatePicker,
  Table,
  Progress,
  Alert,
  Tag,
  Tooltip,
  Button
} from 'antd'
import { 
  TrophyOutlined,
  DollarOutlined,
  UserOutlined,
  ShoppingCartOutlined,
  RiseOutlined,
  FallOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExportOutlined,
  ReloadOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import dayjs, { Dayjs } from 'dayjs'

const { Option } = Select
const { RangePicker } = DatePicker

interface ProductProfitAnalysis {
  productModelCode: string
  productName: string
  totalSales: number
  totalCost: number
  profit: number
  profitMargin: number
  salesVolume: number
  averagePrice: number
  trend: 'up' | 'down' | 'stable'
}

interface CustomerValueAnalysis {
  customerId: string
  customerName: string
  totalOrders: number
  totalAmount: number
  averageOrderValue: number
  lastOrderDate: string
  customerLevel: 'A' | 'B' | 'C'
  lifetimeValue: number
  riskLevel: 'low' | 'medium' | 'high'
}

interface DeliveryPerformance {
  month: string
  totalOrders: number
  onTimeDeliveries: number
  delayedDeliveries: number
  onTimeRate: number
  averageDelay: number
}

const SalesAnalytics: React.FC = () => {
  const [dateRange, setDateRange] = useState<[Dayjs, Dayjs]>([
    dayjs().subtract(30, 'day'),
    dayjs()
  ])
  const [productAnalysis, setProductAnalysis] = useState<ProductProfitAnalysis[]>([])
  const [customerAnalysis, setCustomerAnalysis] = useState<CustomerValueAnalysis[]>([])
  const [deliveryPerformance, setDeliveryPerformance] = useState<DeliveryPerformance[]>([])
  const [salesTrend, setSalesTrend] = useState<any[]>([])
  const [loading, setLoading] = useState(false)

  // 模拟数据
  useEffect(() => {
    const mockProductAnalysis: ProductProfitAnalysis[] = [
      {
        productModelCode: 'CP-202',
        productName: '圆形餐盘202mm',
        totalSales: 125000,
        totalCost: 87500,
        profit: 37500,
        profitMargin: 30,
        salesVolume: 1000000,
        averagePrice: 0.125,
        trend: 'up'
      },
      {
        productModelCode: 'CP-201',
        productName: '方形餐盒180mm',
        totalSales: 82450,
        totalCost: 61837.5,
        profit: 20612.5,
        profitMargin: 25,
        salesVolume: 500000,
        averagePrice: 0.165,
        trend: 'stable'
      },
      {
        productModelCode: 'CP-203',
        productName: '长方形餐盒220mm',
        totalSales: 45000,
        totalCost: 36000,
        profit: 9000,
        profitMargin: 20,
        salesVolume: 200000,
        averagePrice: 0.225,
        trend: 'down'
      }
    ]

    const mockCustomerAnalysis: CustomerValueAnalysis[] = [
      {
        customerId: '1',
        customerName: '上海包装材料有限公司',
        totalOrders: 15,
        totalAmount: 1200000,
        averageOrderValue: 80000,
        lastOrderDate: '2024-01-26',
        customerLevel: 'A',
        lifetimeValue: 2400000,
        riskLevel: 'low'
      },
      {
        customerId: '2',
        customerName: '北京绿色包装科技公司',
        totalOrders: 8,
        totalAmount: 650000,
        averageOrderValue: 81250,
        lastOrderDate: '2024-01-28',
        customerLevel: 'B',
        lifetimeValue: 1300000,
        riskLevel: 'medium'
      },
      {
        customerId: '3',
        customerName: '广州环保餐具厂',
        totalOrders: 3,
        totalAmount: 135000,
        averageOrderValue: 45000,
        lastOrderDate: '2024-01-20',
        customerLevel: 'C',
        lifetimeValue: 270000,
        riskLevel: 'high'
      }
    ]

    const mockDeliveryPerformance: DeliveryPerformance[] = [
      {
        month: '2024-01',
        totalOrders: 25,
        onTimeDeliveries: 22,
        delayedDeliveries: 3,
        onTimeRate: 88,
        averageDelay: 2.5
      },
      {
        month: '2023-12',
        totalOrders: 30,
        onTimeDeliveries: 27,
        delayedDeliveries: 3,
        onTimeRate: 90,
        averageDelay: 1.8
      },
      {
        month: '2023-11',
        totalOrders: 28,
        onTimeDeliveries: 25,
        delayedDeliveries: 3,
        onTimeRate: 89.3,
        averageDelay: 2.1
      }
    ]

    const mockSalesTrend = [
      { month: '2023-07', sales: 450000, orders: 18 },
      { month: '2023-08', sales: 520000, orders: 22 },
      { month: '2023-09', sales: 480000, orders: 19 },
      { month: '2023-10', sales: 650000, orders: 26 },
      { month: '2023-11', sales: 720000, orders: 28 },
      { month: '2023-12', sales: 850000, orders: 30 },
      { month: '2024-01', sales: 750000, orders: 25 }
    ]

    setProductAnalysis(mockProductAnalysis)
    setCustomerAnalysis(mockCustomerAnalysis)
    setDeliveryPerformance(mockDeliveryPerformance)
    setSalesTrend(mockSalesTrend)
  }, [])

  // 产品盈利分析表格列
  const productColumns: ColumnsType<ProductProfitAnalysis> = [
    {
      title: '产品型号',
      dataIndex: 'productModelCode',
      key: 'productModelCode',
      width: 120
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      key: 'productName',
      width: 180
    },
    {
      title: '销售额',
      dataIndex: 'totalSales',
      key: 'totalSales',
      width: 120,
      render: (value: number) => `¥${value.toLocaleString()}`,
      sorter: (a, b) => a.totalSales - b.totalSales
    },
    {
      title: '利润',
      dataIndex: 'profit',
      key: 'profit',
      width: 120,
      render: (value: number) => (
        <span style={{ color: value > 0 ? '#3f8600' : '#cf1322' }}>
          ¥{value.toLocaleString()}
        </span>
      ),
      sorter: (a, b) => a.profit - b.profit
    },
    {
      title: '利润率',
      dataIndex: 'profitMargin',
      key: 'profitMargin',
      width: 100,
      render: (value: number) => (
        <span style={{ color: value >= 25 ? '#3f8600' : value >= 15 ? '#faad14' : '#cf1322' }}>
          {value}%
        </span>
      ),
      sorter: (a, b) => a.profitMargin - b.profitMargin
    },
    {
      title: '销量',
      dataIndex: 'salesVolume',
      key: 'salesVolume',
      width: 120,
      render: (value: number) => `${(value / 10000).toFixed(1)}万个`
    },
    {
      title: '趋势',
      dataIndex: 'trend',
      key: 'trend',
      width: 80,
      render: (trend: string) => {
        const trendMap = {
          'up': { icon: <RiseOutlined />, color: '#3f8600' },
          'down': { icon: <FallOutlined />, color: '#cf1322' },
          'stable': { icon: <ClockCircleOutlined />, color: '#faad14' }
        }
        const config = trendMap[trend as keyof typeof trendMap]
        return <span style={{ color: config.color }}>{config.icon}</span>
      }
    }
  ]

  // 客户价值分析表格列
  const customerColumns: ColumnsType<CustomerValueAnalysis> = [
    {
      title: '客户名称',
      dataIndex: 'customerName',
      key: 'customerName',
      width: 200,
      ellipsis: true
    },
    {
      title: '客户等级',
      dataIndex: 'customerLevel',
      key: 'customerLevel',
      width: 100,
      render: (level: string) => {
        const colorMap = { 'A': 'gold', 'B': 'blue', 'C': 'green' }
        return <Tag color={colorMap[level as keyof typeof colorMap]}>{level}级</Tag>
      }
    },
    {
      title: '订单数',
      dataIndex: 'totalOrders',
      key: 'totalOrders',
      width: 100,
      sorter: (a, b) => a.totalOrders - b.totalOrders
    },
    {
      title: '总金额',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      width: 120,
      render: (value: number) => `¥${value.toLocaleString()}`,
      sorter: (a, b) => a.totalAmount - b.totalAmount
    },
    {
      title: '平均订单价值',
      dataIndex: 'averageOrderValue',
      key: 'averageOrderValue',
      width: 120,
      render: (value: number) => `¥${value.toLocaleString()}`
    },
    {
      title: '生命周期价值',
      dataIndex: 'lifetimeValue',
      key: 'lifetimeValue',
      width: 120,
      render: (value: number) => `¥${value.toLocaleString()}`,
      sorter: (a, b) => a.lifetimeValue - b.lifetimeValue
    },
    {
      title: '风险等级',
      dataIndex: 'riskLevel',
      key: 'riskLevel',
      width: 100,
      render: (level: string) => {
        const colorMap = { 'low': 'green', 'medium': 'orange', 'high': 'red' }
        const textMap = { 'low': '低', 'medium': '中', 'high': '高' }
        return <Tag color={colorMap[level as keyof typeof colorMap]}>{textMap[level as keyof typeof textMap]}</Tag>
      }
    },
    {
      title: '最后订单',
      dataIndex: 'lastOrderDate',
      key: 'lastOrderDate',
      width: 120
    }
  ]

  // 销售趋势图配置
  const salesTrendConfig = {
    data: salesTrend,
    xField: 'month',
    yField: 'sales',
    point: {
      size: 5,
      shape: 'diamond',
    },
    label: {
      style: {
        fill: '#aaa',
      },
    },
    tooltip: {
      formatter: (datum: any) => {
        return {
          name: '销售额',
          value: `¥${datum.sales.toLocaleString()}`
        }
      }
    }
  }

  // 产品销售占比图配置
  const productPieConfig = {
    data: productAnalysis.map(item => ({
      type: item.productName,
      value: item.totalSales
    })),
    angleField: 'value',
    colorField: 'type',
    radius: 0.8,
    label: {
      type: 'outer',
      content: '{name} {percentage}',
    },
    interactions: [
      {
        type: 'element-active',
      },
    ],
  }

  // 交期达成率图配置
  const deliveryConfig = {
    data: deliveryPerformance,
    xField: 'month',
    yField: 'onTimeRate',
    columnWidthRatio: 0.6,
    label: {
      position: 'middle' as const,
      style: {
        fill: '#FFFFFF',
        opacity: 0.6,
      },
    },
    tooltip: {
      formatter: (datum: any) => {
        return {
          name: '准时交付率',
          value: `${datum.onTimeRate}%`
        }
      }
    }
  }

  // 统计数据
  const totalSales = productAnalysis.reduce((sum, item) => sum + item.totalSales, 0)
  const totalProfit = productAnalysis.reduce((sum, item) => sum + item.profit, 0)
  const averageProfitMargin = productAnalysis.length > 0 ? 
    Math.round(productAnalysis.reduce((sum, item) => sum + item.profitMargin, 0) / productAnalysis.length) : 0
  const averageDeliveryRate = deliveryPerformance.length > 0 ?
    Math.round(deliveryPerformance.reduce((sum, item) => sum + item.onTimeRate, 0) / deliveryPerformance.length) : 0

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: '24px' }}>
        <h1 style={{ fontSize: '24px', fontWeight: 'bold', margin: '0 0 8px 0' }}>销售决策分析中心</h1>
        <p style={{ color: '#666', margin: 0 }}>产品盈利分析、客户价值分析、交期达成分析、预警看板</p>
      </div>

      {/* 时间选择和操作 */}
      <Card>
        <Row justify="space-between" align="middle" gutter={[16, 16]}>
          <Col xs={24} lg={18}>
            <Row gutter={[16, 16]}>
            <RangePicker
              value={dateRange}
              onChange={(dates) => dates && setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs])}
            />
            <Select defaultValue="all" style={{ width: 120 }}>
              <Option value="all">全部产品</Option>
              <Option value="CP-201">CP-201</Option>
              <Option value="CP-202">CP-202</Option>
              <Option value="CP-203">CP-203</Option>
            </Select>
            </Row>
          </Col>
          <Col xs={24} lg={6}>
            <Row gutter={[8, 8]} justify="end">
              <Col>
                <Button icon={<ReloadOutlined />} loading={loading}>
                  刷新数据
                </Button>
              </Col>
              <Col>
                <Button icon={<ExportOutlined />}>
                  导出报告
                </Button>
              </Col>
            </Row>
          </Col>
        </Row>
      </Card>

      {/* 核心指标卡片 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="总销售额"
              value={totalSales}
              precision={0}
              prefix="¥"
              valueStyle={{ color: '#3f8600' }}
              suffix={
                <Tooltip title="较上月增长12%">
                  <RiseOutlined style={{ color: '#3f8600' }} />
                </Tooltip>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="总利润"
              value={totalProfit}
              precision={0}
              prefix="¥"
              valueStyle={{ color: '#1890ff' }}
              suffix={
                <Tooltip title="较上月增长8%">
                  <RiseOutlined style={{ color: '#1890ff' }} />
                </Tooltip>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="平均利润率"
              value={averageProfitMargin}
              suffix="%"
              valueStyle={{ color: averageProfitMargin >= 25 ? '#3f8600' : '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="交期达成率"
              value={averageDeliveryRate}
              suffix="%"
              valueStyle={{ color: averageDeliveryRate >= 90 ? '#3f8600' : '#cf1322' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 预警看板 */}
      <Card title="预警看板">
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={8}>
            <Alert
              message="利润率预警"
              description="CP-203产品利润率仅20%，低于目标25%"
              type="warning"
              showIcon
              icon={<WarningOutlined />}
              action={
                <Button size="small" type="link">
                  查看详情
                </Button>
              }
            />
          </Col>
          <Col xs={24} sm={8}>
            <Alert
              message="客户风险预警"
              description="广州环保餐具厂风险等级高，建议加强跟踪"
              type="error"
              showIcon
              icon={<WarningOutlined />}
              action={
                <Button size="small" type="link">
                  查看详情
                </Button>
              }
            />
          </Col>
          <Col xs={24} sm={8}>
            <Alert
              message="交期达成良好"
              description="本月交期达成率88%，符合预期"
              type="success"
              showIcon
              icon={<CheckCircleOutlined />}
            />
          </Col>
        </Row>
      </Card>

      {/* 销售趋势图 */}
      <Card title="销售趋势分析">
        <div style={{
          height: '256px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#f5f5f5',
          borderRadius: '6px'
        }}>
          <div style={{ textAlign: 'center' }}>
            <div style={{ color: '#666', marginBottom: '8px' }}>销售趋势图表</div>
            <div style={{ fontSize: '14px', color: '#999' }}>图表组件暂未配置</div>
          </div>
        </div>
      </Card>

      {/* 产品和客户分析 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="产品销售占比">
            <div style={{
              height: '256px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: '#f5f5f5',
              borderRadius: '6px'
            }}>
              <div style={{ textAlign: 'center' }}>
                <div style={{ color: '#666', marginBottom: '8px' }}>产品销售占比图</div>
                <div style={{ fontSize: '14px', color: '#999' }}>图表组件暂未配置</div>
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="交期达成率趋势">
            <div style={{
              height: '256px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: '#f5f5f5',
              borderRadius: '6px'
            }}>
              <div style={{ textAlign: 'center' }}>
                <div style={{ color: '#666', marginBottom: '8px' }}>交期达成率趋势图</div>
                <div style={{ fontSize: '14px', color: '#999' }}>图表组件暂未配置</div>
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 产品盈利分析 */}
      <Card title="产品盈利分析">
        <Table
          columns={productColumns}
          dataSource={productAnalysis}
          rowKey="productModelCode"
          pagination={false}
          size="small"
        />
      </Card>

      {/* 客户价值分析 */}
      <Card title="客户价值分析">
        <Table
          columns={customerColumns}
          dataSource={customerAnalysis}
          rowKey="customerId"
          pagination={false}
          size="small"
        />
      </Card>

      {/* 交期达成分析 */}
      <Card title="交期达成分析">
        <Row gutter={[16, 16]}>
          {deliveryPerformance.map((item, index) => (
            <Col xs={24} sm={8} key={index}>
              <Card size="small">
                <Statistic
                  title={`${item.month} 交期达成`}
                  value={item.onTimeRate}
                  suffix="%"
                  valueStyle={{
                    color: item.onTimeRate >= 90 ? '#3f8600' :
                           item.onTimeRate >= 80 ? '#faad14' : '#cf1322'
                  }}
                />
                <div style={{ marginTop: 8 }}>
                  <Progress
                    percent={item.onTimeRate}
                    strokeColor={
                      item.onTimeRate >= 90 ? '#52c41a' :
                      item.onTimeRate >= 80 ? '#faad14' : '#ff4d4f'
                    }
                    showInfo={false}
                  />
                  <div style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>
                    准时: {item.onTimeDeliveries} | 延期: {item.delayedDeliveries}
                  </div>
                  <div style={{ fontSize: '12px', color: '#666' }}>
                    平均延期: {item.averageDelay}天
                  </div>
                </div>
              </Card>
            </Col>
          ))}
        </Row>
      </Card>

      {/* 关键指标总结 */}
      <Card title="关键指标总结">
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={6}>
            <div style={{ textAlign: 'center' }}>
              <TrophyOutlined style={{ fontSize: '24px', color: '#faad14' }} />
              <div style={{ marginTop: 8, fontWeight: 'bold' }}>最佳产品</div>
              <div>CP-202 (30%利润率)</div>
            </div>
          </Col>
          <Col xs={24} sm={6}>
            <div style={{ textAlign: 'center' }}>
              <UserOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
              <div style={{ marginTop: 8, fontWeight: 'bold' }}>最佳客户</div>
              <div>上海包装材料</div>
            </div>
          </Col>
          <Col xs={24} sm={6}>
            <div style={{ textAlign: 'center' }}>
              <ShoppingCartOutlined style={{ fontSize: '24px', color: '#52c41a' }} />
              <div style={{ marginTop: 8, fontWeight: 'bold' }}>订单增长</div>
              <div>+12% 环比</div>
            </div>
          </Col>
          <Col xs={24} sm={6}>
            <div style={{ textAlign: 'center' }}>
              <DollarOutlined style={{ fontSize: '24px', color: '#722ed1' }} />
              <div style={{ marginTop: 8, fontWeight: 'bold' }}>收入增长</div>
              <div>+8% 环比</div>
            </div>
          </Col>
        </Row>
      </Card>
    </div>
  )
}

export default SalesAnalytics
