/** @type {import('next').NextConfig} */
const nextConfig = {
  // appDir is now stable in Next.js 14, no need for experimental flag
  transpilePackages: ['antd'],
  compiler: {
    styledComponents: true,
  },
  // 暂时禁用 React StrictMode 以避免 Ant Design 的 findDOMNode 警告
  // 这是 Ant Design 在 React 18 StrictMode 下的已知问题
  reactStrictMode: false,

  // Webpack配置
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // CSS Modules配置
    config.module.rules.push({
      test: /\.module\.css$/,
      use: [
        defaultLoaders.babel,
        {
          loader: 'css-loader',
          options: {
            modules: {
              localIdentName: dev
                ? '[name]__[local]___[hash:base64:5]'
                : '[hash:base64:8]',
              exportLocalsConvention: 'camelCase',
            },
            importLoaders: 1,
          },
        },
      ],
    })

    return config
  },
}

module.exports = nextConfig
