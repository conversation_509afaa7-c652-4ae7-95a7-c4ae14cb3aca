"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/sales/orders/page",{

/***/ "(app-pages-browser)/./src/app/sales/orders/page.tsx":
/*!***************************************!*\
  !*** ./src/app/sales/orders/page.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ OrderManagementPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/date-picker/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/steps/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/app/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tag/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tooltip/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/badge/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/popconfirm/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input-number/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/row/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/col/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/statistic/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/alert/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/table/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_ExportOutlined_PlusOutlined_SearchOutlined_SwapOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,ExportOutlined,PlusOutlined,SearchOutlined,SwapOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/SwapOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_ExportOutlined_PlusOutlined_SearchOutlined_SwapOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,ExportOutlined,PlusOutlined,SearchOutlined,SwapOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_ExportOutlined_PlusOutlined_SearchOutlined_SwapOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,ExportOutlined,PlusOutlined,SearchOutlined,SwapOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/SearchOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_ExportOutlined_PlusOutlined_SearchOutlined_SwapOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,ExportOutlined,PlusOutlined,SearchOutlined,SwapOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ExportOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_ExportOutlined_PlusOutlined_SearchOutlined_SwapOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,ExportOutlined,PlusOutlined,SearchOutlined,SwapOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlusOutlined.js\");\n/* harmony import */ var _services_dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/dataAccess/DataAccessManager */ \"(app-pages-browser)/./src/services/dataAccess/DataAccessManager.ts\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_sales_AddOrderModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/sales/AddOrderModal */ \"(app-pages-browser)/./src/components/sales/AddOrderModal.tsx\");\n/* harmony import */ var _hooks_useProductionOrders__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./hooks/useProductionOrders */ \"(app-pages-browser)/./src/app/sales/orders/hooks/useProductionOrders.ts\");\n/* harmony import */ var _services_OrderCancellationService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/services/OrderCancellationService */ \"(app-pages-browser)/./src/services/OrderCancellationService.ts\");\n/* harmony import */ var _services_OrderQuantityChangeService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/services/OrderQuantityChangeService */ \"(app-pages-browser)/./src/services/OrderQuantityChangeService.ts\");\n/* harmony import */ var _services_OrderDeliveryDateChangeService__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/services/OrderDeliveryDateChangeService */ \"(app-pages-browser)/./src/services/OrderDeliveryDateChangeService.ts\");\n/* harmony import */ var _hooks_useDebouncedCallback__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/hooks/useDebouncedCallback */ \"(app-pages-browser)/./src/hooks/useDebouncedCallback.ts\");\n/* harmony import */ var _hooks_useEventListener__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/hooks/useEventListener */ \"(app-pages-browser)/./src/hooks/useEventListener.ts\");\n/* harmony import */ var _hooks_useDataAccessMonitor__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/hooks/useDataAccessMonitor */ \"(app-pages-browser)/./src/hooks/useDataAccessMonitor.ts\");\n/* harmony import */ var _hooks_useOrdersData__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/hooks/useOrdersData */ \"(app-pages-browser)/./src/hooks/useOrdersData.ts\");\n/* harmony import */ var _utils_architectureCompliance__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/utils/architectureCompliance */ \"(app-pages-browser)/./src/utils/architectureCompliance.ts\");\n/* harmony import */ var _services_validation_OrderValidationService__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/services/validation/OrderValidationService */ \"(app-pages-browser)/./src/services/validation/OrderValidationService.ts\");\n/* harmony import */ var _components_common_OrderDetailModal__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/common/OrderDetailModal */ \"(app-pages-browser)/./src/components/common/OrderDetailModal/index.ts\");\n/* harmony import */ var _components_common_OrderDetailModal_configs_salesOrderConfig__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/common/OrderDetailModal/configs/salesOrderConfig */ \"(app-pages-browser)/./src/components/common/OrderDetailModal/configs/salesOrderConfig.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// 移除useSalesStore依赖，使用dataAccessManager统一数据访问\n\n\n\n// 🔧 新增：导入生产订单相关组件和Hook\n\n\n\n\n// ✅ 架构合规：使用DataAccessManager统一监控和合规的Hooks\n\n\n\n\n// ✅ 架构合规性验证工具\n\n// 🔧 P4-3数据验证统一：导入统一验证服务\n\n// 🔧 P5-1订单详情组件重构：导入通用订单详情组件\n\n\nconst { Option } = _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"];\nconst { TextArea } = _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"];\nconst { RangePicker } = _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"];\nconst { Step } = _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"];\nconst OrderManagement = ()=>{\n    _s();\n    const { message, modal } = _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"].useApp();\n    // MRP相关状态保留用于UI显示\n    const [mrpExecuting, setMRPExecuting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDetailModalVisible, setIsDetailModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isChangeModalVisible, setIsChangeModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAddOrderModalVisible, setIsAddOrderModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedOrder, setSelectedOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedChangeType, setSelectedChangeType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Form实例 - 用于订单变更\n    const [changeForm] = _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"].useForm();\n    // ✅ 架构合规：使用useOrdersData Hook替代手动数据加载\n    const { orders: ordersFromHook, loading: ordersLoading, error: ordersError, refreshOrders, loadOrders, hasOrders, isEmpty } = (0,_hooks_useOrdersData__WEBPACK_IMPORTED_MODULE_13__.useOrdersData)({\n        autoLoad: true,\n        enableCache: true\n    });\n    // 保持原有的orders状态以兼容现有代码\n    const [orders, setOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 同步Hook数据到本地状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setOrders(ordersFromHook);\n    }, [\n        ordersFromHook\n    ]);\n    // 显示错误信息\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (ordersError) {\n            message.error(ordersError);\n        }\n    }, [\n        ordersError,\n        message\n    ]);\n    // ✅ 架构合规性检查功能（仅开发环境）\n    const handleArchitectureComplianceCheck = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDD0D 开始执行架构合规性检查...\");\n            const result = await (0,_utils_architectureCompliance__WEBPACK_IMPORTED_MODULE_14__.performArchitectureComplianceCheck)();\n            (0,_utils_architectureCompliance__WEBPACK_IMPORTED_MODULE_14__.printComplianceReport)(result);\n            if (result.compliant) {\n                message.success(\"架构合规性检查通过！评分: \".concat(result.score, \"/100\"));\n            } else {\n                message.warning(\"架构合规性检查未通过，评分: \".concat(result.score, \"/100，请查看控制台详情\"));\n            }\n        } catch (error) {\n            console.error(\"架构合规性检查失败:\", error);\n            message.error(\"架构合规性检查失败\");\n        }\n    };\n    // 🔧 P4-2架构升级：使用数据变更监听器自动刷新数据\n    (0,_hooks_useEventListener__WEBPACK_IMPORTED_MODULE_11__.useDataChangeListener)(\"sales-orders-page\", {\n        onOrderCreated: ()=>{\n            console.log(\"\\uD83D\\uDCDD 检测到销售订单创建，自动刷新数据\");\n            refreshOrders();\n        },\n        onOrderUpdated: ()=>{\n            console.log(\"\\uD83D\\uDCDD 检测到销售订单更新，自动刷新数据\");\n            refreshOrders();\n        },\n        onOrderDeleted: ()=>{\n            console.log(\"\\uD83D\\uDCDD 检测到销售订单删除，自动刷新数据\");\n            refreshOrders();\n        }\n    });\n    // 🔧 P4-2架构升级：优化搜索性能，使用防抖搜索\n    const [searchText, setSearchText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchInput, setSearchInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\") // 输入框的即时值\n    ;\n    const [filterStatus, setFilterStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [filterProductionStatus, setFilterProductionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 🔧 P4-2架构升级：使用防抖搜索优化性能\n    const debouncedSearch = (0,_hooks_useDebouncedCallback__WEBPACK_IMPORTED_MODULE_10__.useDebouncedSearch)((query)=>{\n        setSearchText(query);\n    }, 300, []);\n    // ✅ 架构合规：使用DataAccessManager统一监控体系\n    const { metrics, cacheStats, isMonitoring, clearCache, getPerformanceAlerts, formatMemorySize, formatPercentage, isHealthy, needsOptimization } = (0,_hooks_useDataAccessMonitor__WEBPACK_IMPORTED_MODULE_12__.useDataAccessMonitor)({\n        interval: 60000,\n        enabled: true,\n        showDetails: \"development\" === \"development\"\n    });\n    // 批量操作相关状态\n    const [selectedRowKeys, setSelectedRowKeys] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedOrders, setSelectedOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [batchLoading, setBatchLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // MRP相关状态 - 仅保留UI显示需要的\n    const [mrpResult, setMrpResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showMRPResult, setShowMRPResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mrpExecutionStep, setMrpExecutionStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [mrpExecutionSteps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        \"启动MRP\",\n        \"MRP分析\",\n        \"生成生产订单\",\n        \"完成\"\n    ]);\n    const [productInventory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // 🔧 新增：获取选中订单的历史生产订单\n    const { productionOrders: historicalProductionOrders, loading: productionOrdersLoading } = (0,_hooks_useProductionOrders__WEBPACK_IMPORTED_MODULE_6__.useMRPProductionOrders)((selectedOrder === null || selectedOrder === void 0 ? void 0 : selectedOrder.orderNumber) || \"\");\n    // API响应处理函数\n    const handleApiResponse = async (apiCall, operationName)=>{\n        try {\n            const response = await apiCall();\n            if (response.status === \"success\" && response.data) {\n                return response.data;\n            } else {\n                message.error(\"\".concat(operationName, \"失败: \").concat(response.message || \"未知错误\"));\n                return null;\n            }\n        } catch (error) {\n            console.error(\"\".concat(operationName, \"异常:\"), error);\n            message.error(\"\".concat(operationName, \"失败: \").concat(error instanceof Error ? error.message : \"未知错误\"));\n            return null;\n        }\n    };\n    // 获取状态标签\n    const getStatusTag = (status)=>{\n        const statusMap = {\n            \"pending\": {\n                color: \"red\",\n                text: \"未审核\"\n            },\n            \"confirmed\": {\n                color: \"green\",\n                text: \"已审核\"\n            },\n            \"completed\": {\n                color: \"gray\",\n                text: \"完成\"\n            },\n            \"cancelled\": {\n                color: \"orange\",\n                text: \"已取消\"\n            }\n        };\n        const config = statusMap[status] || {\n            color: \"default\",\n            text: \"未知\"\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n            color: config.color,\n            children: config.text\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n            lineNumber: 234,\n            columnNumber: 12\n        }, undefined);\n    };\n    // 获取生产状态标签（与订单状态联动）\n    const getProductionStatusTag = (orderStatus, productionStatus)=>{\n        // 如果订单未审核或已取消，不显示生产状态\n        if (orderStatus === \"pending\" || orderStatus === \"cancelled\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                style: {\n                    color: \"#999\"\n                },\n                children: \"-\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                lineNumber: 241,\n                columnNumber: 14\n            }, undefined);\n        }\n        // 如果订单已审核，显示生产状态（默认为未开始）\n        const actualStatus = orderStatus === \"confirmed\" && !productionStatus ? \"not_started\" : productionStatus;\n        const statusMap = {\n            \"not_started\": {\n                color: \"orange\",\n                text: \"未开始\"\n            },\n            \"pending\": {\n                color: \"blue\",\n                text: \"待生产\"\n            },\n            \"in_progress\": {\n                color: \"green\",\n                text: \"生产中\"\n            },\n            \"completed\": {\n                color: \"cyan\",\n                text: \"已完成\"\n            }\n        };\n        const config = statusMap[actualStatus] || {\n            color: \"orange\",\n            text: \"未开始\"\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n            color: config.color,\n            children: config.text\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n            lineNumber: 254,\n            columnNumber: 12\n        }, undefined);\n    };\n    // 获取MRP状态标签\n    const getMRPStatusTag = (mrpStatus)=>{\n        const statusMap = {\n            \"not_started\": {\n                color: \"default\",\n                text: \"未启动\"\n            },\n            \"in_progress\": {\n                color: \"processing\",\n                text: \"执行中\"\n            },\n            \"completed\": {\n                color: \"success\",\n                text: \"已完成\"\n            },\n            \"failed\": {\n                color: \"error\",\n                text: \"执行失败\"\n            }\n        };\n        const config = statusMap[mrpStatus] || {\n            color: \"default\",\n            text: \"未启动\"\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n            color: config.color,\n            children: config.text\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n            lineNumber: 266,\n            columnNumber: 12\n        }, undefined);\n    };\n    // 获取付款状态标签\n    const getPaymentStatusTag = (status)=>{\n        const statusMap = {\n            \"unpaid\": {\n                color: \"red\",\n                text: \"未付款\"\n            },\n            \"partial\": {\n                color: \"orange\",\n                text: \"部分付款\"\n            },\n            \"paid\": {\n                color: \"green\",\n                text: \"已付款\"\n            }\n        };\n        const config = statusMap[status] || {\n            color: \"default\",\n            text: \"未知\"\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n            color: config.color,\n            children: config.text\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n            lineNumber: 277,\n            columnNumber: 12\n        }, undefined);\n    };\n    // 计算智能交期\n    const calculateDeliveryDate = (productModelCode, quantity)=>{\n        const inventory = productInventory[productModelCode];\n        if (!inventory) return \"待确认\";\n        const { stock, dailyCapacity } = inventory;\n        const needProduction = Math.max(0, quantity - stock);\n        const productionDays = Math.ceil(needProduction / dailyCapacity);\n        const totalDays = productionDays + 3;\n        const deliveryDate = new Date();\n        deliveryDate.setDate(deliveryDate.getDate() + totalDays);\n        return deliveryDate.toISOString().split(\"T\")[0];\n    };\n    const convertUnit = (quantity, fromUnit, toUnit, productModelCode)=>{\n        const defaultWeightPerPiece = 12.0;\n        if (fromUnit === \"个\" && toUnit === \"吨\") {\n            return quantity * defaultWeightPerPiece / 1000000;\n        } else if (fromUnit === \"吨\" && toUnit === \"个\") {\n            return quantity * 1000000 / defaultWeightPerPiece;\n        } else if (fromUnit === \"个\" && toUnit === \"克\") {\n            return quantity * defaultWeightPerPiece;\n        } else if (fromUnit === \"克\" && toUnit === \"个\") {\n            return quantity / defaultWeightPerPiece;\n        }\n        return quantity;\n    };\n    // 表格列定义\n    const columns = [\n        {\n            title: \"销售订单号\",\n            dataIndex: \"orderNumber\",\n            key: \"orderNumber\",\n            width: 140,\n            fixed: \"left\",\n            render: (orderNumber, record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                    type: \"link\",\n                    onClick: ()=>handleViewDetail(record),\n                    style: {\n                        padding: 0,\n                        height: \"auto\",\n                        fontWeight: \"bold\"\n                    },\n                    children: orderNumber\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                    lineNumber: 320,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: \"订单状态\",\n            dataIndex: \"status\",\n            key: \"status\",\n            width: 100,\n            render: (status)=>getStatusTag(status)\n        },\n        {\n            title: \"创建时间\",\n            dataIndex: \"createdAt\",\n            key: \"createdAt\",\n            width: 160,\n            sorter: (a, b)=>new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),\n            defaultSortOrder: \"descend\",\n            render: (createdAt)=>{\n                if (!createdAt) return \"-\";\n                return dayjs__WEBPACK_IMPORTED_MODULE_4___default()(createdAt).format(\"YYYY-MM-DD HH:mm:ss\");\n            }\n        },\n        {\n            title: \"客户名称\",\n            dataIndex: \"customerName\",\n            key: \"customerName\",\n            width: 180,\n            ellipsis: true\n        },\n        {\n            title: \"订单日期\",\n            dataIndex: \"orderDate\",\n            key: \"orderDate\",\n            width: 120,\n            sorter: (a, b)=>new Date(a.orderDate).getTime() - new Date(b.orderDate).getTime()\n        },\n        {\n            title: \"订单金额\",\n            dataIndex: \"finalAmount\",\n            key: \"finalAmount\",\n            width: 150,\n            sorter: (a, b)=>a.finalAmount - b.finalAmount,\n            render: (finalAmount, record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontWeight: \"bold\",\n                                color: \"#1890ff\"\n                            },\n                            children: [\n                                \"\\xa5\",\n                                (finalAmount || 0).toLocaleString(\"zh-CN\", {\n                                    minimumFractionDigits: 2,\n                                    maximumFractionDigits: 2\n                                })\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 370,\n                            columnNumber: 11\n                        }, undefined),\n                        (record.discountAmount || 0) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: \"12px\",\n                                color: \"#666\"\n                            },\n                            children: [\n                                \"折扣: \\xa5\",\n                                (record.discountAmount || 0).toLocaleString(\"zh-CN\", {\n                                    minimumFractionDigits: 2,\n                                    maximumFractionDigits: 2\n                                })\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 374,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                    lineNumber: 369,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: \"生产状态\",\n            dataIndex: \"productionStatus\",\n            key: \"productionStatus\",\n            width: 120,\n            render: (productionStatus, record)=>getProductionStatusTag(record.status, productionStatus)\n        },\n        {\n            title: \"付款状态\",\n            dataIndex: \"paymentStatus\",\n            key: \"paymentStatus\",\n            width: 100,\n            render: (status)=>getPaymentStatusTag(status)\n        },\n        {\n            title: \"变更次数\",\n            key: \"changeCount\",\n            width: 100,\n            render: (_, record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: record.changes.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                        title: \"点击查看变更历史\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                            count: record.changes.length,\n                            size: \"small\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                type: \"link\",\n                                size: \"small\",\n                                children: [\n                                    record.changes.length,\n                                    \"次\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                        lineNumber: 403,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: {\n                            color: \"#666\"\n                        },\n                        children: \"无\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                        lineNumber: 411,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                    lineNumber: 401,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: \"操作\",\n            key: \"action\",\n            width: 180,\n            fixed: \"right\",\n            render: (_, record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                    size: \"small\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                            type: \"link\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_ExportOutlined_PlusOutlined_SearchOutlined_SwapOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 426,\n                                columnNumber: 19\n                            }, void 0),\n                            onClick: ()=>handleOrderChange(record),\n                            children: \"变更\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 424,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                            title: \"确定要删除这个订单吗？\",\n                            onConfirm: ()=>handleDelete(record.id),\n                            okText: \"确定\",\n                            cancelText: \"取消\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                type: \"link\",\n                                danger: true,\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_ExportOutlined_PlusOutlined_SearchOutlined_SwapOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                    lineNumber: 437,\n                                    columnNumber: 46\n                                }, void 0),\n                                children: \"删除\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 437,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 431,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                    lineNumber: 422,\n                    columnNumber: 9\n                }, undefined)\n        }\n    ];\n    // 过滤后的订单数据\n    const filteredOrders = orders.filter((order)=>{\n        const matchesSearch = !searchText || order.orderNumber.toLowerCase().includes(searchText.toLowerCase()) || order.customerName.toLowerCase().includes(searchText.toLowerCase()) || order.customerContact.toLowerCase().includes(searchText.toLowerCase());\n        const matchesStatus = !filterStatus || order.status === filterStatus;\n        const matchesProductionStatus = !filterProductionStatus || order.productionStatus === filterProductionStatus;\n        // 日期范围过滤\n        const matchesDateRange = !dateRange || !dateRange[0] || !dateRange[1] || new Date(order.orderDate) >= dateRange[0].toDate() && new Date(order.orderDate) <= dateRange[1].toDate();\n        return matchesSearch && matchesStatus && matchesProductionStatus && matchesDateRange;\n    });\n    // 统计数据\n    const stats = {\n        total: orders.length,\n        pending: orders.filter((o)=>o.status === \"pending\").length,\n        confirmed: orders.filter((o)=>o.status === \"confirmed\").length,\n        completed: orders.filter((o)=>o.status === \"completed\").length,\n        cancelled: orders.filter((o)=>o.status === \"cancelled\").length,\n        totalAmount: orders.reduce((sum, o)=>sum + o.finalAmount, 0),\n        delayedOrders: orders.filter((o)=>new Date(o.deliveryDate) < new Date() && o.status !== \"completed\" && o.status !== \"cancelled\").length\n    };\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleViewDetail = (order)=>{\n        setSelectedOrder(order);\n        setIsDetailModalVisible(true);\n    };\n    const handleOrderChange = (order)=>{\n        setSelectedOrder(order);\n        setSelectedChangeType(\"\") // 重置变更类型\n        ;\n        setIsChangeModalVisible(true);\n        changeForm.resetFields();\n    };\n    // 🔧 新增：根据变更类型自动填充原始值\n    const handleChangeTypeSelect = (changeType)=>{\n        if (!selectedOrder) return;\n        setSelectedChangeType(changeType) // 更新选择的变更类型\n        ;\n        let originalValue = \"\";\n        switch(changeType){\n            case \"quantity\":\n                var _selectedOrder_items;\n                // 获取订单总数量\n                const totalQuantity = ((_selectedOrder_items = selectedOrder.items) === null || _selectedOrder_items === void 0 ? void 0 : _selectedOrder_items.reduce((sum, item)=>sum + item.quantity, 0)) || 0;\n                originalValue = totalQuantity.toString();\n                break;\n            case \"delivery_date\":\n                // 获取交期，格式化为YYYY-MM-DD\n                try {\n                    const date = new Date(selectedOrder.deliveryDate);\n                    originalValue = date.toISOString().split(\"T\")[0] // 格式化为YYYY-MM-DD\n                    ;\n                } catch (error) {\n                    originalValue = selectedOrder.deliveryDate.split(\"T\")[0] // 备用方案\n                    ;\n                }\n                break;\n            case \"cancel\":\n                // 订单取消时，原始值为当前状态\n                const statusMap = {\n                    \"pending\": \"未审核\",\n                    \"confirmed\": \"已审核\",\n                    \"completed\": \"完成\",\n                    \"cancelled\": \"已取消\"\n                };\n                originalValue = statusMap[selectedOrder.status] || selectedOrder.status;\n                break;\n            default:\n                originalValue = \"\";\n        }\n        // 自动填充原始值，并清空新值\n        changeForm.setFieldsValue({\n            originalValue: originalValue,\n            newValue: \"\" // 清空新值，让用户重新输入\n        });\n    };\n    // 🔧 新增：根据变更类型获取输入提示\n    const getPlaceholderByChangeType = (changeType, field)=>{\n        var _placeholders_changeType;\n        if (!changeType) return field === \"originalValue\" ? \"请输入原始值\" : \"请输入新值\";\n        const placeholders = {\n            quantity: {\n                originalValue: \"当前订单总数量\",\n                newValue: \"请输入新的数量\"\n            },\n            delivery_date: {\n                originalValue: \"当前交期日期\",\n                newValue: \"请选择新的交期日期\"\n            },\n            cancel: {\n                originalValue: \"当前订单状态\",\n                newValue: \"取消原因\"\n            }\n        };\n        return ((_placeholders_changeType = placeholders[changeType]) === null || _placeholders_changeType === void 0 ? void 0 : _placeholders_changeType[field]) || (field === \"originalValue\" ? \"请输入原始值\" : \"请输入新值\");\n    };\n    // 🔧 新增：根据变更类型渲染不同的输入组件\n    const renderInputByChangeType = (changeType, field)=>{\n        const placeholder = getPlaceholderByChangeType(changeType, field);\n        const isReadOnly = field === \"originalValue\" && !!selectedChangeType;\n        switch(changeType){\n            case \"quantity\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                    placeholder: placeholder,\n                    min: 0,\n                    style: {\n                        width: \"100%\"\n                    },\n                    readOnly: isReadOnly\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                    lineNumber: 567,\n                    columnNumber: 11\n                }, undefined);\n            case \"delivery_date\":\n                if (field === \"originalValue\") {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        placeholder: placeholder,\n                        readOnly: true\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                        lineNumber: 576,\n                        columnNumber: 18\n                    }, undefined);\n                } else {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                        placeholder: placeholder,\n                        style: {\n                            width: \"100%\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                        lineNumber: 578,\n                        columnNumber: 18\n                    }, undefined);\n                }\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                    placeholder: placeholder,\n                    readOnly: isReadOnly\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                    lineNumber: 583,\n                    columnNumber: 11\n                }, undefined);\n        }\n    };\n    const handleDelete = async (id)=>{\n        const result = await handleApiResponse(()=>_services_dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_3__.dataAccessManager.orders.delete(id), \"删除订单\");\n        if (result !== null) {\n            await refreshOrders();\n            message.success(\"订单删除成功\");\n        } else {\n            message.error(\"删除订单失败\");\n        }\n    };\n    // 新增订单处理函数\n    const handleAddOrder = ()=>{\n        setIsAddOrderModalVisible(true);\n    };\n    const handleAddOrderSuccess = async (newOrder)=>{\n        // 刷新订单列表以获取最新数据\n        await refreshOrders();\n        message.success(\"订单创建成功\");\n        setIsAddOrderModalVisible(false);\n    };\n    const handleAddOrderCancel = ()=>{\n        setIsAddOrderModalVisible(false);\n    };\n    const handleStartMRP = async (order)=>{\n        try {\n            // 验证前置条件\n            if (order.status !== \"confirmed\") {\n                message.error(\"只有已审核的订单才能启动MRP\");\n                return;\n            }\n            if (order.mrpStatus === \"completed\") {\n                message.warning(\"该订单的MRP已经执行完成\");\n                return;\n            }\n            if (order.mrpStatus === \"in_progress\") {\n                message.warning(\"该订单的MRP正在执行中，请等待完成\");\n                return;\n            }\n            // 开始MRP执行\n            setMRPExecuting(true);\n            setMrpExecutionStep(1);\n            setShowMRPResult(false);\n            setMrpResult(null);\n            // 更新订单MRP状态为执行中\n            try {\n                await _services_dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_3__.dataAccessManager.orders.update(order.id, {\n                    mrpStatus: \"in_progress\",\n                    updatedAt: new Date().toISOString()\n                });\n                // 🔧 修复：立即更新selectedOrder状态，确保按钮立即禁用\n                if (selectedOrder && selectedOrder.id === order.id) {\n                    setSelectedOrder({\n                        ...selectedOrder,\n                        mrpStatus: \"in_progress\",\n                        updatedAt: new Date().toISOString()\n                    });\n                }\n                // 立即刷新本地状态\n                await refreshOrders();\n            } catch (error) {\n                console.error(\"更新订单MRP状态失败:\", error);\n            }\n            // 动态导入MRP服务\n            const { mrpService } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_services_mrpService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/services/mrpService */ \"(app-pages-browser)/./src/services/mrpService.ts\"));\n            // 步骤1: 启动MRP\n            message.info(\"正在启动MRP...\");\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            setMrpExecutionStep(2);\n            // 步骤2: MRP分析\n            message.info(\"正在进行MRP分析...\");\n            await new Promise((resolve)=>setTimeout(resolve, 1500));\n            setMrpExecutionStep(3);\n            // 步骤3: 生成生产订单\n            message.info(\"正在生成生产订单...\");\n            // 执行MRP\n            const mrpResult = await mrpService.executeMRP({\n                salesOrder: order,\n                executedBy: \"当前用户\",\n                executionDate: new Date().toISOString()\n            });\n            setMrpExecutionStep(4);\n            await new Promise((resolve)=>setTimeout(resolve, 500));\n            // 步骤4: 完成\n            setMRPExecuting(false);\n            setMrpResult(mrpResult);\n            setShowMRPResult(true);\n            // 更新订单MRP状态为已完成\n            try {\n                await _services_dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_3__.dataAccessManager.orders.update(order.id, {\n                    mrpStatus: \"completed\",\n                    mrpExecutedAt: new Date().toISOString(),\n                    mrpExecutedBy: \"当前用户\",\n                    mrpResultId: mrpResult.id,\n                    updatedAt: new Date().toISOString()\n                });\n                // 🔧 修复：立即更新selectedOrder状态，确保按钮状态正确\n                if (selectedOrder && selectedOrder.id === order.id) {\n                    setSelectedOrder({\n                        ...selectedOrder,\n                        mrpStatus: \"completed\",\n                        mrpExecutedAt: new Date().toISOString(),\n                        mrpExecutedBy: \"当前用户\",\n                        mrpResultId: mrpResult.id,\n                        updatedAt: new Date().toISOString()\n                    });\n                }\n            } catch (error) {\n                console.error(\"更新订单MRP完成状态失败:\", error);\n            }\n            // 步骤5: MRP执行完成，刷新订单数据\n            await refreshOrders() // 刷新订单列表以获取最新状态\n            ;\n            if (mrpResult.generatedProductionOrders && mrpResult.generatedProductionOrders.length > 0) {\n                message.success(\"MRP执行完成！生成了 \".concat(mrpResult.totalProductionOrders, \" 个生产订单，请前往生产管理模块查看\"));\n            } else {\n                message.success(\"MRP执行完成！未生成新的生产订单（可能库存充足）\");\n            }\n        } catch (error) {\n            setMRPExecuting(false);\n            // 更新订单MRP状态为失败\n            const updateResult = await handleApiResponse(()=>_services_dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_3__.dataAccessManager.orders.update(order.id, {\n                    mrpStatus: \"failed\",\n                    updatedAt: new Date().toISOString()\n                }), \"更新订单MRP状态\");\n            if (updateResult) {\n                // 🔧 修复：立即更新selectedOrder状态\n                if (selectedOrder && selectedOrder.id === order.id) {\n                    setSelectedOrder({\n                        ...selectedOrder,\n                        mrpStatus: \"failed\",\n                        updatedAt: new Date().toISOString()\n                    });\n                }\n                await refreshOrders();\n            }\n            message.error(\"MRP执行失败: \".concat(error instanceof Error ? error.message : \"未知错误\"));\n        }\n    };\n    const handleChangeModalOk = ()=>{\n        changeForm.validateFields().then(async (values)=>{\n            if (!selectedOrder) return;\n            // 🔧 P4-3数据验证统一：使用OrderValidationService验证订单变更\n            const changeValidation = _services_validation_OrderValidationService__WEBPACK_IMPORTED_MODULE_15__[\"default\"].validateOrderChange(selectedOrder, values.changeType, values.originalValue, values.newValue);\n            if (!changeValidation.isValid) {\n                message.error(\"变更验证失败：\".concat(changeValidation.errors[0]));\n                return;\n            }\n            // 显示警告信息（如果有）\n            if (changeValidation.warnings && changeValidation.warnings.length > 0) {\n                changeValidation.warnings.forEach((warning)=>{\n                    message.warning(warning);\n                });\n            }\n            const now = new Date().toISOString();\n            const newChange = {\n                id: Date.now().toString(),\n                orderNumber: selectedOrder.orderNumber,\n                ...values,\n                changeStatus: \"pending\",\n                applicant: \"当前用户\",\n                customerConfirmed: false,\n                productionStatus: selectedOrder.productionStatus,\n                createdAt: now\n            };\n            // 更新订单变更记录\n            const currentOrder = orders.find((o)=>o.id === selectedOrder.id);\n            if (currentOrder) {\n                const updatedChanges = [\n                    ...currentOrder.changes || [],\n                    newChange\n                ];\n                const result = await handleApiResponse(()=>_services_dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_3__.dataAccessManager.orders.update(selectedOrder.id, {\n                        changes: updatedChanges,\n                        updatedAt: now\n                    }), \"提交变更申请\");\n                if (result) {\n                    await refreshOrders();\n                    setIsChangeModalVisible(false);\n                    changeForm.resetFields();\n                    message.success(\"订单变更申请提交成功\");\n                } else {\n                    message.error(\"提交变更申请失败\");\n                }\n            }\n        });\n    };\n    const handleChangeModalCancel = ()=>{\n        setIsChangeModalVisible(false);\n        setSelectedChangeType(\"\") // 重置变更类型\n        ;\n        changeForm.resetFields();\n    };\n    // 处理变更审批\n    const handleChangeApproval = async (changeId, action, reason)=>{\n        if (!selectedOrder) return;\n        try {\n            var _currentOrder_changes;\n            const now = new Date().toISOString();\n            const currentOrder = orders.find((o)=>o.id === selectedOrder.id);\n            if (!currentOrder) return;\n            // 更新变更记录状态\n            const updatedChanges = ((_currentOrder_changes = currentOrder.changes) === null || _currentOrder_changes === void 0 ? void 0 : _currentOrder_changes.map((change)=>{\n                if (change.id === changeId) {\n                    return {\n                        ...change,\n                        changeStatus: action === \"approve\" ? \"approved\" : \"rejected\",\n                        approver: \"当前用户\",\n                        approvedAt: now,\n                        ...reason && {\n                            rejectionReason: reason\n                        }\n                    };\n                }\n                return change;\n            })) || [];\n            // 如果变更被批准，需要执行相应的变更逻辑\n            const approvedChange = updatedChanges.find((c)=>c.id === changeId);\n            if (action === \"approve\" && approvedChange) {\n                switch(approvedChange.changeType){\n                    case \"cancel\":\n                        // 验证订单是否可以取消\n                        const cancelValidation = await _services_OrderCancellationService__WEBPACK_IMPORTED_MODULE_7__.OrderCancellationService.validateCancellation(selectedOrder);\n                        if (!cancelValidation.canCancel) {\n                            message.error(\"无法取消订单: \".concat(cancelValidation.reason));\n                            return;\n                        }\n                        if (cancelValidation.warnings.length > 0) {\n                            // 显示警告信息，让用户确认\n                            await new Promise((resolve, reject)=>{\n                                modal.confirm({\n                                    title: \"订单取消确认\",\n                                    content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"确定要取消此订单吗？\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                                lineNumber: 871,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                style: {\n                                                    marginTop: 8,\n                                                    paddingLeft: 20\n                                                },\n                                                children: cancelValidation.warnings.map((warning, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        style: {\n                                                            color: \"#fa8c16\"\n                                                        },\n                                                        children: warning\n                                                    }, index, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 874,\n                                                        columnNumber: 27\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                                lineNumber: 872,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                        lineNumber: 870,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    onOk: ()=>resolve(),\n                                    onCancel: ()=>reject(new Error(\"用户取消操作\"))\n                                });\n                            });\n                        }\n                        await handleOrderCancellation(selectedOrder, approvedChange);\n                        break;\n                    case \"quantity\":\n                        // 执行数量变更\n                        await handleQuantityChange(selectedOrder, approvedChange);\n                        break;\n                    case \"delivery_date\":\n                        // 执行交期变更\n                        await handleDeliveryDateChange(selectedOrder, approvedChange);\n                        break;\n                }\n                // 更新变更记录状态为已执行\n                const finalChanges = updatedChanges.map((change)=>{\n                    if (change.id === changeId) {\n                        return {\n                            ...change,\n                            changeStatus: \"executed\",\n                            executedAt: new Date().toISOString()\n                        };\n                    }\n                    return change;\n                });\n                // 更新订单变更记录\n                const updateResult = await handleApiResponse(()=>_services_dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_3__.dataAccessManager.orders.update(selectedOrder.id, {\n                        changes: finalChanges,\n                        updatedAt: now\n                    }), \"更新订单变更记录\");\n                if (!updateResult) {\n                    message.error(\"变更\".concat(action === \"approve\" ? \"批准\" : \"拒绝\", \"失败\"));\n                    return;\n                }\n            } else {\n                // 如果是拒绝变更，只更新变更记录\n                const updateResult = await handleApiResponse(()=>_services_dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_3__.dataAccessManager.orders.update(selectedOrder.id, {\n                        changes: updatedChanges,\n                        updatedAt: now\n                    }), \"更新变更记录\");\n                if (!updateResult) {\n                    message.error(\"变更\".concat(action === \"approve\" ? \"批准\" : \"拒绝\", \"失败\"));\n                    return;\n                }\n            }\n            await refreshOrders();\n            message.success(\"变更\".concat(action === \"approve\" ? \"批准\" : \"拒绝\", \"成功\"));\n        } catch (error) {\n            message.error(\"变更\".concat(action === \"approve\" ? \"批准\" : \"拒绝\", \"失败: \").concat(error instanceof Error ? error.message : \"未知错误\"));\n            console.error(\"变更审批失败:\", error);\n        }\n    };\n    // 处理订单取消的核心逻辑\n    const handleOrderCancellation = async (order, change)=>{\n        try {\n            // 使用专门的订单取消服务\n            const result = await _services_OrderCancellationService__WEBPACK_IMPORTED_MODULE_7__.OrderCancellationService.executeOrderCancellation(order, change);\n            if (result.success) {\n                message.success(\"订单 \".concat(order.orderNumber, \" 已成功取消，\") + \"同时取消了 \".concat(result.productionOrdersCancelled, \" 个生产订单和 \").concat(result.workOrdersCancelled, \" 个工单\"));\n            } else {\n                throw new Error(result.errors.join(\"; \"));\n            }\n        } catch (error) {\n            console.error(\"订单取消处理失败:\", error);\n            throw error;\n        }\n    };\n    // 处理数量变更的核心逻辑\n    const handleQuantityChange = async (order, change)=>{\n        try {\n            // 使用专门的数量变更服务\n            const result = await _services_OrderQuantityChangeService__WEBPACK_IMPORTED_MODULE_8__.OrderQuantityChangeService.executeQuantityChange(order, change);\n            if (result.success) {\n                message.success(\"订单 \".concat(order.orderNumber, \" 数量变更成功，\") + \"从 \".concat(change.originalValue, \" 变更为 \").concat(change.newValue, \"，\") + \"影响了 \".concat(result.productionOrdersAffected, \" 个生产订单和 \").concat(result.workOrdersAffected, \" 个工单\"));\n            } else {\n                throw new Error(result.errors.join(\"; \"));\n            }\n        } catch (error) {\n            console.error(\"数量变更执行失败:\", error);\n            throw error;\n        }\n    };\n    // 处理交期变更的核心逻辑\n    const handleDeliveryDateChange = async (order, change)=>{\n        try {\n            // 使用专门的交期变更服务\n            const result = await _services_OrderDeliveryDateChangeService__WEBPACK_IMPORTED_MODULE_9__.OrderDeliveryDateChangeService.executeDeliveryDateChange(order, change);\n            if (result.success) {\n                const successMessage = \"订单 \".concat(order.orderNumber, \" 交期变更成功，\") + \"从 \".concat(change.originalValue, \" 变更为 \").concat(change.newValue, \"，\") + \"影响了 \".concat(result.productionOrdersAffected, \" 个生产订单和 \").concat(result.workOrdersAffected, \" 个工单\");\n                if (result.scheduleAdjustmentsRequired) {\n                    message.warning(successMessage + \"\\n注意：有 \".concat(result.scheduleImpact.conflictingOrders.length, \" 个订单的排程需要调整\"));\n                } else {\n                    message.success(successMessage);\n                }\n            } else {\n                throw new Error(result.errors.join(\"; \"));\n            }\n        } catch (error) {\n            console.error(\"交期变更执行失败:\", error);\n            throw error;\n        }\n    };\n    // 批量操作函数\n    const handleBatchApprove = ()=>{\n        if (selectedRowKeys.length === 0) {\n            message.warning(\"请先选择要审核的订单\");\n            return;\n        }\n        const pendingOrders = selectedOrders.filter((order)=>order.status === \"pending\");\n        if (pendingOrders.length === 0) {\n            message.warning(\"所选订单中没有未审核的订单\");\n            return;\n        }\n        modal.confirm({\n            title: \"批量审核确认\",\n            content: \"确定要审核 \".concat(pendingOrders.length, \" 个订单吗？\"),\n            onOk: async ()=>{\n                setBatchLoading(true);\n                try {\n                    // 批量更新订单状态\n                    const updatePromises = pendingOrders.map((order)=>handleApiResponse(()=>_services_dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_3__.dataAccessManager.orders.update(order.id, {\n                                status: \"confirmed\",\n                                productionStatus: \"not_started\",\n                                updatedAt: new Date().toISOString()\n                            }), \"审核订单 \".concat(order.orderNumber)));\n                    const results = await Promise.all(updatePromises);\n                    const successCount = results.filter((result)=>result !== null).length;\n                    if (successCount > 0) {\n                        await refreshOrders();\n                        setSelectedRowKeys([]);\n                        setSelectedOrders([]);\n                        message.success(\"成功审核 \".concat(successCount, \" 个订单\"));\n                    } else {\n                        message.error(\"批量审核失败，没有订单被成功审核\");\n                    }\n                } catch (error) {\n                    message.error(\"批量审核失败: \".concat(error instanceof Error ? error.message : \"未知错误\"));\n                    console.error(\"批量审核异常:\", error);\n                } finally{\n                    setBatchLoading(false);\n                }\n            }\n        });\n    };\n    const handleBatchUnapprove = ()=>{\n        if (selectedRowKeys.length === 0) {\n            message.warning(\"请先选择要反审核的订单\");\n            return;\n        }\n        const confirmedOrders = selectedOrders.filter((order)=>order.status === \"confirmed\");\n        if (confirmedOrders.length === 0) {\n            message.warning(\"所选订单中没有已审核的订单\");\n            return;\n        }\n        modal.confirm({\n            title: \"批量反审核确认\",\n            content: \"确定要反审核 \".concat(confirmedOrders.length, \" 个订单吗？\"),\n            onOk: async ()=>{\n                setBatchLoading(true);\n                try {\n                    // 批量更新订单状态\n                    const updatePromises = confirmedOrders.map((order)=>handleApiResponse(()=>_services_dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_3__.dataAccessManager.orders.update(order.id, {\n                                status: \"pending\",\n                                productionStatus: \"not_started\",\n                                updatedAt: new Date().toISOString()\n                            }), \"反审核订单 \".concat(order.orderNumber)));\n                    const results = await Promise.all(updatePromises);\n                    const successCount = results.filter((result)=>result !== null).length;\n                    if (successCount > 0) {\n                        await refreshOrders();\n                        setSelectedRowKeys([]);\n                        setSelectedOrders([]);\n                        message.success(\"成功反审核 \".concat(successCount, \" 个订单\"));\n                    } else {\n                        message.error(\"批量反审核失败，没有订单被成功反审核\");\n                    }\n                } catch (error) {\n                    message.error(\"批量反审核失败: \".concat(error instanceof Error ? error.message : \"未知错误\"));\n                    console.error(\"批量反审核异常:\", error);\n                } finally{\n                    setBatchLoading(false);\n                }\n            }\n        });\n    };\n    const handleBatchDelete = ()=>{\n        if (selectedRowKeys.length === 0) {\n            message.warning(\"请先选择要删除的订单\");\n            return;\n        }\n        const deletableOrders = selectedOrders.filter((order)=>order.status === \"pending\" || order.status === \"cancelled\");\n        if (deletableOrders.length === 0) {\n            message.warning(\"所选订单中没有可删除的订单（只能删除待审核或已取消的订单）\");\n            return;\n        }\n        modal.confirm({\n            title: \"批量删除确认\",\n            content: \"确定要删除 \".concat(deletableOrders.length, \" 个订单吗？此操作不可恢复！\"),\n            onOk: async ()=>{\n                setBatchLoading(true);\n                try {\n                    const deletePromises = deletableOrders.map((order)=>handleApiResponse(()=>_services_dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_3__.dataAccessManager.orders.delete(order.id), \"删除订单 \".concat(order.orderNumber)));\n                    const results = await Promise.all(deletePromises);\n                    const successCount = results.filter((result)=>result !== null).length;\n                    if (successCount > 0) {\n                        await refreshOrders();\n                        setSelectedRowKeys([]);\n                        setSelectedOrders([]);\n                        message.success(\"成功删除 \".concat(successCount, \" 个订单\"));\n                    } else {\n                        message.error(\"批量删除失败，没有订单被成功删除\");\n                    }\n                } catch (error) {\n                    message.error(\"批量删除失败: \".concat(error instanceof Error ? error.message : \"未知错误\"));\n                    console.error(\"批量删除异常:\", error);\n                } finally{\n                    setBatchLoading(false);\n                }\n            }\n        });\n    };\n    const handleChangeSubmit = async ()=>{\n        try {\n            const values = await changeForm.validateFields();\n            if (!selectedOrder) return;\n            const change = {\n                id: Date.now().toString(),\n                orderId: selectedOrder.id,\n                changeType: values.changeType,\n                originalValue: \"\",\n                newValue: \"\",\n                reason: values.reason,\n                requestedBy: \"当前用户\",\n                requestedAt: new Date().toISOString(),\n                status: \"pending\"\n            };\n            // 根据变更类型设置原始值和新值\n            switch(values.changeType){\n                case \"quantity\":\n                    var _selectedOrder_items;\n                    const totalQuantity = ((_selectedOrder_items = selectedOrder.items) === null || _selectedOrder_items === void 0 ? void 0 : _selectedOrder_items.reduce((sum, item)=>sum + item.quantity, 0)) || 0;\n                    change.originalValue = totalQuantity.toString();\n                    change.newValue = values.newQuantity.toString();\n                    await handleQuantityChange(selectedOrder, change);\n                    break;\n                case \"deliveryDate\":\n                    change.originalValue = selectedOrder.deliveryDate;\n                    change.newValue = values.newDeliveryDate.format(\"YYYY-MM-DD\");\n                    await handleDeliveryDateChange(selectedOrder, change);\n                    break;\n                case \"cancel\":\n                    await handleOrderCancellation(selectedOrder, change);\n                    break;\n            }\n            await refreshOrders();\n            setIsChangeModalVisible(false);\n            changeForm.resetFields();\n        } catch (error) {\n            console.error(\"订单变更失败:\", error);\n            message.error(\"订单变更失败\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"24px\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                gutter: [\n                    16,\n                    16\n                ],\n                style: {\n                    marginBottom: \"24px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                        xs: 24,\n                        sm: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                title: \"订单总数\",\n                                value: stats.total,\n                                suffix: \"个\",\n                                valueStyle: {\n                                    color: \"#1890ff\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 1218,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 1217,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                        lineNumber: 1216,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                        xs: 24,\n                        sm: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                title: \"待审核\",\n                                value: stats.pending,\n                                suffix: \"个\",\n                                valueStyle: {\n                                    color: \"#faad14\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 1228,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 1227,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                        lineNumber: 1226,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                        xs: 24,\n                        sm: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                title: \"已确认\",\n                                value: stats.confirmed,\n                                suffix: \"个\",\n                                valueStyle: {\n                                    color: \"#52c41a\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 1238,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 1237,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                        lineNumber: 1236,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                        xs: 24,\n                        sm: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                title: \"总金额\",\n                                value: stats.totalAmount,\n                                precision: 2,\n                                prefix: \"\\xa5\",\n                                valueStyle: {\n                                    color: \"#f50\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 1248,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 1247,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                        lineNumber: 1246,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                lineNumber: 1215,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                style: {\n                    marginBottom: \"16px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                    gutter: [\n                        16,\n                        16\n                    ],\n                    align: \"middle\",\n                    justify: \"space-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                            xs: 24,\n                            lg: 18,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                wrap: true,\n                                size: \"middle\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        placeholder: \"搜索订单号、客户名称\",\n                                        prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_ExportOutlined_PlusOutlined_SearchOutlined_SwapOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1266,\n                                            columnNumber: 25\n                                        }, void 0),\n                                        value: searchText,\n                                        onChange: (e)=>setSearchText(e.target.value),\n                                        style: {\n                                            width: \"256px\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                        lineNumber: 1264,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        placeholder: \"订单状态\",\n                                        value: filterStatus,\n                                        onChange: setFilterStatus,\n                                        style: {\n                                            width: \"128px\"\n                                        },\n                                        allowClear: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                                value: \"pending\",\n                                                children: \"待审核\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                                lineNumber: 1278,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                                value: \"confirmed\",\n                                                children: \"已确认\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                                lineNumber: 1279,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                                value: \"completed\",\n                                                children: \"已完成\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                                lineNumber: 1280,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                                value: \"cancelled\",\n                                                children: \"已取消\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                                lineNumber: 1281,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                        lineNumber: 1271,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RangePicker, {\n                                        value: dateRange,\n                                        onChange: setDateRange,\n                                        placeholder: [\n                                            \"开始日期\",\n                                            \"结束日期\"\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                        lineNumber: 1283,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 1263,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 1262,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                            xs: 24,\n                            lg: 6,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_ExportOutlined_PlusOutlined_SearchOutlined_SwapOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1292,\n                                            columnNumber: 29\n                                        }, void 0),\n                                        children: \"导出\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                        lineNumber: 1292,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                        type: \"primary\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_ExportOutlined_PlusOutlined_SearchOutlined_SwapOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1295,\n                                            columnNumber: 23\n                                        }, void 0),\n                                        onClick: ()=>setIsAddOrderModalVisible(true),\n                                        children: \"新建订单\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                        lineNumber: 1293,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 1291,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 1290,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                    lineNumber: 1261,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                lineNumber: 1260,\n                columnNumber: 7\n            }, undefined),\n            selectedRowKeys.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                style: {\n                    marginBottom: \"16px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                    message: \"已选择 \".concat(selectedRowKeys.length, \" 个订单\"),\n                    type: \"info\",\n                    showIcon: true,\n                    action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                size: \"small\",\n                                onClick: handleBatchApprove,\n                                loading: batchLoading,\n                                children: \"批量审核\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 1314,\n                                columnNumber: 17\n                            }, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                size: \"small\",\n                                onClick: handleBatchUnapprove,\n                                loading: batchLoading,\n                                children: \"批量反审核\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 1321,\n                                columnNumber: 17\n                            }, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                size: \"small\",\n                                danger: true,\n                                onClick: handleBatchDelete,\n                                loading: batchLoading,\n                                children: \"批量删除\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 1328,\n                                columnNumber: 17\n                            }, void 0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                        lineNumber: 1313,\n                        columnNumber: 15\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                    lineNumber: 1308,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                lineNumber: 1307,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                title: \"销售订单列表\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_41__[\"default\"], {\n                    columns: columns,\n                    dataSource: filteredOrders,\n                    rowKey: \"id\",\n                    loading: ordersLoading || loading,\n                    rowSelection: {\n                        selectedRowKeys,\n                        onChange: (keys, rows)=>{\n                            setSelectedRowKeys(keys);\n                            setSelectedOrders(rows);\n                        },\n                        getCheckboxProps: (record)=>({\n                                disabled: record.status === \"completed\"\n                            })\n                    },\n                    pagination: {\n                        total: filteredOrders.length,\n                        pageSize: 10,\n                        showSizeChanger: true,\n                        showQuickJumper: true,\n                        showTotal: (total, range)=>\"第 \".concat(range[0], \"-\").concat(range[1], \" 条/共 \").concat(total, \" 条\"),\n                        pageSizeOptions: [\n                            \"10\",\n                            \"20\",\n                            \"50\",\n                            \"100\"\n                        ]\n                    },\n                    scroll: {\n                        x: 1600\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                    lineNumber: 1344,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                lineNumber: 1343,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_OrderDetailModal__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                visible: isDetailModalVisible,\n                onCancel: ()=>setIsDetailModalVisible(false),\n                order: selectedOrder,\n                config: _components_common_OrderDetailModal_configs_salesOrderConfig__WEBPACK_IMPORTED_MODULE_17__[\"default\"]\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                lineNumber: 1372,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sales_AddOrderModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                visible: isAddOrderModalVisible,\n                onCancel: ()=>setIsAddOrderModalVisible(false),\n                onSuccess: ()=>{\n                    setIsAddOrderModalVisible(false);\n                    refreshOrders();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                lineNumber: 1380,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_42__[\"default\"], {\n                title: \"订单变更\",\n                open: isChangeModalVisible,\n                onOk: handleChangeSubmit,\n                onCancel: ()=>setIsChangeModalVisible(false),\n                width: 600,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                    form: changeForm,\n                    layout: \"vertical\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"].Item, {\n                            name: \"changeType\",\n                            label: \"变更类型\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择变更类型\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                placeholder: \"请选择变更类型\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"quantity\",\n                                        children: \"数量变更\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                        lineNumber: 1404,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"deliveryDate\",\n                                        children: \"交期变更\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                        lineNumber: 1405,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"cancel\",\n                                        children: \"订单取消\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                        lineNumber: 1406,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 1403,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 1398,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"].Item, {\n                            name: \"reason\",\n                            label: \"变更原因\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入变更原因\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TextArea, {\n                                rows: 4,\n                                placeholder: \"请详细说明变更原因\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 1414,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 1409,\n                            columnNumber: 11\n                        }, undefined),\n                        selectedChangeType === \"quantity\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"].Item, {\n                            name: \"newQuantity\",\n                            label: \"新数量\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入新数量\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                min: 1,\n                                style: {\n                                    width: \"100%\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 1422,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 1417,\n                            columnNumber: 13\n                        }, undefined),\n                        selectedChangeType === \"deliveryDate\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"].Item, {\n                            name: \"newDeliveryDate\",\n                            label: \"新交期\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择新交期\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                style: {\n                                    width: \"100%\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 1431,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 1426,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                    lineNumber: 1397,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                lineNumber: 1390,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n        lineNumber: 1213,\n        columnNumber: 5\n    }, undefined);\n};\n_s(OrderManagement, \"ulpOxUkaGHBxc+E0GASVMjRmbxw=\", false, function() {\n    return [\n        _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"].useApp,\n        _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"].useForm,\n        _hooks_useOrdersData__WEBPACK_IMPORTED_MODULE_13__.useOrdersData,\n        _hooks_useEventListener__WEBPACK_IMPORTED_MODULE_11__.useDataChangeListener,\n        _hooks_useDebouncedCallback__WEBPACK_IMPORTED_MODULE_10__.useDebouncedSearch,\n        _hooks_useDataAccessMonitor__WEBPACK_IMPORTED_MODULE_12__.useDataAccessMonitor,\n        _hooks_useProductionOrders__WEBPACK_IMPORTED_MODULE_6__.useMRPProductionOrders,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = OrderManagement;\nfunction OrderManagementPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(OrderManagement, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n            lineNumber: 1443,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n        lineNumber: 1442,\n        columnNumber: 5\n    }, this);\n}\n_c1 = OrderManagementPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"OrderManagement\");\n$RefreshReg$(_c1, \"OrderManagementPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/sales/orders/page.tsx\n"));

/***/ })

});