/**
 * 布局相关CSS Modules样式
 * 专门用于页面布局和组件布局的样式
 */

/* 主布局容器 */
.mainLayout {
  min-height: 100vh;
  background-color: #f9fafb;
}

.layoutHeader {
  background: #ffffff;
  border-bottom: 1px solid #e5e7eb;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  height: 64px;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.layoutSider {
  background: #ffffff;
  box-shadow: 2px 0 8px 0 rgba(29, 35, 41, 0.05);
  border-right: 1px solid #e5e7eb;
  width: 256px;
  min-height: 100vh;
}

.layoutContent {
  background: #f9fafb;
  padding: 24px;
  min-height: calc(100vh - 64px);
}

.layoutFooter {
  background: #ffffff;
  border-top: 1px solid #e5e7eb;
  padding: 16px 24px;
  text-align: center;
  color: #6b7280;
  font-size: 14px;
}

/* 页面容器 */
.pageContainer {
  padding: 12px;
  background-color: #f9fafb;
  min-height: 100vh;
}

@media (min-width: 768px) {
  .pageContainer {
    padding: 24px;
  }
}

.pageContent {
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.pageHeader {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e5e7eb;
}

@media (min-width: 768px) {
  .pageHeader {
    margin-bottom: 24px;
    padding-bottom: 16px;
  }
}

.pageTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 8px;
  line-height: 1.5;
}

@media (min-width: 768px) {
  .pageTitle {
    font-size: 1.5rem;
  }
}

.pageDescription {
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.5;
}

@media (min-width: 768px) {
  .pageDescription {
    font-size: 1rem;
  }
}

/* 卡片容器 */
.cardContainer {
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  padding: 12px;
  margin-bottom: 16px;
}

@media (min-width: 768px) {
  .cardContainer {
    padding: 24px;
    margin-bottom: 24px;
  }
}

.cardHeader {
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 16px;
}

.cardTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 4px;
}

.cardDescription {
  font-size: 0.875rem;
  color: #6b7280;
}

.cardBody {
  padding: 0;
}

.cardFooter {
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

/* 侧边栏 */
.sidebar {
  width: 256px;
  background: #ffffff;
  border-right: 1px solid #e5e7eb;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1000;
  transform: translateX(-100%);
  transition: transform 0.3s ease-in-out;
}

.sidebarOpen {
  transform: translateX(0);
}

.sidebarCollapsed {
  width: 80px;
}

.sidebarHeader {
  height: 64px;
  padding: 0 16px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e5e7eb;
}

.sidebarContent {
  padding: 16px 0;
  height: calc(100vh - 64px);
  overflow-y: auto;
}

/* 移动端抽屉 */
.mobileDrawer {
  display: block;
}

@media (min-width: 768px) {
  .mobileDrawer {
    display: none;
  }
}

.drawerOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease-in-out;
}

.drawerOverlayVisible {
  opacity: 1;
  visibility: visible;
}

/* 内容区域 */
.contentArea {
  margin-left: 0;
  transition: margin-left 0.3s ease-in-out;
}

@media (min-width: 768px) {
  .contentArea {
    margin-left: 256px;
  }
  
  .contentAreaCollapsed {
    margin-left: 80px;
  }
}

.contentMain {
  padding: 24px;
  min-height: calc(100vh - 64px);
}

@media (max-width: 767px) {
  .contentMain {
    padding: 16px;
  }
}

/* 网格布局 */
.gridLayout {
  display: grid;
  gap: 24px;
}

.gridCols1 {
  grid-template-columns: 1fr;
}

.gridCols2 {
  grid-template-columns: repeat(2, 1fr);
}

.gridCols3 {
  grid-template-columns: repeat(3, 1fr);
}

.gridCols4 {
  grid-template-columns: repeat(4, 1fr);
}

@media (max-width: 767px) {
  .gridCols2,
  .gridCols3,
  .gridCols4 {
    grid-template-columns: 1fr;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .gridCols3,
  .gridCols4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 弹性布局 */
.flexLayout {
  display: flex;
  gap: 24px;
}

.flexCol {
  flex-direction: column;
}

.flexWrap {
  flex-wrap: wrap;
}

@media (max-width: 767px) {
  .flexLayout {
    flex-direction: column;
    gap: 16px;
  }
}

/* 容器尺寸 */
.containerSm {
  max-width: 640px;
  margin: 0 auto;
  padding: 0 16px;
}

.containerMd {
  max-width: 768px;
  margin: 0 auto;
  padding: 0 16px;
}

.containerLg {
  max-width: 1024px;
  margin: 0 auto;
  padding: 0 16px;
}

.containerXl {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 16px;
}

.containerFull {
  width: 100%;
  padding: 0 16px;
}

/* 分隔线 */
.divider {
  height: 1px;
  background: #e5e7eb;
  margin: 16px 0;
}

.dividerVertical {
  width: 1px;
  background: #e5e7eb;
  margin: 0 16px;
  height: 100%;
}

/* 间距工具 */
.spacingXs { gap: 4px; }
.spacingSm { gap: 8px; }
.spacingMd { gap: 16px; }
.spacingLg { gap: 24px; }
.spacingXl { gap: 32px; }
