'use client'

import React, { useState, useEffect } from 'react'
import { 
  Card, 
  Table, 
  Button, 
  Space, 
  Tag, 
  Input, 
  Select, 
  Modal,
  Form,
  InputNumber,
  Row,
  Col,
  Statistic,
  DatePicker,
  message,
  Popconfirm} from 'antd'
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  ExportOutlined,
  BankOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import { PaymentRecord, AccountReceivable} from '@/types'
import FormSelect from '@/components/FormSelect'
import dayjs from 'dayjs'
import { styleHelpers } from '@/utils/styles/antdHelpers'

const { Option } = Select
const { TextArea } = Input
const { RangePicker } = DatePicker

const PaymentManagement: React.FC = () => {
  const [paymentRecords, setPaymentRecords] = useState<PaymentRecord[]>([])
  const [receivables, setReceivables] = useState<AccountReceivable[]>([])
  const [loading, setLoading] = useState(false)
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false)
  const [isReceivableModalVisible, setIsReceivableModalVisible] = useState(false)
  const [editingPayment, setEditingPayment] = useState<PaymentRecord | null>(null)
  const [selectedPayment, setSelectedPayment] = useState<PaymentRecord | null>(null)
  const [selectedReceivable, setSelectedReceivable] = useState<AccountReceivable | null>(null)
  const [form] = Form.useForm()
  const [searchText, setSearchText] = useState('')
  const [filterStatus, setFilterStatus] = useState<string | undefined>(undefined)
  const [filterMethod, setFilterMethod] = useState<string | undefined>(undefined)
  const [activeTab, setActiveTab] = useState('payments')

  // 模拟数据
  useEffect(() => {
    const mockPaymentRecords: PaymentRecord[] = [
      {
        id: '1',
        paymentNumber: 'PAY-2024-001',
        customerId: '1',
        customerName: '上海包装材料有限公司',
        paymentDate: '2024-01-26',
        paymentAmount: 60000,
        paymentMethod: 'bank_transfer',
        bankAccount: '工商银行***1234',
        referenceNumber: 'TXN20240126001',
        relatedOrders: ['SO-2024-001'],
        relatedInvoices: ['INV-2024-001'],
        status: 'confirmed',
        remark: '部分付款，剩余款项月底结算',
        createdAt: '2024-01-26T10:00:00',
        updatedAt: '2024-01-26T10:00:00'
      },
      {
        id: '2',
        paymentNumber: 'PAY-2024-002',
        customerId: '2',
        customerName: '北京绿色包装科技公司',
        paymentDate: '2024-01-28',
        paymentAmount: 82490,
        paymentMethod: 'acceptance',
        referenceNumber: 'ACC20240128001',
        relatedOrders: ['SO-2024-002'],
        relatedInvoices: ['INV-2024-002'],
        status: 'pending',
        remark: '承兑汇票，6个月到期',
        createdAt: '2024-01-28T14:00:00',
        updatedAt: '2024-01-28T14:00:00'
      }
    ]

    const mockReceivables: AccountReceivable[] = [
      {
        id: '1',
        customerId: '1',
        customerName: '上海包装材料有限公司',
        orderNumber: 'SO-2024-001',
        invoiceNumber: 'INV-2024-001',
        invoiceDate: '2024-01-25',
        dueDate: '2024-02-25',
        originalAmount: 120062.5,
        paidAmount: 60000,
        remainingAmount: 60062.5,
        overdueDays: 0,
        status: 'normal',
        riskLevel: 'low',
        followUpActions: [
          {
            id: '1',
            receivableId: '1',
            actionType: 'call',
            actionDate: '2024-01-26',
            actionResult: '客户确认收到发票，承诺月底付清余款',
            nextActionDate: '2024-02-20',
            operator: '财务专员',
            remark: '客户信誉良好'
          }
        ],
        createdAt: '2024-01-25T10:00:00',
        updatedAt: '2024-01-26T10:00:00'
      },
      {
        id: '2',
        customerId: '3',
        customerName: '广州环保餐具厂',
        orderNumber: 'SO-2024-003',
        invoiceNumber: 'INV-2024-003',
        invoiceDate: '2024-01-20',
        dueDate: '2024-01-30',
        originalAmount: 45000,
        paidAmount: 0,
        remainingAmount: 45000,
        overdueDays: 8,
        status: 'overdue',
        riskLevel: 'high',
        followUpActions: [
          {
            id: '2',
            receivableId: '2',
            actionType: 'call',
            actionDate: '2024-01-31',
            actionResult: '客户电话无人接听',
            nextActionDate: '2024-02-02',
            operator: '财务专员'
          },
          {
            id: '3',
            receivableId: '2',
            actionType: 'email',
            actionDate: '2024-02-01',
            actionResult: '发送催款邮件',
            nextActionDate: '2024-02-05',
            operator: '财务专员'
          }
        ],
        createdAt: '2024-01-20T10:00:00',
        updatedAt: '2024-02-01T10:00:00'
      }
    ]

    setPaymentRecords(mockPaymentRecords)
    setReceivables(mockReceivables)
  }, [])

  // 获取付款状态标签
  const getPaymentStatusTag = (status: string) => {
    const statusMap = {
      'pending': { color: 'orange', text: '待确认', icon: <ClockCircleOutlined /> },
      'confirmed': { color: 'green', text: '已确认', icon: <CheckCircleOutlined /> },
      'reconciled': { color: 'cyan', text: '已对账', icon: <BankOutlined /> }
    }
    const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: '未知', icon: null }
    return (
      <Tag color={config.color} icon={config.icon}>
        {config.text}
      </Tag>
    )
  }

  // 获取付款方式标签
  const getPaymentMethodTag = (method: string) => {
    const methodMap = {
      'cash': { color: 'green', text: '现金' },
      'bank_transfer': { color: 'blue', text: '银行转账' },
      'check': { color: 'orange', text: '支票' },
      'acceptance': { color: 'purple', text: '承兑汇票' }
    }
    const config = methodMap[method as keyof typeof methodMap] || { color: 'default', text: '其他' }
    return <Tag color={config.color}>{config.text}</Tag>
  }

  // 获取应收状态标签
  const getReceivableStatusTag = (status: string) => {
    const statusMap = {
      'normal': { color: 'green', text: '正常' },
      'overdue': { color: 'red', text: '逾期' },
      'bad_debt': { color: 'volcano', text: '坏账' }
    }
    const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: '未知' }
    return <Tag color={config.color}>{config.text}</Tag>
  }

  // 获取风险等级标签
  const getRiskLevelTag = (level: string) => {
    const levelMap = {
      'low': { color: 'green', text: '低风险' },
      'medium': { color: 'orange', text: '中风险' },
      'high': { color: 'red', text: '高风险' }
    }
    const config = levelMap[level as keyof typeof levelMap] || { color: 'default', text: '未知' }
    return <Tag color={config.color}>{config.text}</Tag>
  }

  // 收款记录表格列定义
  const paymentColumns: ColumnsType<PaymentRecord> = [
    {
      title: '收款单号',
      dataIndex: 'paymentNumber',
      key: 'paymentNumber',
      width: 140,
      fixed: 'left'
    },
    {
      title: '客户名称',
      dataIndex: 'customerName',
      key: 'customerName',
      width: 180,
      ellipsis: true
    },
    {
      title: '收款日期',
      dataIndex: 'paymentDate',
      key: 'paymentDate',
      width: 120,
      sorter: (a, b) => new Date(a.paymentDate).getTime() - new Date(b.paymentDate).getTime()
    },
    {
      title: '收款金额',
      dataIndex: 'paymentAmount',
      key: 'paymentAmount',
      width: 120,
      render: (amount: number) => (
        <span style={{ fontWeight: 'bold', color: '#3f8600' }}>
          ¥{amount.toLocaleString()}
        </span>
      ),
      sorter: (a, b) => a.paymentAmount - b.paymentAmount
    },
    {
      title: '付款方式',
      dataIndex: 'paymentMethod',
      key: 'paymentMethod',
      width: 120,
      render: (method: string) => getPaymentMethodTag(method)
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => getPaymentStatusTag(status)
    },
    {
      title: '关联订单',
      dataIndex: 'relatedOrders',
      key: 'relatedOrders',
      width: 120,
      render: (orders: string[]) => (
        <div>
          {orders.map(order => (
            <Tag key={order}>{order}</Tag>
          ))}
        </div>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button 
            type="link" 
            icon={<EyeOutlined />} 
            onClick={() => handleViewPaymentDetail(record)}
          >
            详情
          </Button>
          <Button 
            type="link" 
            icon={<EditOutlined />} 
            onClick={() => handleEditPayment(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这条收款记录吗？"
            onConfirm={() => handleDeletePayment(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ]

  // 应收账款表格列定义
  const receivableColumns: ColumnsType<AccountReceivable> = [
    {
      title: '客户名称',
      dataIndex: 'customerName',
      key: 'customerName',
      width: 180,
      ellipsis: true,
      fixed: 'left'
    },
    {
      title: '销售订单号',
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      width: 120
    },
    {
      title: '发票号',
      dataIndex: 'invoiceNumber',
      key: 'invoiceNumber',
      width: 120
    },
    {
      title: '到期日期',
      dataIndex: 'dueDate',
      key: 'dueDate',
      width: 120,
      render: (date: string, record) => (
        <span style={{ color: record.overdueDays > 0 ? '#ff4d4f' : 'inherit' }}>
          {date}
        </span>
      ),
      sorter: (a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime()
    },
    {
      title: '应收金额',
      dataIndex: 'originalAmount',
      key: 'originalAmount',
      width: 120,
      render: (amount: number) => `¥${amount.toLocaleString()}`
    },
    {
      title: '已收金额',
      dataIndex: 'paidAmount',
      key: 'paidAmount',
      width: 120,
      render: (amount: number) => `¥${amount.toLocaleString()}`
    },
    {
      title: '剩余金额',
      dataIndex: 'remainingAmount',
      key: 'remainingAmount',
      width: 120,
      render: (amount: number) => (
        <span style={{ fontWeight: 'bold', color: amount > 0 ? '#cf1322' : '#3f8600' }}>
          ¥{amount.toLocaleString()}
        </span>
      )
    },
    {
      title: '逾期天数',
      dataIndex: 'overdueDays',
      key: 'overdueDays',
      width: 100,
      render: (days: number) => (
        <span style={{ color: days > 0 ? '#ff4d4f' : '#52c41a' }}>
          {days > 0 ? `${days}天` : '正常'}
        </span>
      ),
      sorter: (a, b) => a.overdueDays - b.overdueDays
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => getReceivableStatusTag(status)
    },
    {
      title: '风险等级',
      dataIndex: 'riskLevel',
      key: 'riskLevel',
      width: 100,
      render: (level: string) => getRiskLevelTag(level)
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button 
            type="link" 
            icon={<EyeOutlined />} 
            onClick={() => handleViewReceivableDetail(record)}
          >
            跟踪
          </Button>
        </Space>
      )
    }
  ]

  // 过滤后的数据
  const filteredPayments = paymentRecords.filter(payment => {
    const matchesSearch = !searchText ||
      payment.paymentNumber.toLowerCase().includes(searchText.toLowerCase()) ||
      payment.customerName.toLowerCase().includes(searchText.toLowerCase())

    const matchesStatus = !filterStatus || payment.status === filterStatus
    const matchesMethod = !filterMethod || payment.paymentMethod === filterMethod

    return matchesSearch && matchesStatus && matchesMethod
  })

  const filteredReceivables = receivables.filter(receivable => {
    const matchesSearch = !searchText ||
      receivable.customerName.toLowerCase().includes(searchText.toLowerCase()) ||
      receivable.orderNumber.toLowerCase().includes(searchText.toLowerCase()) ||
      receivable.invoiceNumber.toLowerCase().includes(searchText.toLowerCase())

    return matchesSearch
  })

  // 统计数据
  const paymentStats = {
    total: paymentRecords.length,
    totalAmount: paymentRecords.reduce((sum, p) => sum + p.paymentAmount, 0),
    pending: paymentRecords.filter(p => p.status === 'pending').length,
    confirmed: paymentRecords.filter(p => p.status === 'confirmed').length
  }

  const receivableStats = {
    total: receivables.length,
    totalAmount: receivables.reduce((sum, r) => sum + r.remainingAmount, 0),
    overdue: receivables.filter(r => r.status === 'overdue').length,
    highRisk: receivables.filter(r => r.riskLevel === 'high').length
  }

  const handleCreatePayment = () => {
    setEditingPayment(null)
    setIsModalVisible(true)
    form.resetFields()
  }

  const handleEditPayment = (payment: PaymentRecord) => {
    setEditingPayment(payment)
    setIsModalVisible(true)
    form.setFieldsValue({
      ...payment,
      paymentDate: dayjs(payment.paymentDate)
    })
  }

  const handleViewPaymentDetail = (payment: PaymentRecord) => {
    setSelectedPayment(payment)
    setIsDetailModalVisible(true)
  }

  const handleViewReceivableDetail = (receivable: AccountReceivable) => {
    setSelectedReceivable(receivable)
    setIsReceivableModalVisible(true)
  }

  const handleDeletePayment = (id: string) => {
    setPaymentRecords(paymentRecords.filter(p => p.id !== id))
    message.success('收款记录删除成功')
  }

  const handleModalOk = () => {
    form.validateFields().then(values => {
      const now = new Date().toISOString()
      const formattedValues = {
        ...values,
        paymentDate: values.paymentDate.format('YYYY-MM-DD')
      }

      if (editingPayment) {
        const updatedPayments = paymentRecords.map(p =>
          p.id === editingPayment.id
            ? { ...p, ...formattedValues, updatedAt: now }
            : p
        )
        setPaymentRecords(updatedPayments)
        message.success('收款记录更新成功')
      } else {
        const newPayment: PaymentRecord = {
          id: Date.now().toString(),
          paymentNumber: `PAY-${new Date().getFullYear()}-${String(paymentRecords.length + 1).padStart(3, '0')}`,
          ...formattedValues,
          createdAt: now,
          updatedAt: now
        }
        setPaymentRecords([...paymentRecords, newPayment])
        message.success('收款记录创建成功')
      }
      setIsModalVisible(false)
      form.resetFields()
    })
  }

  const handleModalCancel = () => {
    setIsModalVisible(false)
    form.resetFields()
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: styleHelpers.spacing.lg }}>
      {/* 页面标题 */}
      <div className="page-header">
        <h1 className="page-title">收款管理</h1>
        <p className="page-description">收款记录管理、应收账款监控、账款跟踪时间轴</p>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="收款总额"
              value={paymentStats.totalAmount}
              precision={0}
              prefix="¥"
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="应收余额"
              value={receivableStats.totalAmount}
              precision={0}
              prefix="¥"
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="逾期账款"
              value={receivableStats.overdue}
              suffix="笔"
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="高风险客户"
              value={receivableStats.highRisk}
              suffix="个"
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 标签页切换 */}
      <Card>
        <div style={{ display: 'flex', gap: styleHelpers.spacing.md, marginBottom: styleHelpers.spacing.md }}>
          <Button
            type={activeTab === 'payments' ? 'primary' : 'default'}
            onClick={() => setActiveTab('payments')}
          >
            收款记录
          </Button>
          <Button
            type={activeTab === 'receivables' ? 'primary' : 'default'}
            onClick={() => setActiveTab('receivables')}
          >
            应收账款
          </Button>
        </div>

        {/* 操作区域 */}
        <Row gutter={[16, 16]} align="middle" justify="space-between">
          <Col xs={24} lg={18}>
            <Space wrap size="middle">
              <Input
                placeholder={activeTab === 'payments' ? "搜索收款单号或客户名称" : "搜索客户名称、订单号或发票号"}
                prefix={<SearchOutlined />}
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                style={{ width: '256px' }}
              />
              {activeTab === 'payments' && (
                <>
                  <FormSelect
                    placeholder="收款状态"
                    value={filterStatus}
                    onChange={setFilterStatus}
                    style={{ width: '128px' }}
                    allowClear
                  >
                    <Option value="pending">待确认</Option>
                    <Option value="confirmed">已确认</Option>
                    <Option value="reconciled">已对账</Option>
                  </FormSelect>
                  <FormSelect
                    placeholder="付款方式"
                    value={filterMethod}
                    onChange={setFilterMethod}
                    style={{ width: '128px' }}
                  allowClear
                >
                  <Option value="cash">现金</Option>
                  <Option value="bank_transfer">银行转账</Option>
                  <Option value="check">支票</Option>
                  <Option value="acceptance">承兑汇票</Option>
                  </FormSelect>
                </>
              )}
            </Space>
          </Col>
          <Col xs={24} lg={6}>
            <Space>
              <Button icon={<ExportOutlined />}>导出</Button>
              {activeTab === 'payments' && (
                <Button type="primary" icon={<PlusOutlined />} onClick={handleCreatePayment}>
                  新建收款
                </Button>
              )}
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 数据表格 */}
      <Card title={activeTab === 'payments' ? '收款记录' : '应收账款'}>
        <Table
          columns={activeTab === 'payments' ? paymentColumns : receivableColumns as any}
          dataSource={activeTab === 'payments' ? filteredPayments : filteredReceivables as any}
          rowKey="id"
          loading={loading}
          pagination={{
            total: activeTab === 'payments' ? filteredPayments.length : filteredReceivables.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          scroll={{ x: 1400 }}
        />
      </Card>

      {/* 新建/编辑收款记录模态框 */}
      <Modal
        title={editingPayment ? '编辑收款记录' : '新建收款记录'}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={800}
        okText="确认"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            paymentMethod: 'bank_transfer',
            status: 'pending'
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="customerId"
                label="客户ID"
                rules={[{ required: true, message: '请输入客户ID' }]}
              >
                <Input placeholder="请输入客户ID" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="customerName"
                label="客户名称"
                rules={[{ required: true, message: '请输入客户名称' }]}
              >
                <Input placeholder="请输入客户名称" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="paymentDate"
                label="收款日期"
                rules={[{ required: true, message: '请选择收款日期' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="paymentAmount"
                label="收款金额"
                rules={[{ required: true, message: '请输入收款金额' }]}
              >
                <InputNumber
                  placeholder="请输入收款金额"
                  min={0}
                  style={{ width: '100%' }}
                  formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={value => value!.replace(/¥\s?|(,*)/g, '') as any}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="paymentMethod"
                label="付款方式"
                rules={[{ required: true, message: '请选择付款方式' }]}
              >
                <Select>
                  <Option value="cash">现金</Option>
                  <Option value="bank_transfer">银行转账</Option>
                  <Option value="check">支票</Option>
                  <Option value="acceptance">承兑汇票</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="bankAccount"
                label="收款银行账户"
              >
                <Input placeholder="请输入收款银行账户" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="referenceNumber"
            label="参考号/流水号"
          >
            <Input placeholder="请输入转账流水号或参考号" />
          </Form.Item>

          <Form.Item
            name="remark"
            label="备注"
          >
            <TextArea rows={3} placeholder="请输入备注信息" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default PaymentManagement
