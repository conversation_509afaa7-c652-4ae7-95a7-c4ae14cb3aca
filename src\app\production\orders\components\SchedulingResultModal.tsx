'use client'

import React, { useState } from 'react'
import { Modal, Table, Card, Space, Typography, Tag, Alert, Statistic, Row, Col, Tabs, Badge, message } from 'antd'
import {
  CheckCircleOutlined,
  WarningOutlined,
  ClockCircleOutlined,
  ToolOutlined,
  ExclamationCircleOutlined,
  CalendarOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import {
  SchedulingExecutionResult,
  SchedulingResult,
  ConflictWarning,
  RiskAssessment
} from '@/services/scheduling/SameMoldPrioritySchedulingService'

const { Title, Text } = Typography
const { TabPane } = Tabs

interface SchedulingResultModalProps {
  open: boolean
  onCancel: () => void
  onConfirm: () => void
  result: SchedulingExecutionResult | null
  loading?: boolean
}

const SchedulingResultModal: React.FC<SchedulingResultModalProps> = ({
  open,
  onCancel,
  onConfirm,
  result,
  loading = false
}) => {
  const [activeTab, setActiveTab] = useState('results')

  // 当前始终为预览模式（计算阶段的结果）
  const isPreviewMode = true

  if (!result) {
    return null
  }

  // 排程结果表格列定义
  const resultColumns: ColumnsType<SchedulingResult> = [
    {
      title: '工单ID',
      dataIndex: 'workOrderId',
      key: 'workOrderId',
      width: 120,
      render: (text) => <Text code>{text.slice(-8)}</Text>
    },
    {
      title: '分配工位',
      dataIndex: 'workstation',
      key: 'workstation',
      width: 150,
      render: (text, record) => (
        <Space>
          <Tag color={record.isSameMold ? 'green' : 'blue'}>
            {record.workstationCode}
          </Tag>
          <Text>{text}</Text>
        </Space>
      )
    },
    {
      title: '模具匹配',
      dataIndex: 'isSameMold',
      key: 'isSameMold',
      width: 100,
      render: (isSameMold) => (
        <Tag color={isSameMold ? 'green' : 'orange'} icon={<ToolOutlined />}>
          {isSameMold ? '相同模具' : '换模'}
        </Tag>
      )
    },
    {
      title: '预计开始时间',
      dataIndex: 'plannedStartTime',
      key: 'plannedStartTime',
      width: 150,
      render: (time) => (
        <Space direction="vertical" size={0}>
          <Text>{new Date(time).toLocaleDateString()}</Text>
          <Text type="secondary" style={{ fontSize: 12 }}>
            {new Date(time).toLocaleTimeString()}
          </Text>
        </Space>
      )
    },
    {
      title: '预计结束时间',
      dataIndex: 'plannedEndTime',
      key: 'plannedEndTime',
      width: 150,
      render: (time) => (
        <Space direction="vertical" size={0}>
          <Text>{new Date(time).toLocaleDateString()}</Text>
          <Text type="secondary" style={{ fontSize: 12 }}>
            {new Date(time).toLocaleTimeString()}
          </Text>
        </Space>
      )
    },
    {
      title: '生产时长',
      dataIndex: 'productionTimeMinutes',
      key: 'productionTimeMinutes',
      width: 100,
      render: (minutes) => (
        <Text>{Math.round(minutes / 60 * 10) / 10} 小时</Text>
      )
    },
    {
      title: '换模时间',
      dataIndex: 'changeoverTimeMinutes',
      key: 'changeoverTimeMinutes',
      width: 100,
      render: (minutes) => (
        <Text type={minutes > 0 ? 'warning' : 'secondary'}>
          {minutes} 分钟
        </Text>
      )
    }
  ]

  // 冲突警告表格列定义
  const conflictColumns: ColumnsType<ConflictWarning> = [
    {
      title: '冲突类型',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      render: (type) => {
        const typeMap = {
          'time_overlap': { text: '时间重叠', color: 'red' },
          'workstation_conflict': { text: '工位冲突', color: 'orange' },
          'mold_conflict': { text: '模具冲突', color: 'yellow' }
        }
        const config = typeMap[type as keyof typeof typeMap] || { text: type, color: 'default' }
        return <Tag color={config.color}>{config.text}</Tag>
      }
    },
    {
      title: '涉及工位',
      dataIndex: 'workstation',
      key: 'workstation',
      width: 150
    },
    {
      title: '冲突工单',
      dataIndex: 'conflictingTasks',
      key: 'conflictingTasks',
      width: 200,
      render: (tasks: string[]) => (
        <Space wrap>
          {tasks.map(task => (
            <Tag key={task} color="red">{task.slice(-8)}</Tag>
          ))}
        </Space>
      )
    },
    {
      title: '重叠时间',
      dataIndex: 'overlapTime',
      key: 'overlapTime',
      width: 100,
      render: (time) => time ? `${time} 分钟` : '-'
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    }
  ]

  // 风险评估表格列定义
  const riskColumns: ColumnsType<RiskAssessment> = [
    {
      title: '工单ID',
      dataIndex: 'workOrderId',
      key: 'workOrderId',
      width: 120,
      render: (text) => <Text code>{text.slice(-8)}</Text>
    },
    {
      title: '风险等级',
      dataIndex: 'riskLevel',
      key: 'riskLevel',
      width: 100,
      render: (level) => {
        const levelMap = {
          'normal': { text: '正常', color: 'green', icon: <CheckCircleOutlined /> },
          'medium': { text: '中风险', color: 'orange', icon: <WarningOutlined /> },
          'high': { text: '高风险', color: 'red', icon: <ExclamationCircleOutlined /> }
        }
        const config = levelMap[level as keyof typeof levelMap] || { text: level, color: 'default', icon: null }
        return (
          <Tag color={config.color} icon={config.icon}>
            {config.text}
          </Tag>
        )
      }
    },
    {
      title: '延误/提前',
      key: 'timeDiff',
      width: 120,
      render: (_, record) => {
        if (record.delayDays) {
          return <Text type="danger">延误 {record.delayDays} 天</Text>
        } else if (record.advanceDays) {
          return <Text type="success">提前 {record.advanceDays} 天</Text>
        }
        return <Text>按时</Text>
      }
    },
    {
      title: '预计完成日期',
      dataIndex: 'expectedCompletionDate',
      key: 'expectedCompletionDate',
      width: 120,
      render: (date) => new Date(date).toLocaleDateString()
    },
    {
      title: '建议',
      dataIndex: 'suggestion',
      key: 'suggestion',
      ellipsis: true,
      render: (text) => text || '-'
    }
  ]

  // 获取风险统计
  const getRiskStats = () => {
    const normal = result.riskAssessments.filter(r => r.riskLevel === 'normal').length
    const medium = result.riskAssessments.filter(r => r.riskLevel === 'medium').length
    const high = result.riskAssessments.filter(r => r.riskLevel === 'high').length
    return { normal, medium, high }
  }

  const riskStats = getRiskStats()

  return (
    <Modal
      title={
        <Space>
          {isPreviewMode ? (
            <ClockCircleOutlined style={{ color: '#1890ff' }} />
          ) : (
            <CheckCircleOutlined style={{ color: '#52c41a' }} />
          )}
          <span>排程结果</span>
          {isPreviewMode && (
            <Tag color="blue" icon={<ClockCircleOutlined />}>
              预览模式
            </Tag>
          )}
        </Space>
      }
      open={open}
      onCancel={onCancel}
      onOk={onConfirm}
      confirmLoading={loading}
      width={1200}
      okText={isPreviewMode ? "确认应用排程结果" : "重新计算"}
      cancelText="取消"
      style={{ top: 20 }}
    >
      {/* 预览模式说明 */}
      {isPreviewMode && (
        <Alert
          message="📋 排程预览模式"
          description={
            <div>
              <p>当前显示的是排程计算结果预览，<strong>工位状态和工单状态尚未实际更新</strong>。</p>
              <p>• 工位队列任务：未添加</p>
              <p>• 工单状态：仍为&ldquo;待开始&rdquo;</p>
              <p>• 工位状态：保持原有状态</p>
              <p style={{ marginBottom: 0, marginTop: 8 }}>
                <strong>点击&ldquo;确认应用排程结果&rdquo;后才会正式应用所有更改。</strong>
              </p>
            </div>
          }
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {/* 统计概览 */}
      <Card size="small" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={4}>
            <Statistic
              title="总工单数"
              value={result.statistics.totalWorkOrders}
              suffix="个"
              valueStyle={{ color: '#1890ff' }}
            />
          </Col>
          <Col span={4}>
            <Statistic
              title="成功排程"
              value={result.statistics.successfullyScheduled}
              suffix="个"
              valueStyle={{ color: '#52c41a' }}
            />
          </Col>
          <Col span={4}>
            <Statistic
              title="相同模具"
              value={result.statistics.sameMoldAssignments}
              suffix="个"
              valueStyle={{ color: '#722ed1' }}
            />
          </Col>
          <Col span={4}>
            <Statistic
              title="涉及工位"
              value={result.statistics.involvedWorkstations}
              suffix="个"
              valueStyle={{ color: '#faad14' }}
            />
          </Col>
          <Col span={4}>
            <Statistic
              title="正常风险"
              value={riskStats.normal}
              suffix="个"
              valueStyle={{ color: '#52c41a' }}
            />
          </Col>
          <Col span={4}>
            <Statistic
              title="风险工单"
              value={riskStats.medium + riskStats.high}
              suffix="个"
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Col>
        </Row>
      </Card>

      {/* 详细结果标签页 */}
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane
          tab={
            <Space>
              <ClockCircleOutlined />
              <span>排程结果</span>
              <Badge count={result.schedulingResults.length} showZero />
            </Space>
          }
          key="results"
        >
          <Table
            columns={resultColumns}
            dataSource={result.schedulingResults}
            rowKey="workOrderId"
            size="small"
            pagination={{ pageSize: 10 }}
            scroll={{ y: 400 }}
          />
        </TabPane>

        <TabPane
          tab={
            <Space>
              <WarningOutlined />
              <span>冲突警告</span>
              <Badge count={result.conflicts.length} showZero />
            </Space>
          }
          key="conflicts"
        >
          {result.conflicts.length > 0 ? (
            <Table
              columns={conflictColumns}
              dataSource={result.conflicts}
              rowKey={(record, index) => `conflict-${index}`}
              size="small"
              pagination={{ pageSize: 10 }}
              scroll={{ y: 400 }}
            />
          ) : (
            <Alert
              message="无冲突"
              description="排程结果无时间冲突或工位冲突"
              type="success"
              showIcon
            />
          )}
        </TabPane>

        <TabPane
          tab={
            <Space>
              <CalendarOutlined />
              <span>风险评估</span>
              <Badge count={riskStats.medium + riskStats.high} showZero />
            </Space>
          }
          key="risks"
        >
          <Table
            columns={riskColumns}
            dataSource={result.riskAssessments}
            rowKey="workOrderId"
            size="small"
            pagination={{ pageSize: 10 }}
            scroll={{ y: 400 }}
          />
        </TabPane>
      </Tabs>

      {/* 底部提示 */}
      {result.conflicts.length > 0 && (
        <Alert
          message="发现排程冲突"
          description="系统检测到时间冲突，建议检查冲突详情并考虑调整排程参数"
          type="warning"
          showIcon
          style={{ marginTop: 16 }}
        />
      )}

      {/* 预览模式底部提示 */}
      {isPreviewMode && (
        <Alert
          message="💡 温馨提示"
          description={
            <div>
              <p>这是排程计算的预览结果，您可以：</p>
              <ul style={{ marginBottom: 0, paddingLeft: 20 }}>
                <li><strong>确认应用</strong>：将排程结果应用到实际系统，更新工单和工位状态</li>
                <li><strong>取消</strong>：放弃此次排程，不做任何更改</li>
                <li><strong>查看详情</strong>：检查排程结果、冲突和风险评估</li>
              </ul>
            </div>
          }
          type="success"
          showIcon
          style={{ marginTop: 16 }}
        />
      )}
    </Modal>
  )
}

export default SchedulingResultModal
