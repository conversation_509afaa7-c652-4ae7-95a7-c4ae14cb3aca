/**
 * 登录表单组件
 * 
 * 提供用户登录表单，包含用户名、密码输入和记住我选项
 * 遵循PRD文档中的表单验证和安全要求
 */

'use client'

import React, { useState } from 'react'
import { Form, Input, Button, Checkbox, Alert, message } from 'antd'
import { UserOutlined, LockOutlined, EyeInvisibleOutlined, EyeTwoTone } from '@ant-design/icons'
import { LoginFormProps, LoginFormData, User } from '@/types/auth'

/**
 * 登录表单组件
 */
const LoginForm: React.FC<LoginFormProps> = ({ 
  onSuccess, 
  onError, 
  loading: externalLoading 
}) => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string>('')

  /**
   * 处理表单提交
   */
  const handleSubmit = async (values: LoginFormData) => {
    try {
      setLoading(true)
      setError('')

      // 调用登录API
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: values.username,
          password: values.password,
          rememberMe: values.rememberMe || false
        }),
      })

      const result = await response.json()

      if (result.status === 'success') {
        // 登录成功
        message.success('登录成功！')
        onSuccess?.(result.data.user)
      } else {
        // 登录失败
        const errorMessage = result.message || '登录失败，请重试'
        setError(errorMessage)
        onError?.(errorMessage)
        
        // 根据错误类型显示不同的提示
        if (result.code === 'ACCOUNT_LOCKED') {
          message.error('账户已被锁定，请30分钟后重试或联系管理员')
        } else if (result.code === 'INVALID_CREDENTIALS') {
          message.error('用户名或密码错误')
        } else if (result.code === 'ACCOUNT_INACTIVE') {
          message.error('账户未激活，请联系管理员')
        } else {
          message.error(errorMessage)
        }
      }
    } catch (error) {
      console.error('登录请求失败:', error)
      const errorMessage = '网络错误，请检查网络连接后重试'
      setError(errorMessage)
      onError?.(errorMessage)
      message.error(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  /**
   * 处理表单验证失败
   */
  const handleSubmitFailed = (errorInfo: any) => {
    console.log('表单验证失败:', errorInfo)
    message.warning('请检查输入信息')
  }

  const isLoading = loading || externalLoading

  return (
    <div className="w-full">
      {/* 错误提示 */}
      {error && (
        <Alert
          message={error}
          type="error"
          showIcon
          closable
          onClose={() => setError('')}
          className="mb-4"
        />
      )}

      {/* 登录表单 */}
      <Form
        form={form}
        name="loginForm"
        layout="vertical"
        size="large"
        onFinish={handleSubmit}
        onFinishFailed={handleSubmitFailed}
        autoComplete="off"
        disabled={isLoading}
      >
        {/* 用户名输入 */}
        <Form.Item
          name="username"
          label="用户名"
          rules={[
            { required: true, message: '请输入用户名' },
            { min: 3, message: '用户名至少3个字符' },
            { max: 50, message: '用户名不能超过50个字符' },
            { 
              pattern: /^[a-zA-Z0-9_]+$/, 
              message: '用户名只能包含字母、数字和下划线' 
            }
          ]}
        >
          <Input
            prefix={<UserOutlined className="text-gray-400" />}
            placeholder="请输入用户名"
            autoComplete="username"
            className="h-12"
          />
        </Form.Item>

        {/* 密码输入 */}
        <Form.Item
          name="password"
          label="密码"
          rules={[
            { required: true, message: '请输入密码' },
            { min: 6, message: '密码至少6个字符' },
            { max: 128, message: '密码不能超过128个字符' }
          ]}
        >
          <Input.Password
            prefix={<LockOutlined className="text-gray-400" />}
            placeholder="请输入密码"
            autoComplete="current-password"
            className="h-12"
            iconRender={(visible) => 
              visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
            }
          />
        </Form.Item>

        {/* 记住我选项 */}
        <Form.Item name="rememberMe" valuePropName="checked" className="mb-6">
          <Checkbox>
            记住我（7天内免登录）
          </Checkbox>
        </Form.Item>

        {/* 登录按钮 */}
        <Form.Item className="mb-0">
          <Button
            type="primary"
            htmlType="submit"
            loading={isLoading}
            className="w-full h-12 text-base font-medium"
            size="large"
          >
            {isLoading ? '登录中...' : '登录'}
          </Button>
        </Form.Item>
      </Form>

      {/* 开发环境测试提示 */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mt-6 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
          <div className="text-yellow-800 text-sm">
            <div className="font-medium mb-1">开发环境测试账户：</div>
            <div>用户名: admin</div>
            <div>密码: admin123</div>
          </div>
        </div>
      )}
    </div>
  )
}

export default LoginForm
