'use client'

import React, { useState } from 'react'
import { Modal, Form, Radio, Card, Space, Typography, Alert, Statistic, Row, Col, message, InputNumber } from 'antd'
import {
  ClockCircleOutlined,
  ToolOutlined,
  CalendarOutlined,
  WarningOutlined
} from '@ant-design/icons'
import { ProductionWorkOrder } from '@/types'

const { Title, Text, Paragraph } = Typography

// 排程模式类型
export type SchedulingMode = 'same_mold_priority' | 'delivery_priority'

// 排程配置
export interface SchedulingConfig {
  mode: SchedulingMode
  changeoverTime: number // 换模时间（分钟）
  allowOvertime: boolean // 允许加班
  maxOvertimeHours: number // 最大加班时间
  sameMoldDeliveryToleranceDays: number // 相同模具交货期容忍天数
}

// Modal属性
interface SchedulingConfigModalProps {
  open: boolean
  onCancel: () => void
  onConfirm: (config: SchedulingConfig) => void
  selectedWorkOrders: ProductionWorkOrder[]
  loading?: boolean
}

const SchedulingConfigModal: React.FC<SchedulingConfigModalProps> = ({
  open,
  onCancel,
  onConfirm,
  selectedWorkOrders,
  loading = false
}) => {
  const [form] = Form.useForm()
  const [selectedMode, setSelectedMode] = useState<SchedulingMode>('same_mold_priority')

  // 计算统计信息
  const statistics = React.useMemo(() => {
    const totalWorkOrders = selectedWorkOrders.length
    const totalMoldCount = selectedWorkOrders.reduce((sum, wo) => sum + wo.plannedMoldCount, 0)
    const uniqueMolds = new Set(selectedWorkOrders.map(wo => wo.formingMoldNumber)).size
    const avgHourlyCapacity = selectedWorkOrders.length > 0 
      ? Math.round(selectedWorkOrders.reduce((sum, wo) => sum + wo.hourlyCapacity, 0) / selectedWorkOrders.length)
      : 0

    return {
      totalWorkOrders,
      totalMoldCount,
      uniqueMolds,
      avgHourlyCapacity
    }
  }, [selectedWorkOrders])

  // 处理确认
  const handleConfirm = async () => {
    try {
      const values = await form.validateFields()
      const config: SchedulingConfig = {
        mode: selectedMode,
        changeoverTime: 20, // 固定20分钟换模时间
        allowOvertime: values.allowOvertime || false,
        maxOvertimeHours: values.maxOvertimeHours || 4,
        sameMoldDeliveryToleranceDays: values.sameMoldDeliveryToleranceDays || 30
      }
      onConfirm(config)
    } catch (error) {
      message.error('请完善排程配置信息')
    }
  }

  // 模式描述
  const getModeDescription = (mode: SchedulingMode) => {
    switch (mode) {
      case 'same_mold_priority':
        return {
          title: '相同模具优先',
          description: '优先将工单分配到已装载相同模具的工位，减少换模时间，提高生产效率',
          advantages: [
            '减少换模次数，节省换模时间',
            '提高工位利用率',
            '降低生产成本',
            '适合批量生产场景'
          ],
          icon: <ToolOutlined style={{ color: '#1890ff' }} />
        }
      case 'delivery_priority':
        return {
          title: '交期优先',
          description: '严格按照交货期排序，优先安排紧急订单，确保按时交付',
          advantages: [
            '确保交货期准时性',
            '提高客户满意度',
            '降低延期风险',
            '适合多品种小批量场景'
          ],
          icon: <CalendarOutlined style={{ color: '#52c41a' }} />
        }
      default:
        return {
          title: '',
          description: '',
          advantages: [],
          icon: null
        }
    }
  }

  const currentModeInfo = getModeDescription(selectedMode)

  return (
    <Modal
      title={
        <Space>
          <ClockCircleOutlined />
          <span>生产排程配置</span>
        </Space>
      }
      open={open}
      onCancel={onCancel}
      onOk={handleConfirm}
      confirmLoading={loading}
      width={800}
      okText="开始排程"
      cancelText="取消"
      destroyOnHidden
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          mode: 'same_mold_priority',
          allowOvertime: false,
          maxOvertimeHours: 4,
          sameMoldDeliveryToleranceDays: 30
        }}
      >
        {/* 工单统计信息 */}
        <Card size="small" style={{ marginBottom: 16 }}>
          <Title level={5} style={{ margin: 0, marginBottom: 12 }}>
            待排程工单统计
          </Title>
          <Row gutter={16}>
            <Col span={6}>
              <Statistic
                title="工单数量"
                value={statistics.totalWorkOrders}
                suffix="个"
                valueStyle={{ color: '#1890ff' }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="总计划模数"
                value={statistics.totalMoldCount}
                suffix="模"
                valueStyle={{ color: '#52c41a' }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="涉及模具"
                value={statistics.uniqueMolds}
                suffix="套"
                valueStyle={{ color: '#faad14' }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="平均产能"
                value={statistics.avgHourlyCapacity}
                suffix="模/时"
                valueStyle={{ color: '#722ed1' }}
              />
            </Col>
          </Row>
        </Card>

        {/* 排程模式选择 */}
        <Form.Item
          name="mode"
          label={
            <Space>
              <ToolOutlined />
              <span>排程模式</span>
            </Space>
          }
          rules={[{ required: true, message: '请选择排程模式' }]}
        >
          <Radio.Group
            value={selectedMode}
            onChange={(e) => setSelectedMode(e.target.value)}
            style={{ width: '100%' }}
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              <Radio value="same_mold_priority">
                <Card
                  size="small"
                  hoverable
                  style={{
                    width: '100%',
                    border: selectedMode === 'same_mold_priority' ? '2px solid #1890ff' : '1px solid #d9d9d9'
                  }}
                >
                  <Space>
                    <ToolOutlined style={{ color: '#1890ff', fontSize: 20 }} />
                    <div>
                      <Text strong>相同模具优先</Text>
                      <br />
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        优先分配到相同模具工位，减少换模时间
                      </Text>
                    </div>
                  </Space>
                </Card>
              </Radio>
              
              <Radio value="delivery_priority">
                <Card
                  size="small"
                  hoverable
                  style={{
                    width: '100%',
                    border: selectedMode === 'delivery_priority' ? '2px solid #1890ff' : '1px solid #d9d9d9'
                  }}
                >
                  <Space>
                    <CalendarOutlined style={{ color: '#52c41a', fontSize: 20 }} />
                    <div>
                      <Text strong>交期优先</Text>
                      <br />
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        严格按照交货期排序，确保按时交付
                      </Text>
                    </div>
                  </Space>
                </Card>
              </Radio>
            </Space>
          </Radio.Group>
        </Form.Item>

        {/* 模式详细说明 */}
        <Card size="small" style={{ marginBottom: 16 }}>
          <Space>
            {currentModeInfo.icon}
            <Title level={5} style={{ margin: 0 }}>
              {currentModeInfo.title}
            </Title>
          </Space>
          <Paragraph style={{ marginTop: 8, marginBottom: 12 }}>
            {currentModeInfo.description}
          </Paragraph>
          <Text strong>主要优势：</Text>
          <ul style={{ marginTop: 4, marginBottom: 0 }}>
            {currentModeInfo.advantages.map((advantage, index) => (
              <li key={index}>
                <Text>{advantage}</Text>
              </li>
            ))}
          </ul>
        </Card>

        {/* 排程参数 */}
        <Card size="small" title="排程参数">
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item label="换模时间">
                <Text>20 分钟（固定）</Text>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="工作时间配置">
                <Text>使用系统默认配置</Text>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="相同模具交货期容忍天数"
                name="sameMoldDeliveryToleranceDays"
                tooltip="交货期相差在此天数内的相同模具工单将被分配到同一工位，减少换模次数"
                rules={[
                  { required: true, message: '请输入容忍天数' },
                  { type: 'number', min: 1, max: 90, message: '容忍天数必须在1-90天之间' }
                ]}
              >
                <InputNumber
                  min={1}
                  max={90}
                  suffix="天"
                  placeholder="请输入天数"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* 注意事项 */}
        <Alert
          message="排程注意事项"
          description={
            <ul style={{ margin: 0, paddingLeft: 16 }}>
              <li>排程将自动分配工位和时间，请确认工单信息准确</li>
              <li>排程完成后，工单状态将变更为&ldquo;已排程&rdquo;</li>
              <li>如有冲突或风险，系统将提供详细的警告信息</li>
              <li>排程结果可在排程完成后进行调整</li>
            </ul>
          }
          type="info"
          showIcon
          icon={<WarningOutlined />}
          style={{ marginTop: 16 }}
        />
      </Form>
    </Modal>
  )
}

export default SchedulingConfigModal
