"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_services_dataAccess_DataConsistencyService_ts";
exports.ids = ["_ssr_src_services_dataAccess_DataConsistencyService_ts"];
exports.modules = {

/***/ "(ssr)/./src/services/dataAccess/DataConsistencyService.ts":
/*!***********************************************************!*\
  !*** ./src/services/dataAccess/DataConsistencyService.ts ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DataConsistencyService: () => (/* binding */ DataConsistencyService),\n/* harmony export */   dataConsistencyService: () => (/* binding */ dataConsistencyService)\n/* harmony export */ });\n/* harmony import */ var _DataAccessManager__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./DataAccessManager */ \"(ssr)/./src/services/dataAccess/DataAccessManager.ts\");\n/* harmony import */ var _utils_business__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/business */ \"(ssr)/./src/utils/business/index.ts\");\n/**\n * 数据一致性验证服务\n * 检查和维护跨模块数据的一致性\n * 符合\"模块间的接口和数据交互标准\"文件要求\n */ \n\n/**\n * 数据一致性验证服务\n */ class DataConsistencyService {\n    constructor(){}\n    static getInstance() {\n        if (!DataConsistencyService.instance) {\n            DataConsistencyService.instance = new DataConsistencyService();\n        }\n        return DataConsistencyService.instance;\n    }\n    /**\n   * 执行全面的数据一致性检查\n   */ async performFullConsistencyCheck() {\n        const results = [];\n        // 检查产品-库存一致性\n        const productInventoryCheck = await this.checkProductInventoryConsistency();\n        results.push(productInventoryCheck);\n        // 检查订单-客户一致性\n        const orderCustomerCheck = await this.checkOrderCustomerConsistency();\n        results.push(orderCustomerCheck);\n        // 检查订单-员工一致性\n        const orderEmployeeCheck = await this.checkOrderEmployeeConsistency();\n        results.push(orderEmployeeCheck);\n        // 计算总体统计\n        const totalIssues = results.reduce((sum, result)=>sum + result.inconsistentItems, 0);\n        const criticalIssues = results.reduce((sum, result)=>sum + result.issues.filter((issue)=>issue.severity === \"critical\").length, 0);\n        const overall = {\n            totalIssues,\n            criticalIssues,\n            // ✅ 使用统一时间戳生成器\n            timestamp: _utils_business__WEBPACK_IMPORTED_MODULE_1__.timestampGenerator.now()\n        };\n        return {\n            overall,\n            results\n        };\n    }\n    /**\n   * 检查产品-库存数据一致性\n   */ async checkProductInventoryConsistency() {\n        const issues = [];\n        let totalChecked = 0;\n        try {\n            // 获取所有库存记录\n            const inventoryResponse = await _DataAccessManager__WEBPACK_IMPORTED_MODULE_0__.dataAccessManager.inventory.getAll();\n            if (inventoryResponse.status === \"success\" && inventoryResponse.data) {\n                const inventoryItems = inventoryResponse.data.items;\n                totalChecked = inventoryItems.length;\n                for (const inventory of inventoryItems){\n                    // 检查对应的产品是否存在\n                    const productResponse = await _DataAccessManager__WEBPACK_IMPORTED_MODULE_0__.dataAccessManager.products.getByCode(inventory.productCode);\n                    if (productResponse.status !== \"success\" || !productResponse.data) {\n                        issues.push({\n                            type: \"missing_reference\",\n                            severity: \"high\",\n                            entityId: inventory.id,\n                            description: `库存记录引用的产品编码 ${inventory.productCode} 不存在`,\n                            suggestedAction: \"删除孤立的库存记录或创建对应的产品记录\"\n                        });\n                        continue;\n                    }\n                    const product = productResponse.data;\n                    // 检查产品名称是否一致\n                    if (inventory.productName !== product.modelName) {\n                        issues.push({\n                            type: \"data_mismatch\",\n                            severity: \"medium\",\n                            entityId: inventory.id,\n                            description: `库存记录中的产品名称与产品数据不一致`,\n                            expectedValue: product.modelName,\n                            actualValue: inventory.productName,\n                            suggestedAction: \"同步产品名称到库存记录\"\n                        });\n                    }\n                    // 检查产品状态\n                    if (product.status !== \"active\") {\n                        issues.push({\n                            type: \"invalid_status\",\n                            severity: \"low\",\n                            entityId: inventory.id,\n                            description: `库存记录对应的产品状态为非活跃状态`,\n                            actualValue: product.status,\n                            suggestedAction: \"考虑清理非活跃产品的库存记录\"\n                        });\n                    }\n                }\n            }\n        } catch (error) {\n            issues.push({\n                type: \"missing_reference\",\n                severity: \"critical\",\n                entityId: \"system\",\n                description: `检查产品-库存一致性时发生错误: ${error}`,\n                suggestedAction: \"检查系统配置和网络连接\"\n            });\n        }\n        return {\n            module: \"inventory\",\n            entityType: \"product_inventory\",\n            totalChecked,\n            inconsistentItems: issues.length,\n            issues,\n            // ✅ 使用统一时间戳生成器\n            timestamp: _utils_business__WEBPACK_IMPORTED_MODULE_1__.timestampGenerator.now()\n        };\n    }\n    /**\n   * 检查订单-客户数据一致性\n   */ async checkOrderCustomerConsistency() {\n        const issues = [];\n        let totalChecked = 0;\n        try {\n        // 这里需要访问订单数据，暂时使用模拟数据\n        // 在实际实现中，需要添加订单数据访问接口\n        } catch (error) {}\n        return {\n            module: \"sales\",\n            entityType: \"order_customer\",\n            totalChecked,\n            inconsistentItems: issues.length,\n            issues,\n            // ✅ 使用统一时间戳生成器\n            timestamp: _utils_business__WEBPACK_IMPORTED_MODULE_1__.timestampGenerator.now()\n        };\n    }\n    /**\n   * 检查订单-员工数据一致性\n   */ async checkOrderEmployeeConsistency() {\n        const issues = [];\n        let totalChecked = 0;\n        try {\n        // 这里需要访问订单数据，暂时使用模拟数据\n        // 在实际实现中，需要添加订单数据访问接口\n        } catch (error) {}\n        return {\n            module: \"sales\",\n            entityType: \"order_employee\",\n            totalChecked,\n            inconsistentItems: issues.length,\n            issues,\n            // ✅ 使用统一时间戳生成器\n            timestamp: _utils_business__WEBPACK_IMPORTED_MODULE_1__.timestampGenerator.now()\n        };\n    }\n    /**\n   * 自动修复数据一致性问题\n   */ async autoRepairConsistencyIssues(issues) {\n        let repairedItems = 0;\n        let failedItems = 0;\n        const errors = [];\n        for (const issue of issues){\n            try {\n                switch(issue.type){\n                    case \"data_mismatch\":\n                        if (issue.entityId && issue.expectedValue) {\n                            await this.repairDataMismatch(issue);\n                            repairedItems++;\n                        }\n                        break;\n                    case \"orphaned_record\":\n                        await this.removeOrphanedRecord(issue);\n                        repairedItems++;\n                        break;\n                    default:\n                }\n            } catch (error) {\n                failedItems++;\n                errors.push(`修复问题 ${issue.entityId} 失败: ${error}`);\n            }\n        }\n        const result = {\n            success: failedItems === 0,\n            repairedItems,\n            failedItems,\n            errors,\n            // ✅ 使用统一时间戳生成器\n            timestamp: _utils_business__WEBPACK_IMPORTED_MODULE_1__.timestampGenerator.now()\n        };\n        return result;\n    }\n    /**\n   * 修复数据不匹配问题\n   */ async repairDataMismatch(issue) {\n        if (issue.description.includes(\"产品名称\")) {\n            // 修复库存记录中的产品名称\n            const inventoryResponse = await _DataAccessManager__WEBPACK_IMPORTED_MODULE_0__.dataAccessManager.inventory.getByProductCode(issue.entityId);\n            if (inventoryResponse.status === \"success\" && inventoryResponse.data) {\n                await _DataAccessManager__WEBPACK_IMPORTED_MODULE_0__.dataAccessManager.inventory.update(issue.entityId, {\n                    productName: issue.expectedValue\n                });\n            }\n        }\n    }\n    /**\n   * 移除孤立记录\n   */ async removeOrphanedRecord(issue) {\n    // 这里需要根据具体的业务逻辑实现孤立记录的移除\n    }\n    /**\n   * 验证特定实体的数据一致性\n   */ async validateEntityConsistency(module, entityType, entityId) {\n        const issues = [];\n        try {\n            switch(`${module}.${entityType}`){\n                case \"inventory.product_inventory\":\n                    const inventoryIssues = await this.validateInventoryItemConsistency(entityId);\n                    issues.push(...inventoryIssues);\n                    break;\n                default:\n            }\n        } catch (error) {\n            issues.push({\n                type: \"missing_reference\",\n                severity: \"critical\",\n                entityId,\n                description: `验证实体一致性时发生错误: ${error}`,\n                suggestedAction: \"检查系统配置\"\n            });\n        }\n        return issues;\n    }\n    /**\n   * 验证单个库存项的一致性\n   */ async validateInventoryItemConsistency(inventoryId) {\n        const issues = [];\n        try {\n            // 获取库存记录\n            const inventoryResponse = await _DataAccessManager__WEBPACK_IMPORTED_MODULE_0__.dataAccessManager.inventory.getByProductCode(inventoryId);\n            if (inventoryResponse.status !== \"success\" || !inventoryResponse.data) {\n                issues.push({\n                    type: \"missing_reference\",\n                    severity: \"high\",\n                    entityId: inventoryId,\n                    description: \"库存记录不存在\",\n                    suggestedAction: \"检查库存记录ID是否正确\"\n                });\n                return issues;\n            }\n            const inventory = inventoryResponse.data;\n            // 检查对应的产品\n            const productResponse = await _DataAccessManager__WEBPACK_IMPORTED_MODULE_0__.dataAccessManager.products.getByCode(inventory.productCode);\n            if (productResponse.status !== \"success\" || !productResponse.data) {\n                issues.push({\n                    type: \"missing_reference\",\n                    severity: \"high\",\n                    entityId: inventoryId,\n                    description: `库存记录引用的产品编码 ${inventory.productCode} 不存在`,\n                    suggestedAction: \"创建对应的产品记录或删除库存记录\"\n                });\n            } else {\n                const product = productResponse.data;\n                // 检查数据一致性\n                if (inventory.productName !== product.modelName) {\n                    issues.push({\n                        type: \"data_mismatch\",\n                        severity: \"medium\",\n                        entityId: inventoryId,\n                        description: \"产品名称不一致\",\n                        expectedValue: product.modelName,\n                        actualValue: inventory.productName,\n                        suggestedAction: \"同步产品名称\"\n                    });\n                }\n            }\n        } catch (error) {}\n        return issues;\n    }\n}\n// 导出默认实例\nconst dataConsistencyService = DataConsistencyService.getInstance();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/dataAccess/DataConsistencyService.ts\n");

/***/ })

};
;