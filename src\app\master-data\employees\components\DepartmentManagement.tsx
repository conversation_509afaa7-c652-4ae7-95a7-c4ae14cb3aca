'use client'

import React, { useState } from 'react'
import {
  Modal,
  Form,
  Input,
  Select,
  Tree,
  Button,
  Space,
  Card,
  Row,
  Col,
  message,
  Popconfirm,
  Tag} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  TeamOutlined} from '@ant-design/icons'
import type { DataNode } from 'antd/es/tree'
import { Department, Employee } from '@/types'
import { styleHelpers } from '@/utils/styles/antdHelpers'

const { Option } = Select
const { TextArea } = Input

interface DepartmentManagementProps {
  visible: boolean
  onClose: () => void
  departments: Department[]
  employees: Employee[]
  onDepartmentUpdate: (departments: Department[]) => void
  onEmployeeUpdate: (employees: Employee[]) => void
}

function DepartmentManagement({
  visible,
  onClose,
  departments,
  employees,
  onDepartmentUpdate,
  onEmployeeUpdate
}: DepartmentManagementProps) {
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [editingDepartment, setEditingDepartment] = useState<Department | null>(null)
  const [form] = Form.useForm()

  // 生成下一个部门编码
  const generateNextDepartmentCode = (): string => {
    const existingCodes = departments.map(dept => dept.departmentCode)
    const dCodes = existingCodes.filter(code => /^D\d{3,4}$/.test(code))
    
    if (dCodes.length === 0) {
      return 'D001'
    }
    
    const maxNumber = Math.max(...dCodes.map(code => {
      const match = code.match(/^D(\d{3,4})$/)
      return match ? parseInt(match[1], 10) : 0
    }))
    
    const nextNumber = maxNumber + 1
    
    if (nextNumber > 9999) {
      message.warning('编码已达到最大值D9999，请手动输入编码')
      return ''
    }
    
    // 3位数范围：D001 到 D999，4位数范围：D1000 到 D9999
    if (nextNumber <= 999) {
      return `D${nextNumber.toString().padStart(3, '0')}`
    } else {
      return `D${nextNumber.toString()}`
    }
  }

  // 检查部门编码唯一性
  const checkDepartmentCodeUniqueness = (code: string, excludeId?: string): boolean => {
    return !departments.some(dept => 
      dept.departmentCode === code && dept.id !== excludeId
    )
  }

  // 转换部门数据为树形结构
  const convertToTreeData = (departments: Department[]): DataNode[] => {
    const departmentMap = new Map<string, Department>()
    departments.forEach(dept => departmentMap.set(dept.id, dept))

    const rootDepartments = departments.filter(dept => !dept.parentDepartmentId)

    const buildTree = (dept: Department): DataNode => {
      const children = departments
        .filter(d => d.parentDepartmentId === dept.id)
        .map(buildTree)

      return {
        key: dept.id,
        title: (
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <span style={{ display: 'flex', alignItems: 'center' }}>
              <TeamOutlined style={{ marginRight: styleHelpers.spacing.sm }} />
              {dept.departmentName}
              <Tag color="blue" style={{ marginLeft: styleHelpers.spacing.sm }}>{dept.employeeCount}人</Tag>
            </span>
            <Space size="small">
              <Button 
                type="link" 
                size="small" 
                icon={<EditOutlined />}
                onClick={(e) => {
                  e.stopPropagation()
                  handleEdit(dept)
                }}
              >
                编辑
              </Button>
              <Popconfirm
                title="确定要删除这个部门吗？"
                onConfirm={(e) => {
                  e?.stopPropagation()
                  handleDelete(dept.id)
                }}
                okText="确定"
                cancelText="取消"
              >
                <Button 
                  type="link" 
                  size="small" 
                  danger 
                  icon={<DeleteOutlined />}
                  onClick={(e) => e.stopPropagation()}
                >
                  删除
                </Button>
              </Popconfirm>
            </Space>
          </div>
        ),
        children: children.length > 0 ? children : undefined
      }
    }

    return rootDepartments.map(buildTree)
  }

  const handleCreate = () => {
    setEditingDepartment(null)
    setIsModalVisible(true)
    form.resetFields()
    
    // 自动生成部门编码
    const nextCode = generateNextDepartmentCode()
    if (nextCode) {
      form.setFieldsValue({ departmentCode: nextCode })
    }
  }

  const handleEdit = (department: Department) => {
    setEditingDepartment(department)
    setIsModalVisible(true)
    form.setFieldsValue(department)
  }

  const handleDelete = (id: string) => {
    // 检查是否有子部门
    const hasChildren = departments.some(dept => dept.parentDepartmentId === id)
    if (hasChildren) {
      message.error('该部门下还有子部门，无法删除')
      return
    }

    // 检查是否有员工
    const hasEmployees = employees.some(emp => {
      const dept = departments.find(d => d.id === id)
      return dept && emp.department === dept.departmentName
    })
    
    if (hasEmployees) {
      message.error('该部门下还有员工，无法删除')
      return
    }

    const updatedDepartments = departments.filter(d => d.id !== id)
    onDepartmentUpdate(updatedDepartments)
    message.success('部门删除成功')
  }

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields()
      const now = new Date().toISOString().split('T')[0]

      if (editingDepartment) {
        const updatedDepartments = departments.map(d =>
          d.id === editingDepartment.id
            ? { 
                ...d, 
                ...values, 
                updatedAt: now,
                employeeCount: employees.filter(emp => emp.department === values.departmentName).length
              }
            : d
        )
        
        // 如果部门名称发生变化，需要更新相关员工的部门信息
        if (editingDepartment.departmentName !== values.departmentName) {
          const updatedEmployees = employees.map(emp =>
            emp.department === editingDepartment.departmentName
              ? { ...emp, department: values.departmentName, updatedAt: now }
              : emp
          )
          onEmployeeUpdate(updatedEmployees)
        }
        
        onDepartmentUpdate(updatedDepartments)
        message.success('部门信息更新成功')
      } else {
        const newDepartment: Department = {
          id: Date.now().toString(),
          ...values,
          level: values.parentDepartmentId ? 
            (departments.find(d => d.id === values.parentDepartmentId)?.level || 0) + 1 : 1,
          employeeCount: 0,
          status: 'active' as const,
          createdAt: now,
          updatedAt: now
        }
        onDepartmentUpdate([...departments, newDepartment])
        message.success('部门创建成功')
      }
      setIsModalVisible(false)
      form.resetFields()
    } catch (error) {
    }
  }

  const handleModalCancel = () => {
    setIsModalVisible(false)
    form.resetFields()
  }

  const treeData = convertToTreeData(departments)

  return (
    <Modal
      title="部门管理"
      open={visible}
      onCancel={onClose}
      footer={[
        <Button key="close" onClick={onClose}>
          关闭
        </Button>
      ]}
      width={1200}
    >
      <div style={{ display: 'flex', flexDirection: 'column', gap: styleHelpers.spacing.md }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h3 style={{ fontSize: '18px', fontWeight: 500 }}>组织架构</h3>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
            新建部门
          </Button>
        </div>

        <Row gutter={16}>
          <Col span={12}>
            <Card title="部门树形结构" size="small">
              {treeData.length > 0 ? (
                <Tree
                  treeData={treeData}
                  defaultExpandAll
                  showLine
                  showIcon
                />
              ) : (
                <div style={{ textAlign: 'center', color: '#9ca3af', padding: '32px 0' }}>
                  暂无部门数据
                </div>
              )}
            </Card>
          </Col>
          <Col span={12}>
            <Card title="部门统计" size="small">
              <div style={{ display: 'flex', flexDirection: 'column', gap: styleHelpers.spacing.md }}>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <span>部门总数：</span>
                  <span style={{ fontWeight: 500 }}>{departments.length}个</span>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <span>员工总数：</span>
                  <span style={{ fontWeight: 500 }}>{employees.length}人</span>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <span>平均部门规模：</span>
                  <span style={{ fontWeight: 500 }}>
                    {departments.length > 0 ? Math.round(employees.length / departments.length) : 0}人
                  </span>
                </div>
              </div>
            </Card>
          </Col>
        </Row>
      </div>

      {/* 新建/编辑部门模态框 */}
      <Modal
        title={editingDepartment ? '编辑部门' : '新建部门'}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        okText="确认"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            status: 'active'
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="departmentCode"
                label="部门编码"
                rules={[
                  { required: true, message: '请输入部门编码' },
                  { pattern: /^D\d{3,4}$/, message: '格式：DXXX或DXXXX（如：D001）' },
                  {
                    validator: (_, value) => {
                      if (!value) return Promise.resolve()
                      if (!checkDepartmentCodeUniqueness(value, editingDepartment?.id)) {
                        return Promise.reject(new Error('部门编码已存在，请使用其他编码'))
                      }
                      return Promise.resolve()
                    }
                  }
                ]}
              >
                <Input placeholder="如：D001（自动生成）" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="departmentName"
                label="部门名称"
                rules={[{ required: true, message: '请输入部门名称' }]}
              >
                <Input placeholder="请输入部门名称" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="parentDepartmentId"
            label="上级部门"
          >
            <Select placeholder="请选择上级部门（可选）" allowClear>
              {departments
                .filter(dept => dept.id !== editingDepartment?.id)
                .map(dept => (
                  <Option key={dept.id} value={dept.id}>
                    {dept.departmentName}
                  </Option>
                ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="departmentHead"
            label="部门负责人"
          >
            <Select placeholder="请选择部门负责人（可选）" allowClear>
              {employees.map(emp => (
                <Option key={emp.id} value={emp.id}>
                  {emp.name} ({emp.employeeCode})
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="description"
            label="部门描述"
          >
            <TextArea rows={3} placeholder="请输入部门描述" />
          </Form.Item>
        </Form>
      </Modal>
    </Modal>
  )
}

export default DepartmentManagement
