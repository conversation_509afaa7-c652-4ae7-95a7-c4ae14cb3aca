/**
 * 认证Hook
 * 
 * 提供用户认证状态管理和相关操作
 * 遵循PRD文档中的认证上下文设计
 */

'use client'

import { useState, useEffect, useCallback } from 'react'
import { message } from 'antd'
import { 
  User, 
  LoginRequest, 
  LoginResult, 
  TokenResult, 
  AuthContext 
} from '@/types/auth'

/**
 * 认证状态管理Hook
 */
export const useAuth = (): AuthContext => {
  const [user, setUser] = useState<User | null>(null)
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  /**
   * 检查用户权限
   */
  const checkPermission = useCallback((permission: string): boolean => {
    if (!user || !isAuthenticated) {
      return false
    }

    // 系统管理员拥有所有权限
    if (user.roles.some(role => role.code === 'admin')) {
      return true
    }

    // 检查用户是否拥有指定权限
    const userPermissions = user.roles.flatMap(role => 
      role.permissions.map(perm => perm.code)
    )

    return userPermissions.includes(permission)
  }, [user, isAuthenticated])

  /**
   * 检查用户角色
   */
  const checkRole = useCallback((role: string): boolean => {
    if (!user || !isAuthenticated) {
      return false
    }

    return user.roles.some(userRole => userRole.code === role)
  }, [user, isAuthenticated])

  /**
   * 用户登录
   */
  const login = useCallback(async (credentials: LoginRequest): Promise<LoginResult> => {
    try {
      setIsLoading(true)

      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      })

      const result = await response.json()

      if (result.status === 'success') {
        setUser(result.data.user)
        setIsAuthenticated(true)
        message.success('登录成功')
        return result.data
      } else {
        throw new Error(result.message || '登录失败')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '登录失败'
      message.error(errorMessage)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [])

  /**
   * 用户登出
   */
  const logout = useCallback(async (): Promise<void> => {
    try {
      setIsLoading(true)

      await fetch('/api/auth/logout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      // 清除本地状态
      setUser(null)
      setIsAuthenticated(false)
      
      message.success('登出成功')
      
      // 跳转到登录页
      window.location.href = '/login'
    } catch (error) {
      console.error('登出失败:', error)
      message.error('登出失败')
      
      // 即使登出失败也清除本地状态
      setUser(null)
      setIsAuthenticated(false)
      window.location.href = '/login'
    } finally {
      setIsLoading(false)
    }
  }, [])

  /**
   * 刷新Token
   */
  const refreshToken = useCallback(async (): Promise<TokenResult> => {
    try {
      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const result = await response.json()

      if (result.status === 'success') {
        return result.data
      } else {
        throw new Error(result.message || 'Token刷新失败')
      }
    } catch (error) {
      console.error('Token刷新失败:', error)
      
      // Token刷新失败，清除认证状态并跳转到登录页
      setUser(null)
      setIsAuthenticated(false)
      window.location.href = '/login?expired=true'
      
      throw error
    }
  }, [])

  /**
   * 获取用户信息
   */
  const fetchUserProfile = useCallback(async (): Promise<void> => {
    try {
      const response = await fetch('/api/auth/profile', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const result = await response.json()

      if (result.status === 'success') {
        setUser(result.data)
        setIsAuthenticated(true)
      } else {
        // 获取用户信息失败，可能是Token无效
        setUser(null)
        setIsAuthenticated(false)
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      setUser(null)
      setIsAuthenticated(false)
    }
  }, [])

  /**
   * 自动刷新Token
   */
  const autoRefreshToken = useCallback(async (): Promise<void> => {
    try {
      await refreshToken()
      console.log('Token自动刷新成功')
    } catch (error) {
      console.error('Token自动刷新失败:', error)
    }
  }, [refreshToken])

  /**
   * 初始化认证状态
   */
  useEffect(() => {
    const initAuth = async () => {
      try {
        setIsLoading(true)
        await fetchUserProfile()
      } catch (error) {
        console.error('初始化认证状态失败:', error)
      } finally {
        setIsLoading(false)
      }
    }

    initAuth()
  }, [fetchUserProfile])

  /**
   * 监听Token刷新提示
   */
  useEffect(() => {
    const handleTokenRefresh = (event: Event) => {
      const customEvent = event as CustomEvent
      if (customEvent.detail?.refreshNeeded) {
        autoRefreshToken()
      }
    }

    window.addEventListener('token-refresh-needed', handleTokenRefresh)
    
    return () => {
      window.removeEventListener('token-refresh-needed', handleTokenRefresh)
    }
  }, [autoRefreshToken])

  /**
   * 设置定时刷新Token
   */
  useEffect(() => {
    if (!isAuthenticated) {
      return
    }

    // 每50分钟检查一次是否需要刷新Token
    const interval = setInterval(() => {
      if (isAuthenticated) {
        autoRefreshToken()
      }
    }, 50 * 60 * 1000) // 50分钟

    return () => clearInterval(interval)
  }, [isAuthenticated, autoRefreshToken])

  return {
    user,
    isAuthenticated,
    isLoading,
    login,
    logout,
    refreshToken,
    checkPermission,
    checkRole
  }
}

/**
 * 权限检查Hook
 */
export const usePermissions = () => {
  const { user, isAuthenticated, checkPermission, checkRole } = useAuth()

  return {
    user,
    isAuthenticated,
    checkPermission,
    checkRole,
    hasPermission: checkPermission,
    hasRole: checkRole,
    permissions: user?.roles.flatMap(role => role.permissions.map(perm => perm.code)) || [],
    roles: user?.roles.map(role => role.code) || []
  }
}
