'use client'

import React, { useState, useEffect } from 'react'
import {
  Card,
  Button,
  Space,
  Statistic,
  Row,
  Col,
  Alert,
  Upload,
  message,
  Descriptions,
  Divider,
  App
} from 'antd'
import {
  DatabaseOutlined,
  DownloadOutlined,
  UploadOutlined,
  DeleteOutlined,
  InfoCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons'
import { masterDataUtils, useMasterDataStore } from '@/store/modules/masterDataStore'
import { styleHelpers } from '@/utils/styles/antdHelpers'

function DataManagement() {
  const { message: messageApi, modal } = App.useApp()
  const [storageInfo, setStorageInfo] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  
  const { materials, productModels, clearAll } = useMasterDataStore()

  // 获取存储信息
  const refreshStorageInfo = () => {
    const info = masterDataUtils.getStorageInfo()
    setStorageInfo(info)
  }

  useEffect(() => {
    refreshStorageInfo()
  }, [materials, productModels])

  // 导出数据
  const handleExport = () => {
    try {
      masterDataUtils.exportData()
      messageApi.success('数据导出成功')
    } catch (error) {
      messageApi.error('数据导出失败')
    }
  }

  // 导入数据
  const handleImport = (file: File) => {
    setLoading(true)
    masterDataUtils.importData(file)
      .then(() => {
        messageApi.success('数据导入成功')
        refreshStorageInfo()
      })
      .catch((error) => {
        messageApi.error(`数据导入失败: ${error.message}`)
      })
      .finally(() => {
        setLoading(false)
      })
    
    return false // 阻止自动上传
  }

  // 清除数据
  const handleClearData = () => {
    modal.confirm({
      title: '确认清除数据',
      icon: <ExclamationCircleOutlined />,
      content: '此操作将清除所有本地存储的主数据，包括物料和产品信息。此操作不可恢复，确定要继续吗？',
      okText: '确认清除',
      okType: 'danger',
      cancelText: '取消',
      onOk() {
        try {
          masterDataUtils.clearLocalStorage()
          clearAll()
          messageApi.success('数据清除成功')
          refreshStorageInfo()
        } catch (error) {
          messageApi.error('数据清除失败')
        }
      }
    })
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: styleHelpers.spacing.lg }}>
      {/* 页面标题 */}
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <div>
          <h1 style={{
            fontSize: '24px',
            fontWeight: 'bold',
            color: styleHelpers.colors.gray[900],
            margin: 0
          }}>数据管理</h1>
          <p style={{
            color: styleHelpers.colors.gray[600],
            marginTop: styleHelpers.spacing.xs,
            margin: 0
          }}>管理ERP系统的本地数据存储</p>
        </div>
      </div>

      {/* 存储统计 */}
      <Card title={
        <span>
          <DatabaseOutlined style={{ marginRight: 8 }} />
          存储统计
        </span>
      }>
        <Row gutter={16}>
          <Col span={6}>
            <Statistic
              title="物料数量"
              value={storageInfo?.materialsCount || 0}
              suffix="个"
              valueStyle={{ color: '#1890ff' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="产品数量"
              value={storageInfo?.productModelsCount || 0}
              suffix="个"
              valueStyle={{ color: '#52c41a' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="存储大小"
              value={(storageInfo?.storageSize || 0) / 1024}
              precision={2}
              suffix="KB"
              valueStyle={{ color: '#722ed1' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="数据状态"
              value={storageInfo?.materialsCount > 0 || storageInfo?.productModelsCount > 0 ? '有数据' : '无数据'}
              valueStyle={{ 
                color: storageInfo?.materialsCount > 0 || storageInfo?.productModelsCount > 0 ? '#52c41a' : '#ff4d4f' 
              }}
            />
          </Col>
        </Row>
      </Card>

      {/* 数据操作 */}
      <Card title="数据操作">
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          {/* 导出数据 */}
          <div>
            <h3 style={{
              fontSize: '18px',
              fontWeight: 500,
              marginBottom: styleHelpers.spacing.xs,
              margin: 0
            }}>导出数据</h3>
            <p style={{
              color: styleHelpers.colors.gray[600],
              marginBottom: styleHelpers.spacing.sm,
              margin: 0
            }}>将当前的物料和产品数据导出为JSON文件，可用于备份或迁移。</p>
            <Button 
              type="primary" 
              icon={<DownloadOutlined />}
              onClick={handleExport}
              disabled={!storageInfo?.materialsCount && !storageInfo?.productModelsCount}
            >
              导出数据
            </Button>
          </div>

          <Divider />

          {/* 导入数据 */}
          <div>
            <h3 style={{
              fontSize: '18px',
              fontWeight: 500,
              marginBottom: styleHelpers.spacing.xs,
              margin: 0
            }}>导入数据</h3>
            <p style={{
              color: styleHelpers.colors.gray[600],
              marginBottom: styleHelpers.spacing.sm,
              margin: 0
            }}>从JSON文件导入物料和产品数据。导入的数据将与现有数据合并。</p>
            <Upload
              accept=".json"
              beforeUpload={handleImport}
              showUploadList={false}
            >
              <Button icon={<UploadOutlined />} loading={loading}>
                选择文件导入
              </Button>
            </Upload>
          </div>

          <Divider />

          {/* 清除数据 */}
          <div>
            <h3 style={{
              fontSize: '18px',
              fontWeight: 500,
              marginBottom: styleHelpers.spacing.xs,
              margin: 0
            }}>清除数据</h3>
            <p style={{
              color: styleHelpers.colors.gray[600],
              marginBottom: styleHelpers.spacing.sm,
              margin: 0
            }}>清除所有本地存储的数据。此操作不可恢复，请谨慎使用。</p>
            <Button 
              danger 
              icon={<DeleteOutlined />}
              onClick={handleClearData}
              disabled={!storageInfo?.materialsCount && !storageInfo?.productModelsCount}
            >
              清除所有数据
            </Button>
          </div>
        </Space>
      </Card>

      {/* 技术说明 */}
      <Card title={
        <span>
          <InfoCircleOutlined style={{ marginRight: 8 }} />
          技术说明
        </span>
      }>
        <Alert
          message="数据持久化机制"
          description={
            <div style={{ display: 'flex', flexDirection: 'column', gap: styleHelpers.spacing.xs }}>
              <p style={{ margin: 0 }}>• <strong>存储方式</strong>: 使用浏览器的localStorage进行本地存储</p>
              <p style={{ margin: 0 }}>• <strong>数据范围</strong>: 包括物料主数据和产品型号库数据</p>
              <p style={{ margin: 0 }}>• <strong>持久化</strong>: 页面刷新后数据不会丢失</p>
              <p style={{ margin: 0 }}>• <strong>容量限制</strong>: localStorage通常支持5-10MB的数据存储</p>
              <p style={{ margin: 0 }}>• <strong>数据安全</strong>: 数据仅存储在本地浏览器中，不会上传到服务器</p>
            </div>
          }
          type="info"
          showIcon
        />
        
        <Divider />
        
        <Descriptions title="存储详情" bordered size="small">
          <Descriptions.Item label="存储键名">master-data-storage</Descriptions.Item>
          <Descriptions.Item label="存储位置">浏览器localStorage</Descriptions.Item>
          <Descriptions.Item label="数据格式">JSON</Descriptions.Item>
          <Descriptions.Item label="版本控制">支持</Descriptions.Item>
          <Descriptions.Item label="自动备份">否</Descriptions.Item>
          <Descriptions.Item label="跨设备同步">否</Descriptions.Item>
        </Descriptions>
      </Card>

      {/* 使用建议 */}
      <Card title="使用建议">
        <Alert
          message="数据管理最佳实践"
          description={
            <div style={{ display: 'flex', flexDirection: 'column', gap: styleHelpers.spacing.xs }}>
              <p style={{ margin: 0 }}>• <strong>定期备份</strong>: 建议定期导出数据进行备份</p>
              <p style={{ margin: 0 }}>• <strong>版本管理</strong>: 重要变更前先导出当前数据</p>
              <p style={{ margin: 0 }}>• <strong>数据验证</strong>: 导入数据前请确认文件格式正确</p>
              <p style={{ margin: 0 }}>• <strong>清理策略</strong>: 定期清理无用数据以节省存储空间</p>
              <p style={{ margin: 0 }}>• <strong>迁移准备</strong>: 如需更换设备，请先导出数据</p>
            </div>
          }
          type="warning"
          showIcon
        />
      </Card>
    </div>
  )
}

// 用App组件包裹以提供message等上下文
export default function DataManagementPage() {
  return (
    <App>
      <DataManagement />
    </App>
  )
}
