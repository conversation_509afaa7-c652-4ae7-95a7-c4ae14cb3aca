/**
 * 产品选择Modal组件
 * 显示产品数据模块中的所有可用产品，支持搜索和筛选功能
 */

import React, { useState, useEffect } from 'react'
import { Modal, Table, Input, Select, Button, Tag, App, Row, Col, Card, Statistic } from 'antd'
import { SearchOutlined, ShoppingCartOutlined } from '@ant-design/icons'
import { ProductModel } from '@/types'
import { dataAccessManager } from '@/services/dataAccess/DataAccessManager'

const { Option } = Select

interface ProductSelectModalProps {
  open: boolean
  onCancel: () => void
  onSelect: (products: ProductModel[]) => void
}

const ProductSelectModal: React.FC<ProductSelectModalProps> = ({
  open,
  onCancel,
  onSelect
}) => {
  const { message } = App.useApp()
  const [loading, setLoading] = useState(false)
  const [products, setProducts] = useState<ProductModel[]>([])
  const [filteredProducts, setFilteredProducts] = useState<ProductModel[]>([])
  const [searchText, setSearchText] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])
  const [selectedProducts, setSelectedProducts] = useState<ProductModel[]>([])

  /**
   * 加载产品数据
   */
  const loadProducts = async () => {
    setLoading(true)
    try {
      const response = await dataAccessManager.products.getActive()
      if (response.status === 'success' && response.data) {
        let products: ProductModel[] = []

        // 处理不同的数据结构
        if (Array.isArray(response.data)) {
          products = response.data
        } else if (response.data && typeof response.data === 'object' && 'items' in response.data) {
          const paginatedData = response.data as { items: ProductModel[] }
          products = paginatedData.items
        } else {
          products = []
        }

        setProducts(products)
        setFilteredProducts(products)
      } else {
        message.error('获取产品数据失败')
      }
    } catch (error) {
      message.error('加载产品数据失败')
    } finally {
      setLoading(false)
    }
  }

  /**
   * 筛选产品数据
   */
  const filterProducts = () => {
    let filtered = [...products]

    // 搜索筛选
    if (searchText) {
      const searchLower = searchText.toLowerCase()
      filtered = filtered.filter(product =>
        product.modelCode.toLowerCase().includes(searchLower) ||
        product.modelName.toLowerCase().includes(searchLower)
      )
    }

    // 状态筛选
    if (statusFilter) {
      filtered = filtered.filter(product => product.status === statusFilter)
    }

    setFilteredProducts(filtered)
  }

  /**
   * 处理产品选择
   */
  const handleProductSelect = () => {
    if (selectedProducts.length > 0) {
      onSelect(selectedProducts)
      handleCancel()
    } else {
      message.warning('请至少选择一个产品')
    }
  }

  /**
   * 清空选择
   */
  const handleClearSelection = () => {
    setSelectedRowKeys([])
    setSelectedProducts([])
  }

  /**
   * 处理取消
   */
  const handleCancel = () => {
    setSearchText('')
    setStatusFilter('')
    setSelectedRowKeys([])
    setSelectedProducts([])
    onCancel()
  }

  /**
   * 表格列定义
   */
  const columns = [
    {
      title: '产品编码',
      dataIndex: 'modelCode',
      key: 'modelCode',
      width: 120,
      fixed: 'left' as const,
    },
    {
      title: '产品名称',
      dataIndex: 'modelName',
      key: 'modelName',
      width: 200,
      ellipsis: true,
    },
    {
      title: '产品价格',
      dataIndex: 'productPrice',
      key: 'productPrice',
      width: 120,
      render: (price: number) => price ? `¥${price.toFixed(3)}` : '¥0.000',
      sorter: (a: ProductModel, b: ProductModel) => (a.productPrice || 0) - (b.productPrice || 0),
    },
    {
      title: '产品重量',
      dataIndex: 'productWeight',
      key: 'productWeight',
      width: 120,
      render: (weight: number) => weight ? `${weight.toFixed(2)}g` : '0.00g',
      sorter: (a: ProductModel, b: ProductModel) => (a.productWeight || 0) - (b.productWeight || 0),
    },
    {
      title: '成型模具',
      dataIndex: 'formingMold',
      key: 'formingMold',
      width: 120,
      ellipsis: true,
    },
    {
      title: '单模数量',
      dataIndex: 'formingMoldQuantity',
      key: 'formingMoldQuantity',
      width: 100,
      render: (quantity: number) => `${quantity || 0}个`,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {status === 'active' ? '启用' : '停用'}
        </Tag>
      ),
    },
  ]

  /**
   * 表格行选择配置
   */
  const rowSelection = {
    type: 'checkbox' as const,
    selectedRowKeys,
    onChange: (selectedKeys: React.Key[], selectedRows: ProductModel[]) => {
      setSelectedRowKeys(selectedKeys)
      setSelectedProducts(selectedRows)
    },
    onSelectAll: (selected: boolean, selectedRows: ProductModel[], changeRows: ProductModel[]) => {
      if (selected) {
        // 全选：添加当前页面的所有产品
        const newSelectedProducts = [...selectedProducts]
        changeRows.forEach(product => {
          if (!newSelectedProducts.find(p => p.id === product.id)) {
            newSelectedProducts.push(product)
          }
        })
        setSelectedProducts(newSelectedProducts)
      } else {
        // 取消全选：移除当前页面的所有产品
        const currentPageIds = changeRows.map(p => p.id)
        const newSelectedProducts = selectedProducts.filter(p => !currentPageIds.includes(p.id))
        setSelectedProducts(newSelectedProducts)
      }
    },
    onSelect: (record: ProductModel, selected: boolean) => {
      if (selected) {
        // 添加产品
        if (!selectedProducts.find(p => p.id === record.id)) {
          setSelectedProducts([...selectedProducts, record])
        }
      } else {
        // 移除产品
        setSelectedProducts(selectedProducts.filter(p => p.id !== record.id))
      }
    },
  }

  /**
   * Modal打开时加载数据
   */
  useEffect(() => {
    if (open) {
      loadProducts()
    }
  }, [open])

  /**
   * 搜索和筛选变化时重新筛选
   */
  useEffect(() => {
    filterProducts()
  }, [searchText, statusFilter, products])

  return (
    <Modal
      title="选择产品"
      open={open}
      onCancel={handleCancel}
      width={1000}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button key="clear" onClick={handleClearSelection} disabled={selectedProducts.length === 0}>
          清空选择
        </Button>,
        <Button
          key="select"
          type="primary"
          icon={<ShoppingCartOutlined />}
          onClick={handleProductSelect}
          disabled={selectedProducts.length === 0}
        >
          确认添加 ({selectedProducts.length}个产品)
        </Button>
      ]}
      destroyOnHidden
    >
      {/* 统计信息 */}
      <Row gutter={16} style={{ marginBottom: '16px' }}>
        <Col span={8}>
          <Card size="small">
            <Statistic
              title="可用产品"
              value={products.filter(p => p.status === 'active').length}
              suffix="个"
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card size="small">
            <Statistic
              title="筛选结果"
              value={filteredProducts.length}
              suffix="个"
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card size="small">
            <Statistic
              title="平均价格"
              value={filteredProducts.length > 0 
                ? (filteredProducts.reduce((sum, p) => sum + p.productPrice, 0) / filteredProducts.length)
                : 0
              }
              precision={3}
              prefix="¥"
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索和筛选区域 */}
      <Row gutter={16} style={{ marginBottom: '16px' }}>
        <Col span={12}>
          <Input
            placeholder="搜索产品编码或名称"
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            allowClear
          />
        </Col>
        <Col span={6}>
          <Select
            placeholder="状态筛选"
            value={statusFilter}
            onChange={setStatusFilter}
            style={{ width: '100%' }}
            allowClear
          >
            <Option value="">全部状态</Option>
            <Option value="active">启用</Option>
            <Option value="inactive">停用</Option>
          </Select>
        </Col>
        <Col span={6}>
          <Button onClick={loadProducts} loading={loading}>
            刷新数据
          </Button>
        </Col>
      </Row>

      {/* 产品列表表格 */}
      <Table
        columns={columns}
        dataSource={filteredProducts}
        rowKey="id"
        rowSelection={rowSelection}
        loading={loading}
        pagination={{
          total: filteredProducts.length,
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
        }}
        scroll={{ x: 800, y: 400 }}
        size="small"
      />

      {/* 选中产品信息 */}
      {selectedProducts.length > 0 && (
        <Card title={`已选择产品 (${selectedProducts.length}个)`} size="small" style={{ marginTop: '16px' }}>
          <div style={{ maxHeight: '200px', overflowY: 'auto' }}>
            {selectedProducts.map((product, index) => (
              <Row key={product.id} gutter={16} style={index > 0 ? { marginTop: '8px', paddingTop: '8px', borderTop: '1px solid #f0f0f0' } : {}}>
                <Col span={6}>
                  <div><strong>编码:</strong> {product.modelCode}</div>
                  <div><strong>名称:</strong> {product.modelName}</div>
                </Col>
                <Col span={6}>
                  <div><strong>价格:</strong> ¥{product.productPrice ? product.productPrice.toFixed(3) : '0.000'}</div>
                  <div><strong>重量:</strong> {product.productWeight ? product.productWeight.toFixed(2) : '0.00'}g</div>
                </Col>
                <Col span={6}>
                  <div><strong>成型模具:</strong> {product.formingMold || '-'}</div>
                  <div><strong>单模数量:</strong> {product.formingMoldQuantity || 0}个</div>
                </Col>
                <Col span={6}>
                  <Button
                    type="link"
                    size="small"
                    danger
                    onClick={() => {
                      const newSelectedProducts = selectedProducts.filter(p => p.id !== product.id)
                      const newSelectedRowKeys = selectedRowKeys.filter(key => key !== product.id)
                      setSelectedProducts(newSelectedProducts)
                      setSelectedRowKeys(newSelectedRowKeys)
                    }}
                  >
                    移除
                  </Button>
                </Col>
              </Row>
            ))}
          </div>
        </Card>
      )}
    </Modal>
  )
}

export default ProductSelectModal
