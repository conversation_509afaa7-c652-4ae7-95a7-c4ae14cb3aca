import React, { useState, useEffect } from 'react'
import {
  Modal,
  Form,
  Input,
  InputNumber,
  Select,
  Tabs,
  Row,
  Col,
  App
} from 'antd'
import { ToolOutlined, DollarOutlined } from '@ant-design/icons'
import { ProductModel } from '@/types'
import { dataAccessManager } from '@/services/dataAccess/DataAccessManager'

const { Option } = Select

interface ProductCreateModalProps {
  open: boolean
  onCancel: () => void
  onSuccess: (product: ProductModel) => void
}

const ProductCreateModal: React.FC<ProductCreateModalProps> = ({
  open,
  onCancel,
  onSuccess
}) => {
  const { message } = App.useApp()
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [productModelsData, setProductModelsData] = useState<ProductModel[]>([])
  const [currentFormingMold, setCurrentFormingMold] = useState('')

  // 加载现有产品数据
  const loadProductsData = async () => {
    try {
      const response = await dataAccessManager.products.getAll()
      if (response.status === 'success' && response.data) {
        let products: ProductModel[] = []

        if (Array.isArray(response.data)) {
          products = response.data
        } else if ('items' in response.data && Array.isArray(response.data.items)) {
          products = response.data.items
        }

        setProductModelsData(products)
      }
    } catch (error) {
    }
  }

  // 生成下一个产品编码
  const generateNextProductCode = async (): Promise<string> => {
    try {
      const response = await dataAccessManager.products.getAll()
      if (response.status === 'success' && response.data) {
        let products: ProductModel[] = []
        
        if (Array.isArray(response.data)) {
          products = response.data
        } else if ('items' in response.data && Array.isArray(response.data.items)) {
          products = response.data.items
        } else {
          return 'P00001'
        }
        
        const codes = products
          .map((p: ProductModel) => p.modelCode)
          .filter((code: string) => /^P\d{5}$/.test(code))
          .map((code: string) => parseInt(code.substring(1), 10))
          .sort((a: number, b: number) => b - a)
        
        const nextNumber = codes.length > 0 ? codes[0] + 1 : 1
        return `P${nextNumber.toString().padStart(5, '0')}`
      }
    } catch (error) {
    }
    return 'P00001'
  }

  // 检查产品编码唯一性
  const checkCodeUniqueness = (code: string): boolean => {
    return !productModelsData.some(model => model.modelCode === code)
  }

  // 验证模具格式
  const validateMoldFormat = (value: string): boolean => {
    return /^M-[A-Z]{2}-\d{2}$/.test(value)
  }

  // 获取使用指定模具的产品列表
  const getProductsUsingMold = (moldCode: string, type: 'forming' | 'hotPress'): ProductModel[] => {
    if (type === 'forming') {
      return productModelsData.filter(model => model.formingMold === moldCode)
    } else {
      return productModelsData.filter(model => model.hotPressMold === moldCode)
    }
  }

  // 处理成型模具变化
  const handleFormingMoldChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setCurrentFormingMold(value)
  }

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields()
      setLoading(true)

      // 检查产品编码唯一性
      if (!checkCodeUniqueness(values.modelCode)) {
        message.error('产品编码已存在，请使用其他编码')
        setLoading(false)
        return
      }

      // 构建产品数据
      const productData: Omit<ProductModel, 'id' | 'createdAt' | 'updatedAt'> = {
        modelCode: values.modelCode,
        modelName: values.modelName,
        formingMold: values.formingMold || '',
        formingMoldQuantity: values.formingMoldQuantity || 1,
        hotPressMold: values.hotPressMold || '',
        hotPressMoldQuantity: values.hotPressMoldQuantity || 1,
        formingPiecePrice: values.formingPiecePrice || 0,
        hotPressPiecePrice: values.hotPressPiecePrice || 0,
        productPrice: values.productPrice,
        productWeight: values.productWeight,
        boxSpecification: values.boxSpecification || '',
        packingQuantity: values.packingQuantity || 100,
        piecesPerMold: values.piecesPerMold || 1,
        moldId: values.formingMold || '',
        status: values.status || 'active'
      }

      // 调用数据访问管理器创建产品
      const response = await dataAccessManager.products.create(productData)
      
      if (response.status === 'success' && response.data) {
        message.success('产品创建成功，已自动添加到订单中')
        onSuccess(response.data)
        handleCancel()
      } else {
        message.error(response.message || '产品创建失败')
      }
    } catch (error) {
      message.error('创建产品失败')
    } finally {
      setLoading(false)
    }
  }

  // 处理取消
  const handleCancel = () => {
    form.resetFields()
    setCurrentFormingMold('')
    onCancel()
  }

  // Modal打开时初始化
  const initializeForm = async () => {
    if (open) {
      await loadProductsData()
      const nextCode = await generateNextProductCode()
      form.setFieldsValue({
        modelCode: nextCode,
        status: 'active',
        formingMoldQuantity: 1,
        hotPressMoldQuantity: 1,
        packingQuantity: 100,
        piecesPerMold: 1,
        formingPiecePrice: 0,
        hotPressPiecePrice: 0
      })
    }
  }

  useEffect(() => {
    initializeForm()
  }, [open])

  return (
    <Modal
      title="新增产品"
      open={open}
      onOk={handleSubmit}
      onCancel={handleCancel}
      width={900}
      confirmLoading={loading}
      okText="确认创建"
      cancelText="取消"
      destroyOnHidden
      styles={{
        body: { maxHeight: '70vh', overflowY: 'auto' }
      }}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          status: 'active',
          formingMoldQuantity: 1,
          hotPressMoldQuantity: 1,
          packingQuantity: 100,
          piecesPerMold: 1,
          formingPiecePrice: 0,
          hotPressPiecePrice: 0
        }}
      >
        {/* 基本信息 */}
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="产品编码"
              name="modelCode"
              rules={[
                { required: true, message: '请输入产品编码' },
                { pattern: /^P\d{5}$/, message: '格式：P + 5位数字（如：P00001）' },
                {
                  validator: (_, value) => {
                    if (!value) return Promise.resolve()
                    if (!checkCodeUniqueness(value)) {
                      return Promise.reject(new Error('产品编码已存在，请使用其他编码'))
                    }
                    return Promise.resolve()
                  }
                }
              ]}
            >
              <Input placeholder="如：P00001（自动生成）" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="产品名称"
              name="modelName"
              rules={[{ required: true, message: '请输入产品名称' }]}
            >
              <Input placeholder="请输入产品名称" />
            </Form.Item>
          </Col>
        </Row>

        {/* Tabs分组 - 与产品数据模块完全一致 */}
        <Tabs
          defaultActiveKey="mold"
          items={[
            {
              key: 'mold',
              label: <span><ToolOutlined />模具信息</span>,
              children: (
                <>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        label="成型模具编号"
                        name="formingMold"
                        rules={[
                          { required: true, message: '请输入成型模具编号' },
                          {
                            validator: (_, value) => {
                              if (!value) return Promise.resolve()
                              if (validateMoldFormat(value)) {
                                return Promise.resolve()
                              }
                              return Promise.reject(new Error('模具编号格式错误，请使用格式：M-XX-XX（如：M-JX-05）'))
                            }
                          }
                        ]}
                        extra={
                          currentFormingMold &&
                          getProductsUsingMold(currentFormingMold, 'forming').length > 0 && (
                            <div style={{ marginTop: '4px' }}>
                              <span style={{ color: '#1890ff', fontSize: '12px' }}>
                                💡 此模具已被 {getProductsUsingMold(currentFormingMold, 'forming').length} 个产品使用：
                              </span>
                              <div style={{ fontSize: '11px', color: '#666', marginTop: '2px' }}>
                                {getProductsUsingMold(currentFormingMold, 'forming')
                                  .map(p => p.modelName)
                                  .join('、')}
                              </div>
                            </div>
                          )
                        }
                      >
                        <Input
                          placeholder="如：M-JX-05（支持多产品共享）"
                          onChange={handleFormingMoldChange}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        label="成型模具单模数量"
                        name="formingMoldQuantity"
                        rules={[{ required: true, message: '请输入单模数量' }]}
                      >
                        <InputNumber
                          style={{ width: '100%' }}
                          placeholder="请输入数量"
                          suffix="个/模"
                          min={1}
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        label="热压模具编号"
                        name="hotPressMold"
                        rules={[
                          { required: true, message: '请输入热压模具编号' },
                          {
                            validator: (_, value) => {
                              if (!value) return Promise.resolve()
                              if (!validateMoldFormat(value)) {
                                return Promise.reject(new Error('模具编号格式错误，请使用格式：M-XX-XX（如：M-RY-12）'))
                              }
                              // 热压模具保持唯一性约束
                              const isDuplicate = productModelsData.some(model => model.hotPressMold === value)
                              if (isDuplicate) {
                                return Promise.reject(new Error('热压模具编号已存在，请使用其他编号'))
                              }
                              return Promise.resolve()
                            }
                          }
                        ]}
                      >
                        <Input placeholder="如：M-RY-12" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        label="热压模具单模数量"
                        name="hotPressMoldQuantity"
                        rules={[{ required: true, message: '请输入单模数量' }]}
                      >
                        <InputNumber
                          style={{ width: '100%' }}
                          placeholder="请输入数量"
                          suffix="个/模"
                          min={1}
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Form.Item
                    label="单模出数"
                    name="piecesPerMold"
                    rules={[
                      { required: true, message: '请输入单模出数' },
                      { type: 'number', min: 1, message: '单模出数必须大于0' }
                    ]}
                  >
                    <InputNumber
                      style={{ width: '100%' }}
                      placeholder="用于智能排产模数计算"
                      suffix="个/模"
                      min={1}
                      precision={0}
                      step={1}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </>
              )
            },

            {
              key: 'price',
              label: <span><DollarOutlined />计件单价</span>,
              children: (
                <>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        label="成型计件单价"
                        name="formingPiecePrice"
                        rules={[{ required: true, message: '请输入成型计件单价' }]}
                      >
                        <InputNumber
                          style={{ width: '100%' }}
                          placeholder="请输入单价"
                          prefix="¥"
                          suffix="/模"
                          min={0}
                          precision={2}
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        label="热压计件单价"
                        name="hotPressPiecePrice"
                        rules={[{ required: true, message: '请输入热压计件单价' }]}
                      >
                        <InputNumber
                          style={{ width: '100%' }}
                          placeholder="请输入单价"
                          prefix="¥"
                          suffix="/模"
                          min={0}
                          precision={2}
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        label="产品价格"
                        name="productPrice"
                        rules={[
                          { required: true, message: '请输入产品价格' },
                          { type: 'number', min: 0.01, message: '产品价格必须大于0' }
                        ]}
                      >
                        <InputNumber
                          style={{ width: '100%' }}
                          placeholder="请输入产品价格"
                          prefix="¥"
                          min={0}
                          precision={3}
                          step={0.001}
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        label="产品重量"
                        name="productWeight"
                        rules={[
                          { required: true, message: '请输入产品重量' },
                          { type: 'number', min: 0.01, message: '产品重量必须大于0' }
                        ]}
                      >
                        <InputNumber
                          style={{ width: '100%' }}
                          placeholder="请输入产品重量"
                          suffix="克"
                          min={0}
                          precision={2}
                          step={0.01}
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        label="箱规"
                        name="boxSpecification"
                        rules={[{ required: true, message: '请输入箱规' }]}
                      >
                        <Input
                          placeholder="如：30×20×15 cm"
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        label="装箱数"
                        name="packingQuantity"
                        rules={[
                          { required: true, message: '请输入装箱数' },
                          { type: 'number', min: 1, message: '装箱数必须大于0' }
                        ]}
                      >
                        <InputNumber
                          style={{ width: '100%' }}
                          placeholder="请输入装箱数"
                          suffix="个/箱"
                          min={1}
                          precision={0}
                          step={1}
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Form.Item
                    label="状态"
                    name="status"
                    rules={[{ required: true, message: '请选择状态' }]}
                  >
                    <Select placeholder="请选择状态">
                      <Option value="active">启用</Option>
                      <Option value="inactive">停用</Option>
                    </Select>
                  </Form.Item>
                </>
              )
            }
          ]}
        />
      </Form>
    </Modal>
  )
}

export default ProductCreateModal
