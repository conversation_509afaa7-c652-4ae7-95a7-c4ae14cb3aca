/**
 * MainLayout组件测试
 * 验证Tailwind迁移后的功能完整性
 */

import React from 'react'
import { render, screen } from '@testing-library/react'
import { MainLayout } from '../MainLayout'

// Mock useAppStore
jest.mock('@/store/useAppStore', () => ({
  useAppStore: () => ({
    sidebarCollapsed: false,
    isMobile: false
  })
}))

// Mock子组件
jest.mock('../Sidebar', () => ({
  Sidebar: () => <div data-testid="sidebar">Sidebar</div>
}))

jest.mock('../Header', () => ({
  Header: () => <div data-testid="header">Header</div>
}))

describe('MainLayout', () => {
  const TestChildren = () => <div data-testid="test-children">Test Content</div>

  beforeEach(() => {
    // 重置所有mock
    jest.clearAllMocks()
  })

  it('应该正确渲染基本结构', () => {
    render(
      <MainLayout>
        <TestChildren />
      </MainLayout>
    )

    // 验证基本组件存在
    expect(screen.getByTestId('sidebar')).toBeInTheDocument()
    expect(screen.getByTestId('header')).toBeInTheDocument()
    expect(screen.getByTestId('test-children')).toBeInTheDocument()
  })

  it('应该应用正确的内联样式', () => {
    const { container } = render(
      <MainLayout>
        <TestChildren />
      </MainLayout>
    )

    // 验证主容器样式
    const mainLayout = container.querySelector('.ant-layout')
    expect(mainLayout).toHaveStyle({
      minHeight: '100vh'
    })
  })

  it('应该正确渲染内容布局', () => {
    const { container } = render(
      <MainLayout>
        <TestChildren />
      </MainLayout>
    )

    // 验证内容布局存在
    const contentLayout = container.querySelector('.ant-layout .ant-layout')
    expect(contentLayout).toBeInTheDocument()
  })

  it('应该为内容区域应用正确的样式', () => {
    const { container } = render(
      <MainLayout>
        <TestChildren />
      </MainLayout>
    )

    // 验证内容区域存在
    const content = container.querySelector('.ant-layout-content')
    expect(content).toBeInTheDocument()
  })

  it('应该正确渲染子组件内容', () => {
    const customContent = 'Custom Layout Content'

    render(
      <MainLayout>
        <div>{customContent}</div>
      </MainLayout>
    )

    expect(screen.getByText(customContent)).toBeInTheDocument()
  })
})
