{"name": "erp-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "jest --testPathPattern=e2e", "test:integration": "jest --testPathPattern=integration", "test:performance": "jest --testPathPattern=performance", "test:batch": "jest --testPathPattern=batch", "test:config": "jest --testPathPattern=configuration", "test:business": "node scripts/test-business-utils.js", "test:business:watch": "node scripts/test-business-utils.js --watch", "test:business:coverage": "jest --testPathPattern=src/utils/__tests__/business --coverage --coverageDirectory=coverage/business-utils", "detect:duplicates": "node scripts/detect-duplicate-functions.js", "monitor:deprecated": "node scripts/monitor-deprecated-usage.js", "monitor:performance": "node scripts/performance-monitor.js", "analyze:code": "npm run detect:duplicates"}, "dependencies": {"@ant-design/icons": "^5.2.6", "@ant-design/plots": "^2.5.0", "@types/react-window": "^1.8.8", "antd": "^5.12.8", "classnames": "^2.3.2", "dayjs": "^1.11.10", "glob": "^11.0.3", "next": "14.0.4", "react": "^18.2.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-window": "^1.8.11", "zustand": "^4.4.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.8", "@types/lodash": "^4.17.20", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dnd": "^3.0.2", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "eslint-config-next": "14.0.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "ts-jest": "^29.1.1", "typescript": "^5.3.3"}}