"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/sales/orders/page",{

/***/ "(app-pages-browser)/./src/app/sales/orders/page.tsx":
/*!***************************************!*\
  !*** ./src/app/sales/orders/page.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ OrderManagementPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/date-picker/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/steps/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/app/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tag/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tooltip/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/badge/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/popconfirm/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input-number/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/row/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/col/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/statistic/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/alert/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/table/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_43__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,App,Badge,Button,Card,Col,DatePicker,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Steps,Table,Tag,Tooltip!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_ExportOutlined_PlusOutlined_SearchOutlined_SwapOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,ExportOutlined,PlusOutlined,SearchOutlined,SwapOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/SwapOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_ExportOutlined_PlusOutlined_SearchOutlined_SwapOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,ExportOutlined,PlusOutlined,SearchOutlined,SwapOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_ExportOutlined_PlusOutlined_SearchOutlined_SwapOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,ExportOutlined,PlusOutlined,SearchOutlined,SwapOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/SearchOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_ExportOutlined_PlusOutlined_SearchOutlined_SwapOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,ExportOutlined,PlusOutlined,SearchOutlined,SwapOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ExportOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_ExportOutlined_PlusOutlined_SearchOutlined_SwapOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,ExportOutlined,PlusOutlined,SearchOutlined,SwapOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlusOutlined.js\");\n/* harmony import */ var _services_dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/dataAccess/DataAccessManager */ \"(app-pages-browser)/./src/services/dataAccess/DataAccessManager.ts\");\n/* harmony import */ var _utils_dataAccessErrorHandler__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/dataAccessErrorHandler */ \"(app-pages-browser)/./src/utils/dataAccessErrorHandler.ts\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _components_sales_AddOrderModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/sales/AddOrderModal */ \"(app-pages-browser)/./src/components/sales/AddOrderModal.tsx\");\n/* harmony import */ var _hooks_useProductionOrders__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./hooks/useProductionOrders */ \"(app-pages-browser)/./src/app/sales/orders/hooks/useProductionOrders.ts\");\n/* harmony import */ var _services_OrderCancellationService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/services/OrderCancellationService */ \"(app-pages-browser)/./src/services/OrderCancellationService.ts\");\n/* harmony import */ var _services_OrderQuantityChangeService__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/services/OrderQuantityChangeService */ \"(app-pages-browser)/./src/services/OrderQuantityChangeService.ts\");\n/* harmony import */ var _services_OrderDeliveryDateChangeService__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/services/OrderDeliveryDateChangeService */ \"(app-pages-browser)/./src/services/OrderDeliveryDateChangeService.ts\");\n/* harmony import */ var _hooks_useDebouncedCallback__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/hooks/useDebouncedCallback */ \"(app-pages-browser)/./src/hooks/useDebouncedCallback.ts\");\n/* harmony import */ var _hooks_useEventListener__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/hooks/useEventListener */ \"(app-pages-browser)/./src/hooks/useEventListener.ts\");\n/* harmony import */ var _hooks_useDataAccessMonitor__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/hooks/useDataAccessMonitor */ \"(app-pages-browser)/./src/hooks/useDataAccessMonitor.ts\");\n/* harmony import */ var _hooks_useOrdersData__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/hooks/useOrdersData */ \"(app-pages-browser)/./src/hooks/useOrdersData.ts\");\n/* harmony import */ var _utils_architectureCompliance__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/utils/architectureCompliance */ \"(app-pages-browser)/./src/utils/architectureCompliance.ts\");\n/* harmony import */ var _services_validation_OrderValidationService__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/services/validation/OrderValidationService */ \"(app-pages-browser)/./src/services/validation/OrderValidationService.ts\");\n/* harmony import */ var _components_common_OrderDetailModal__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/common/OrderDetailModal */ \"(app-pages-browser)/./src/components/common/OrderDetailModal/index.ts\");\n/* harmony import */ var _components_common_OrderDetailModal_configs_salesOrderConfig__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/common/OrderDetailModal/configs/salesOrderConfig */ \"(app-pages-browser)/./src/components/common/OrderDetailModal/configs/salesOrderConfig.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// 移除useSalesStore依赖，使用dataAccessManager统一数据访问\n\n\n\n\n// 🔧 新增：导入生产订单相关组件和Hook\n\n\n\n\n// ✅ 架构合规：使用DataAccessManager统一监控和合规的Hooks\n\n\n\n\n// ✅ 架构合规性验证工具\n\n// 🔧 P4-3数据验证统一：导入统一验证服务\n\n// 🔧 P5-1订单详情组件重构：导入通用订单详情组件\n\n\nconst { Option } = _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"];\nconst { TextArea } = _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"];\nconst { RangePicker } = _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"];\nconst { Step } = _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"];\nconst OrderManagement = ()=>{\n    _s();\n    const { message, modal } = _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"].useApp();\n    // MRP相关状态保留用于UI显示\n    const [mrpExecuting, setMRPExecuting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDetailModalVisible, setIsDetailModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isChangeModalVisible, setIsChangeModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAddOrderModalVisible, setIsAddOrderModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedOrder, setSelectedOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedChangeType, setSelectedChangeType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Form实例 - 用于订单变更\n    const [changeForm] = _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"].useForm();\n    // ✅ 架构合规：使用useOrdersData Hook替代手动数据加载\n    const { orders: ordersFromHook, loading: ordersLoading, error: ordersError, refreshOrders, loadOrders, hasOrders, isEmpty } = (0,_hooks_useOrdersData__WEBPACK_IMPORTED_MODULE_14__.useOrdersData)({\n        autoLoad: true,\n        enableCache: true\n    });\n    // 保持原有的orders状态以兼容现有代码\n    const [orders, setOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 同步Hook数据到本地状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setOrders(ordersFromHook);\n    }, [\n        ordersFromHook\n    ]);\n    // 显示错误信息\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (ordersError) {\n            message.error(ordersError);\n        }\n    }, [\n        ordersError,\n        message\n    ]);\n    // ✅ 架构合规性检查功能（仅开发环境）\n    const handleArchitectureComplianceCheck = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDD0D 开始执行架构合规性检查...\");\n            const result = await (0,_utils_architectureCompliance__WEBPACK_IMPORTED_MODULE_15__.performArchitectureComplianceCheck)();\n            (0,_utils_architectureCompliance__WEBPACK_IMPORTED_MODULE_15__.printComplianceReport)(result);\n            if (result.compliant) {\n                message.success(\"架构合规性检查通过！评分: \".concat(result.score, \"/100\"));\n            } else {\n                message.warning(\"架构合规性检查未通过，评分: \".concat(result.score, \"/100，请查看控制台详情\"));\n            }\n        } catch (error) {\n            console.error(\"架构合规性检查失败:\", error);\n            message.error(\"架构合规性检查失败\");\n        }\n    };\n    // 🔧 P4-2架构升级：使用数据变更监听器自动刷新数据\n    (0,_hooks_useEventListener__WEBPACK_IMPORTED_MODULE_12__.useDataChangeListener)(\"sales-orders-page\", {\n        onOrderCreated: ()=>{\n            console.log(\"\\uD83D\\uDCDD 检测到销售订单创建，自动刷新数据\");\n            refreshOrders();\n        },\n        onOrderUpdated: ()=>{\n            console.log(\"\\uD83D\\uDCDD 检测到销售订单更新，自动刷新数据\");\n            refreshOrders();\n        },\n        onOrderDeleted: ()=>{\n            console.log(\"\\uD83D\\uDCDD 检测到销售订单删除，自动刷新数据\");\n            refreshOrders();\n        }\n    });\n    // 🔧 P4-2架构升级：优化搜索性能，使用防抖搜索\n    const [searchText, setSearchText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchInput, setSearchInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\") // 输入框的即时值\n    ;\n    const [filterStatus, setFilterStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [filterProductionStatus, setFilterProductionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 🔧 P4-2架构升级：使用防抖搜索优化性能\n    const debouncedSearch = (0,_hooks_useDebouncedCallback__WEBPACK_IMPORTED_MODULE_11__.useDebouncedSearch)((query)=>{\n        setSearchText(query);\n    }, 300, []);\n    // ✅ 架构合规：使用DataAccessManager统一监控体系\n    const { metrics, cacheStats, isMonitoring, clearCache, getPerformanceAlerts, formatMemorySize, formatPercentage, isHealthy, needsOptimization } = (0,_hooks_useDataAccessMonitor__WEBPACK_IMPORTED_MODULE_13__.useDataAccessMonitor)({\n        interval: 60000,\n        enabled: true,\n        showDetails: \"development\" === \"development\"\n    });\n    // 批量操作相关状态\n    const [selectedRowKeys, setSelectedRowKeys] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedOrders, setSelectedOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [batchLoading, setBatchLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // MRP相关状态 - 仅保留UI显示需要的\n    const [mrpResult, setMrpResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showMRPResult, setShowMRPResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mrpExecutionStep, setMrpExecutionStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [mrpExecutionSteps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        \"启动MRP\",\n        \"MRP分析\",\n        \"生成生产订单\",\n        \"完成\"\n    ]);\n    const [productInventory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // 🔧 新增：获取选中订单的历史生产订单\n    const { productionOrders: historicalProductionOrders, loading: productionOrdersLoading } = (0,_hooks_useProductionOrders__WEBPACK_IMPORTED_MODULE_7__.useMRPProductionOrders)((selectedOrder === null || selectedOrder === void 0 ? void 0 : selectedOrder.orderNumber) || \"\");\n    // 获取状态标签\n    const getStatusTag = (status)=>{\n        const statusMap = {\n            \"pending\": {\n                color: \"red\",\n                text: \"未审核\"\n            },\n            \"confirmed\": {\n                color: \"green\",\n                text: \"已审核\"\n            },\n            \"completed\": {\n                color: \"gray\",\n                text: \"完成\"\n            },\n            \"cancelled\": {\n                color: \"orange\",\n                text: \"已取消\"\n            }\n        };\n        const config = statusMap[status] || {\n            color: \"default\",\n            text: \"未知\"\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n            color: config.color,\n            children: config.text\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n            lineNumber: 214,\n            columnNumber: 12\n        }, undefined);\n    };\n    // 获取生产状态标签（与订单状态联动）\n    const getProductionStatusTag = (orderStatus, productionStatus)=>{\n        // 如果订单未审核或已取消，不显示生产状态\n        if (orderStatus === \"pending\" || orderStatus === \"cancelled\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                style: {\n                    color: \"#999\"\n                },\n                children: \"-\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                lineNumber: 221,\n                columnNumber: 14\n            }, undefined);\n        }\n        // 如果订单已审核，显示生产状态（默认为未开始）\n        const actualStatus = orderStatus === \"confirmed\" && !productionStatus ? \"not_started\" : productionStatus;\n        const statusMap = {\n            \"not_started\": {\n                color: \"orange\",\n                text: \"未开始\"\n            },\n            \"pending\": {\n                color: \"blue\",\n                text: \"待生产\"\n            },\n            \"in_progress\": {\n                color: \"green\",\n                text: \"生产中\"\n            },\n            \"completed\": {\n                color: \"cyan\",\n                text: \"已完成\"\n            }\n        };\n        const config = statusMap[actualStatus] || {\n            color: \"orange\",\n            text: \"未开始\"\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n            color: config.color,\n            children: config.text\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n            lineNumber: 234,\n            columnNumber: 12\n        }, undefined);\n    };\n    // 获取MRP状态标签\n    const getMRPStatusTag = (mrpStatus)=>{\n        const statusMap = {\n            \"not_started\": {\n                color: \"default\",\n                text: \"未启动\"\n            },\n            \"in_progress\": {\n                color: \"processing\",\n                text: \"执行中\"\n            },\n            \"completed\": {\n                color: \"success\",\n                text: \"已完成\"\n            },\n            \"failed\": {\n                color: \"error\",\n                text: \"执行失败\"\n            }\n        };\n        const config = statusMap[mrpStatus] || {\n            color: \"default\",\n            text: \"未启动\"\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n            color: config.color,\n            children: config.text\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n            lineNumber: 246,\n            columnNumber: 12\n        }, undefined);\n    };\n    // 获取付款状态标签\n    const getPaymentStatusTag = (status)=>{\n        const statusMap = {\n            \"unpaid\": {\n                color: \"red\",\n                text: \"未付款\"\n            },\n            \"partial\": {\n                color: \"orange\",\n                text: \"部分付款\"\n            },\n            \"paid\": {\n                color: \"green\",\n                text: \"已付款\"\n            }\n        };\n        const config = statusMap[status] || {\n            color: \"default\",\n            text: \"未知\"\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n            color: config.color,\n            children: config.text\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n            lineNumber: 257,\n            columnNumber: 12\n        }, undefined);\n    };\n    // 计算智能交期\n    const calculateDeliveryDate = (productModelCode, quantity)=>{\n        const inventory = productInventory[productModelCode];\n        if (!inventory) return \"待确认\";\n        const { stock, dailyCapacity } = inventory;\n        const needProduction = Math.max(0, quantity - stock);\n        const productionDays = Math.ceil(needProduction / dailyCapacity);\n        const totalDays = productionDays + 3;\n        const deliveryDate = new Date();\n        deliveryDate.setDate(deliveryDate.getDate() + totalDays);\n        return deliveryDate.toISOString().split(\"T\")[0];\n    };\n    const convertUnit = (quantity, fromUnit, toUnit, productModelCode)=>{\n        const defaultWeightPerPiece = 12.0;\n        if (fromUnit === \"个\" && toUnit === \"吨\") {\n            return quantity * defaultWeightPerPiece / 1000000;\n        } else if (fromUnit === \"吨\" && toUnit === \"个\") {\n            return quantity * 1000000 / defaultWeightPerPiece;\n        } else if (fromUnit === \"个\" && toUnit === \"克\") {\n            return quantity * defaultWeightPerPiece;\n        } else if (fromUnit === \"克\" && toUnit === \"个\") {\n            return quantity / defaultWeightPerPiece;\n        }\n        return quantity;\n    };\n    // 表格列定义\n    const columns = [\n        {\n            title: \"销售订单号\",\n            dataIndex: \"orderNumber\",\n            key: \"orderNumber\",\n            width: 140,\n            fixed: \"left\",\n            render: (orderNumber, record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                    type: \"link\",\n                    onClick: ()=>handleViewDetail(record),\n                    style: {\n                        padding: 0,\n                        height: \"auto\",\n                        fontWeight: \"bold\"\n                    },\n                    children: orderNumber\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                    lineNumber: 300,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: \"订单状态\",\n            dataIndex: \"status\",\n            key: \"status\",\n            width: 100,\n            render: (status)=>getStatusTag(status)\n        },\n        {\n            title: \"创建时间\",\n            dataIndex: \"createdAt\",\n            key: \"createdAt\",\n            width: 160,\n            sorter: (a, b)=>new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),\n            defaultSortOrder: \"descend\",\n            render: (createdAt)=>{\n                if (!createdAt) return \"-\";\n                return dayjs__WEBPACK_IMPORTED_MODULE_5___default()(createdAt).format(\"YYYY-MM-DD HH:mm:ss\");\n            }\n        },\n        {\n            title: \"客户名称\",\n            dataIndex: \"customerName\",\n            key: \"customerName\",\n            width: 180,\n            ellipsis: true\n        },\n        {\n            title: \"订单日期\",\n            dataIndex: \"orderDate\",\n            key: \"orderDate\",\n            width: 120,\n            sorter: (a, b)=>new Date(a.orderDate).getTime() - new Date(b.orderDate).getTime()\n        },\n        {\n            title: \"订单金额\",\n            dataIndex: \"finalAmount\",\n            key: \"finalAmount\",\n            width: 150,\n            sorter: (a, b)=>a.finalAmount - b.finalAmount,\n            render: (finalAmount, record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontWeight: \"bold\",\n                                color: \"#1890ff\"\n                            },\n                            children: [\n                                \"\\xa5\",\n                                (finalAmount || 0).toLocaleString(\"zh-CN\", {\n                                    minimumFractionDigits: 2,\n                                    maximumFractionDigits: 2\n                                })\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 11\n                        }, undefined),\n                        (record.discountAmount || 0) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: \"12px\",\n                                color: \"#666\"\n                            },\n                            children: [\n                                \"折扣: \\xa5\",\n                                (record.discountAmount || 0).toLocaleString(\"zh-CN\", {\n                                    minimumFractionDigits: 2,\n                                    maximumFractionDigits: 2\n                                })\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 354,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                    lineNumber: 349,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: \"生产状态\",\n            dataIndex: \"productionStatus\",\n            key: \"productionStatus\",\n            width: 120,\n            render: (productionStatus, record)=>getProductionStatusTag(record.status, productionStatus)\n        },\n        {\n            title: \"付款状态\",\n            dataIndex: \"paymentStatus\",\n            key: \"paymentStatus\",\n            width: 100,\n            render: (status)=>getPaymentStatusTag(status)\n        },\n        {\n            title: \"变更次数\",\n            key: \"changeCount\",\n            width: 100,\n            render: (_, record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: record.changes.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                        title: \"点击查看变更历史\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                            count: record.changes.length,\n                            size: \"small\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                type: \"link\",\n                                size: \"small\",\n                                children: [\n                                    record.changes.length,\n                                    \"次\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 384,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                        lineNumber: 383,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: {\n                            color: \"#666\"\n                        },\n                        children: \"无\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                        lineNumber: 391,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                    lineNumber: 381,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: \"操作\",\n            key: \"action\",\n            width: 180,\n            fixed: \"right\",\n            render: (_, record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                    size: \"small\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                            type: \"link\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_ExportOutlined_PlusOutlined_SearchOutlined_SwapOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 19\n                            }, void 0),\n                            onClick: ()=>handleOrderChange(record),\n                            children: \"变更\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                            title: \"确定要删除这个订单吗？\",\n                            onConfirm: ()=>handleDelete(record.id),\n                            okText: \"确定\",\n                            cancelText: \"取消\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                type: \"link\",\n                                danger: true,\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_ExportOutlined_PlusOutlined_SearchOutlined_SwapOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                    lineNumber: 417,\n                                    columnNumber: 46\n                                }, void 0),\n                                children: \"删除\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 411,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                    lineNumber: 402,\n                    columnNumber: 9\n                }, undefined)\n        }\n    ];\n    // 过滤后的订单数据\n    const filteredOrders = orders.filter((order)=>{\n        const matchesSearch = !searchText || order.orderNumber.toLowerCase().includes(searchText.toLowerCase()) || order.customerName.toLowerCase().includes(searchText.toLowerCase()) || order.customerContact.toLowerCase().includes(searchText.toLowerCase());\n        const matchesStatus = !filterStatus || order.status === filterStatus;\n        const matchesProductionStatus = !filterProductionStatus || order.productionStatus === filterProductionStatus;\n        return matchesSearch && matchesStatus && matchesProductionStatus;\n    });\n    // 统计数据\n    const stats = {\n        total: orders.length,\n        pending: orders.filter((o)=>o.status === \"pending\").length,\n        confirmed: orders.filter((o)=>o.status === \"confirmed\").length,\n        completed: orders.filter((o)=>o.status === \"completed\").length,\n        cancelled: orders.filter((o)=>o.status === \"cancelled\").length,\n        totalAmount: orders.reduce((sum, o)=>sum + o.finalAmount, 0),\n        delayedOrders: orders.filter((o)=>new Date(o.deliveryDate) < new Date() && o.status !== \"completed\" && o.status !== \"cancelled\").length\n    };\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleViewDetail = (order)=>{\n        setSelectedOrder(order);\n        setIsDetailModalVisible(true);\n    };\n    const handleOrderChange = (order)=>{\n        setSelectedOrder(order);\n        setSelectedChangeType(\"\") // 重置变更类型\n        ;\n        setIsChangeModalVisible(true);\n        changeForm.resetFields();\n    };\n    // 🔧 新增：根据变更类型自动填充原始值\n    const handleChangeTypeSelect = (changeType)=>{\n        if (!selectedOrder) return;\n        setSelectedChangeType(changeType) // 更新选择的变更类型\n        ;\n        let originalValue = \"\";\n        switch(changeType){\n            case \"quantity\":\n                var _selectedOrder_items;\n                // 获取订单总数量\n                const totalQuantity = ((_selectedOrder_items = selectedOrder.items) === null || _selectedOrder_items === void 0 ? void 0 : _selectedOrder_items.reduce((sum, item)=>sum + item.quantity, 0)) || 0;\n                originalValue = totalQuantity.toString();\n                break;\n            case \"delivery_date\":\n                // 获取交期，格式化为YYYY-MM-DD\n                try {\n                    const date = new Date(selectedOrder.deliveryDate);\n                    originalValue = date.toISOString().split(\"T\")[0] // 格式化为YYYY-MM-DD\n                    ;\n                } catch (error) {\n                    originalValue = selectedOrder.deliveryDate.split(\"T\")[0] // 备用方案\n                    ;\n                }\n                break;\n            case \"cancel\":\n                // 订单取消时，原始值为当前状态\n                const statusMap = {\n                    \"pending\": \"未审核\",\n                    \"confirmed\": \"已审核\",\n                    \"completed\": \"完成\",\n                    \"cancelled\": \"已取消\"\n                };\n                originalValue = statusMap[selectedOrder.status] || selectedOrder.status;\n                break;\n            default:\n                originalValue = \"\";\n        }\n        // 自动填充原始值，并清空新值\n        changeForm.setFieldsValue({\n            originalValue: originalValue,\n            newValue: \"\" // 清空新值，让用户重新输入\n        });\n    };\n    // 🔧 新增：根据变更类型获取输入提示\n    const getPlaceholderByChangeType = (changeType, field)=>{\n        var _placeholders_changeType;\n        if (!changeType) return field === \"originalValue\" ? \"请输入原始值\" : \"请输入新值\";\n        const placeholders = {\n            quantity: {\n                originalValue: \"当前订单总数量\",\n                newValue: \"请输入新的数量\"\n            },\n            delivery_date: {\n                originalValue: \"当前交期日期\",\n                newValue: \"请选择新的交期日期\"\n            },\n            cancel: {\n                originalValue: \"当前订单状态\",\n                newValue: \"取消原因\"\n            }\n        };\n        return ((_placeholders_changeType = placeholders[changeType]) === null || _placeholders_changeType === void 0 ? void 0 : _placeholders_changeType[field]) || (field === \"originalValue\" ? \"请输入原始值\" : \"请输入新值\");\n    };\n    // 🔧 新增：根据变更类型渲染不同的输入组件\n    const renderInputByChangeType = (changeType, field)=>{\n        const placeholder = getPlaceholderByChangeType(changeType, field);\n        const isReadOnly = field === \"originalValue\" && !!selectedChangeType;\n        switch(changeType){\n            case \"quantity\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                    placeholder: placeholder,\n                    min: 0,\n                    style: {\n                        width: \"100%\"\n                    },\n                    readOnly: isReadOnly\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                    lineNumber: 543,\n                    columnNumber: 11\n                }, undefined);\n            case \"delivery_date\":\n                if (field === \"originalValue\") {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                        placeholder: placeholder,\n                        readOnly: true\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                        lineNumber: 552,\n                        columnNumber: 18\n                    }, undefined);\n                } else {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                        placeholder: placeholder,\n                        style: {\n                            width: \"100%\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                        lineNumber: 554,\n                        columnNumber: 18\n                    }, undefined);\n                }\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    placeholder: placeholder,\n                    readOnly: isReadOnly\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                    lineNumber: 559,\n                    columnNumber: 11\n                }, undefined);\n        }\n    };\n    const handleDelete = async (id)=>{\n        const result = await (0,_utils_dataAccessErrorHandler__WEBPACK_IMPORTED_MODULE_4__.handleApiResponse)(()=>_services_dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_3__.dataAccessManager.orders.delete(id), \"删除订单\");\n        if (result !== null) {\n            await refreshOrders();\n            message.success(\"订单删除成功\");\n        } else {\n            message.error(\"删除订单失败\");\n        }\n    };\n    // 新增订单处理函数\n    const handleAddOrder = ()=>{\n        setIsAddOrderModalVisible(true);\n    };\n    const handleAddOrderSuccess = async (newOrder)=>{\n        // 刷新订单列表以获取最新数据\n        await refreshOrders();\n        message.success(\"订单创建成功\");\n        setIsAddOrderModalVisible(false);\n    };\n    const handleAddOrderCancel = ()=>{\n        setIsAddOrderModalVisible(false);\n    };\n    const handleStartMRP = async (order)=>{\n        try {\n            // 验证前置条件\n            if (order.status !== \"confirmed\") {\n                message.error(\"只有已审核的订单才能启动MRP\");\n                return;\n            }\n            if (order.mrpStatus === \"completed\") {\n                message.warning(\"该订单的MRP已经执行完成\");\n                return;\n            }\n            if (order.mrpStatus === \"in_progress\") {\n                message.warning(\"该订单的MRP正在执行中，请等待完成\");\n                return;\n            }\n            // 开始MRP执行\n            setMRPExecuting(true);\n            setMrpExecutionStep(1);\n            setShowMRPResult(false);\n            setMrpResult(null);\n            // 更新订单MRP状态为执行中\n            try {\n                await _services_dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_3__.dataAccessManager.orders.update(order.id, {\n                    mrpStatus: \"in_progress\",\n                    updatedAt: new Date().toISOString()\n                });\n                // 🔧 修复：立即更新selectedOrder状态，确保按钮立即禁用\n                if (selectedOrder && selectedOrder.id === order.id) {\n                    setSelectedOrder({\n                        ...selectedOrder,\n                        mrpStatus: \"in_progress\",\n                        updatedAt: new Date().toISOString()\n                    });\n                }\n                // 立即刷新本地状态\n                await refreshOrders();\n            } catch (error) {\n                console.error(\"更新订单MRP状态失败:\", error);\n            }\n            // 动态导入MRP服务\n            const { mrpService } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_services_mrpService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/services/mrpService */ \"(app-pages-browser)/./src/services/mrpService.ts\"));\n            // 步骤1: 启动MRP\n            message.info(\"正在启动MRP...\");\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            setMrpExecutionStep(2);\n            // 步骤2: MRP分析\n            message.info(\"正在进行MRP分析...\");\n            await new Promise((resolve)=>setTimeout(resolve, 1500));\n            setMrpExecutionStep(3);\n            // 步骤3: 生成生产订单\n            message.info(\"正在生成生产订单...\");\n            // 执行MRP\n            const mrpResult = await mrpService.executeMRP({\n                salesOrder: order,\n                executedBy: \"当前用户\",\n                executionDate: new Date().toISOString()\n            });\n            setMrpExecutionStep(4);\n            await new Promise((resolve)=>setTimeout(resolve, 500));\n            // 步骤4: 完成\n            setMRPExecuting(false);\n            setMrpResult(mrpResult);\n            setShowMRPResult(true);\n            // 更新订单MRP状态为已完成\n            try {\n                await _services_dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_3__.dataAccessManager.orders.update(order.id, {\n                    mrpStatus: \"completed\",\n                    mrpExecutedAt: new Date().toISOString(),\n                    mrpExecutedBy: \"当前用户\",\n                    mrpResultId: mrpResult.id,\n                    updatedAt: new Date().toISOString()\n                });\n                // 🔧 修复：立即更新selectedOrder状态，确保按钮状态正确\n                if (selectedOrder && selectedOrder.id === order.id) {\n                    setSelectedOrder({\n                        ...selectedOrder,\n                        mrpStatus: \"completed\",\n                        mrpExecutedAt: new Date().toISOString(),\n                        mrpExecutedBy: \"当前用户\",\n                        mrpResultId: mrpResult.id,\n                        updatedAt: new Date().toISOString()\n                    });\n                }\n            } catch (error) {\n                console.error(\"更新订单MRP完成状态失败:\", error);\n            }\n            // 步骤5: MRP执行完成，刷新订单数据\n            await refreshOrders() // 刷新订单列表以获取最新状态\n            ;\n            if (mrpResult.generatedProductionOrders && mrpResult.generatedProductionOrders.length > 0) {\n                message.success(\"MRP执行完成！生成了 \".concat(mrpResult.totalProductionOrders, \" 个生产订单，请前往生产管理模块查看\"));\n            } else {\n                message.success(\"MRP执行完成！未生成新的生产订单（可能库存充足）\");\n            }\n        } catch (error) {\n            setMRPExecuting(false);\n            // 更新订单MRP状态为失败\n            const updateResult = await (0,_utils_dataAccessErrorHandler__WEBPACK_IMPORTED_MODULE_4__.handleApiResponse)(()=>_services_dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_3__.dataAccessManager.orders.update(order.id, {\n                    mrpStatus: \"failed\",\n                    updatedAt: new Date().toISOString()\n                }), \"更新订单MRP状态\");\n            if (updateResult) {\n                // 🔧 修复：立即更新selectedOrder状态\n                if (selectedOrder && selectedOrder.id === order.id) {\n                    setSelectedOrder({\n                        ...selectedOrder,\n                        mrpStatus: \"failed\",\n                        updatedAt: new Date().toISOString()\n                    });\n                }\n                await refreshOrders();\n            }\n            message.error(\"MRP执行失败: \".concat(error instanceof Error ? error.message : \"未知错误\"));\n        }\n    };\n    const handleChangeModalOk = ()=>{\n        changeForm.validateFields().then(async (values)=>{\n            if (!selectedOrder) return;\n            // 🔧 P4-3数据验证统一：使用OrderValidationService验证订单变更\n            const changeValidation = _services_validation_OrderValidationService__WEBPACK_IMPORTED_MODULE_16__[\"default\"].validateOrderChange(selectedOrder, values.changeType, values.originalValue, values.newValue);\n            if (!changeValidation.isValid) {\n                message.error(\"变更验证失败：\".concat(changeValidation.errors[0]));\n                return;\n            }\n            // 显示警告信息（如果有）\n            if (changeValidation.warnings && changeValidation.warnings.length > 0) {\n                changeValidation.warnings.forEach((warning)=>{\n                    message.warning(warning);\n                });\n            }\n            const now = new Date().toISOString();\n            const newChange = {\n                id: Date.now().toString(),\n                orderNumber: selectedOrder.orderNumber,\n                ...values,\n                changeStatus: \"pending\",\n                applicant: \"当前用户\",\n                customerConfirmed: false,\n                productionStatus: selectedOrder.productionStatus,\n                createdAt: now\n            };\n            // 更新订单变更记录\n            const currentOrder = orders.find((o)=>o.id === selectedOrder.id);\n            if (currentOrder) {\n                const updatedChanges = [\n                    ...currentOrder.changes || [],\n                    newChange\n                ];\n                const result = await (0,_utils_dataAccessErrorHandler__WEBPACK_IMPORTED_MODULE_4__.handleApiResponse)(()=>_services_dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_3__.dataAccessManager.orders.update(selectedOrder.id, {\n                        changes: updatedChanges,\n                        updatedAt: now\n                    }), \"提交变更申请\");\n                if (result) {\n                    await refreshOrders();\n                    setIsChangeModalVisible(false);\n                    changeForm.resetFields();\n                    message.success(\"订单变更申请提交成功\");\n                } else {\n                    message.error(\"提交变更申请失败\");\n                }\n            }\n        });\n    };\n    const handleChangeModalCancel = ()=>{\n        setIsChangeModalVisible(false);\n        setSelectedChangeType(\"\") // 重置变更类型\n        ;\n        changeForm.resetFields();\n    };\n    // 处理变更审批\n    const handleChangeApproval = async (changeId, action, reason)=>{\n        if (!selectedOrder) return;\n        try {\n            var _currentOrder_changes;\n            const now = new Date().toISOString();\n            const currentOrder = orders.find((o)=>o.id === selectedOrder.id);\n            if (!currentOrder) return;\n            // 更新变更记录状态\n            const updatedChanges = ((_currentOrder_changes = currentOrder.changes) === null || _currentOrder_changes === void 0 ? void 0 : _currentOrder_changes.map((change)=>{\n                if (change.id === changeId) {\n                    return {\n                        ...change,\n                        changeStatus: action === \"approve\" ? \"approved\" : \"rejected\",\n                        approver: \"当前用户\",\n                        approvedAt: now,\n                        ...reason && {\n                            rejectionReason: reason\n                        }\n                    };\n                }\n                return change;\n            })) || [];\n            // 如果变更被批准，需要执行相应的变更逻辑\n            const approvedChange = updatedChanges.find((c)=>c.id === changeId);\n            if (action === \"approve\" && approvedChange) {\n                switch(approvedChange.changeType){\n                    case \"cancel\":\n                        // 验证订单是否可以取消\n                        const cancelValidation = await _services_OrderCancellationService__WEBPACK_IMPORTED_MODULE_8__.OrderCancellationService.validateCancellation(selectedOrder);\n                        if (!cancelValidation.canCancel) {\n                            message.error(\"无法取消订单: \".concat(cancelValidation.reason));\n                            return;\n                        }\n                        if (cancelValidation.warnings.length > 0) {\n                            // 显示警告信息，让用户确认\n                            await new Promise((resolve, reject)=>{\n                                modal.confirm({\n                                    title: \"订单取消确认\",\n                                    content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"确定要取消此订单吗？\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                                lineNumber: 847,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                style: {\n                                                    marginTop: 8,\n                                                    paddingLeft: 20\n                                                },\n                                                children: cancelValidation.warnings.map((warning, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        style: {\n                                                            color: \"#fa8c16\"\n                                                        },\n                                                        children: warning\n                                                    }, index, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 850,\n                                                        columnNumber: 27\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                                lineNumber: 848,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                        lineNumber: 846,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    onOk: ()=>resolve(),\n                                    onCancel: ()=>reject(new Error(\"用户取消操作\"))\n                                });\n                            });\n                        }\n                        await handleOrderCancellation(selectedOrder, approvedChange);\n                        break;\n                    case \"quantity\":\n                        // 执行数量变更\n                        await handleQuantityChange(selectedOrder, approvedChange);\n                        break;\n                    case \"delivery_date\":\n                        // 执行交期变更\n                        await handleDeliveryDateChange(selectedOrder, approvedChange);\n                        break;\n                }\n                // 更新变更记录状态为已执行\n                const finalChanges = updatedChanges.map((change)=>{\n                    if (change.id === changeId) {\n                        return {\n                            ...change,\n                            changeStatus: \"executed\",\n                            executedAt: new Date().toISOString()\n                        };\n                    }\n                    return change;\n                });\n                // 更新订单变更记录\n                const updateResult = await (0,_utils_dataAccessErrorHandler__WEBPACK_IMPORTED_MODULE_4__.handleApiResponse)(()=>_services_dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_3__.dataAccessManager.orders.update(selectedOrder.id, {\n                        changes: finalChanges,\n                        updatedAt: now\n                    }), \"更新订单变更记录\");\n                if (!updateResult) {\n                    message.error(\"变更\".concat(action === \"approve\" ? \"批准\" : \"拒绝\", \"失败\"));\n                    return;\n                }\n            } else {\n                // 如果是拒绝变更，只更新变更记录\n                const updateResult = await (0,_utils_dataAccessErrorHandler__WEBPACK_IMPORTED_MODULE_4__.handleApiResponse)(()=>_services_dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_3__.dataAccessManager.orders.update(selectedOrder.id, {\n                        changes: updatedChanges,\n                        updatedAt: now\n                    }), \"更新变更记录\");\n                if (!updateResult) {\n                    message.error(\"变更\".concat(action === \"approve\" ? \"批准\" : \"拒绝\", \"失败\"));\n                    return;\n                }\n            }\n            await refreshOrders();\n            message.success(\"变更\".concat(action === \"approve\" ? \"批准\" : \"拒绝\", \"成功\"));\n        } catch (error) {\n            message.error(\"变更\".concat(action === \"approve\" ? \"批准\" : \"拒绝\", \"失败: \").concat(error instanceof Error ? error.message : \"未知错误\"));\n            console.error(\"变更审批失败:\", error);\n        }\n    };\n    // 处理订单取消的核心逻辑\n    const handleOrderCancellation = async (order, change)=>{\n        try {\n            // 使用专门的订单取消服务\n            const result = await _services_OrderCancellationService__WEBPACK_IMPORTED_MODULE_8__.OrderCancellationService.executeOrderCancellation(order, change);\n            if (result.success) {\n                message.success(\"订单 \".concat(order.orderNumber, \" 已成功取消，\") + \"同时取消了 \".concat(result.productionOrdersCancelled, \" 个生产订单和 \").concat(result.workOrdersCancelled, \" 个工单\"));\n            } else {\n                throw new Error(result.errors.join(\"; \"));\n            }\n        } catch (error) {\n            console.error(\"订单取消处理失败:\", error);\n            throw error;\n        }\n    };\n    // 处理数量变更的核心逻辑\n    const handleQuantityChange = async (order, change)=>{\n        try {\n            // 使用专门的数量变更服务\n            const result = await _services_OrderQuantityChangeService__WEBPACK_IMPORTED_MODULE_9__.OrderQuantityChangeService.executeQuantityChange(order, change);\n            if (result.success) {\n                message.success(\"订单 \".concat(order.orderNumber, \" 数量变更成功，\") + \"从 \".concat(change.originalValue, \" 变更为 \").concat(change.newValue, \"，\") + \"影响了 \".concat(result.productionOrdersAffected, \" 个生产订单和 \").concat(result.workOrdersAffected, \" 个工单\"));\n            } else {\n                throw new Error(result.errors.join(\"; \"));\n            }\n        } catch (error) {\n            console.error(\"数量变更执行失败:\", error);\n            throw error;\n        }\n    };\n    // 处理交期变更的核心逻辑\n    const handleDeliveryDateChange = async (order, change)=>{\n        try {\n            // 使用专门的交期变更服务\n            const result = await _services_OrderDeliveryDateChangeService__WEBPACK_IMPORTED_MODULE_10__.OrderDeliveryDateChangeService.executeDeliveryDateChange(order, change);\n            if (result.success) {\n                const successMessage = \"订单 \".concat(order.orderNumber, \" 交期变更成功，\") + \"从 \".concat(change.originalValue, \" 变更为 \").concat(change.newValue, \"，\") + \"影响了 \".concat(result.productionOrdersAffected, \" 个生产订单和 \").concat(result.workOrdersAffected, \" 个工单\");\n                if (result.scheduleAdjustmentsRequired) {\n                    message.warning(successMessage + \"\\n注意：有 \".concat(result.scheduleImpact.conflictingOrders.length, \" 个订单的排程需要调整\"));\n                } else {\n                    message.success(successMessage);\n                }\n            } else {\n                throw new Error(result.errors.join(\"; \"));\n            }\n        } catch (error) {\n            console.error(\"交期变更执行失败:\", error);\n            throw error;\n        }\n    };\n    // 批量操作函数\n    const handleBatchApprove = ()=>{\n        if (selectedRowKeys.length === 0) {\n            message.warning(\"请先选择要审核的订单\");\n            return;\n        }\n        const pendingOrders = selectedOrders.filter((order)=>order.status === \"pending\");\n        if (pendingOrders.length === 0) {\n            message.warning(\"所选订单中没有未审核的订单\");\n            return;\n        }\n        modal.confirm({\n            title: \"批量审核确认\",\n            content: \"确定要审核 \".concat(pendingOrders.length, \" 个订单吗？\"),\n            onOk: async ()=>{\n                setBatchLoading(true);\n                try {\n                    // 批量更新订单状态\n                    const updatePromises = pendingOrders.map((order)=>(0,_utils_dataAccessErrorHandler__WEBPACK_IMPORTED_MODULE_4__.handleApiResponse)(()=>_services_dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_3__.dataAccessManager.orders.update(order.id, {\n                                status: \"confirmed\",\n                                productionStatus: \"not_started\",\n                                updatedAt: new Date().toISOString()\n                            }), \"审核订单 \".concat(order.orderNumber)));\n                    const results = await Promise.all(updatePromises);\n                    const successCount = results.filter((result)=>result !== null).length;\n                    if (successCount > 0) {\n                        await refreshOrders();\n                        setSelectedRowKeys([]);\n                        setSelectedOrders([]);\n                        message.success(\"成功审核 \".concat(successCount, \" 个订单\"));\n                    } else {\n                        message.error(\"批量审核失败，没有订单被成功审核\");\n                    }\n                } catch (error) {\n                    message.error(\"批量审核失败: \".concat(error instanceof Error ? error.message : \"未知错误\"));\n                    console.error(\"批量审核异常:\", error);\n                } finally{\n                    setBatchLoading(false);\n                }\n            }\n        });\n    };\n    const handleBatchUnapprove = ()=>{\n        if (selectedRowKeys.length === 0) {\n            message.warning(\"请先选择要反审核的订单\");\n            return;\n        }\n        const confirmedOrders = selectedOrders.filter((order)=>order.status === \"confirmed\");\n        if (confirmedOrders.length === 0) {\n            message.warning(\"所选订单中没有已审核的订单\");\n            return;\n        }\n        modal.confirm({\n            title: \"批量反审核确认\",\n            content: \"确定要反审核 \".concat(confirmedOrders.length, \" 个订单吗？\"),\n            onOk: async ()=>{\n                setBatchLoading(true);\n                try {\n                    // 批量更新订单状态\n                    const updatePromises = confirmedOrders.map((order)=>(0,_utils_dataAccessErrorHandler__WEBPACK_IMPORTED_MODULE_4__.handleApiResponse)(()=>_services_dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_3__.dataAccessManager.orders.update(order.id, {\n                                status: \"pending\",\n                                productionStatus: \"not_started\",\n                                updatedAt: new Date().toISOString()\n                            }), \"反审核订单 \".concat(order.orderNumber)));\n                    const results = await Promise.all(updatePromises);\n                    const successCount = results.filter((result)=>result !== null).length;\n                    if (successCount > 0) {\n                        await refreshOrders();\n                        setSelectedRowKeys([]);\n                        setSelectedOrders([]);\n                        message.success(\"成功反审核 \".concat(successCount, \" 个订单\"));\n                    } else {\n                        message.error(\"批量反审核失败，没有订单被成功反审核\");\n                    }\n                } catch (error) {\n                    message.error(\"批量反审核失败: \".concat(error instanceof Error ? error.message : \"未知错误\"));\n                    console.error(\"批量反审核异常:\", error);\n                } finally{\n                    setBatchLoading(false);\n                }\n            }\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"24px\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                gutter: [\n                    16,\n                    16\n                ],\n                style: {\n                    marginBottom: \"24px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                        xs: 24,\n                        sm: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                title: \"订单总数\",\n                                value: stats.total,\n                                suffix: \"个\",\n                                valueStyle: {\n                                    color: \"#1890ff\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 1101,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 1100,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                        lineNumber: 1099,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                        xs: 24,\n                        sm: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                title: \"待审核\",\n                                value: stats.pending,\n                                suffix: \"个\",\n                                valueStyle: {\n                                    color: \"#faad14\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 1111,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 1110,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                        lineNumber: 1109,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                        xs: 24,\n                        sm: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                title: \"已确认\",\n                                value: stats.confirmed,\n                                suffix: \"个\",\n                                valueStyle: {\n                                    color: \"#52c41a\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 1121,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 1120,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                        lineNumber: 1119,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                        xs: 24,\n                        sm: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                title: \"总金额\",\n                                value: stats.totalAmount,\n                                precision: 2,\n                                prefix: \"\\xa5\",\n                                valueStyle: {\n                                    color: \"#f50\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 1131,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 1130,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                        lineNumber: 1129,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                lineNumber: 1098,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                style: {\n                    marginBottom: \"16px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                    gutter: [\n                        16,\n                        16\n                    ],\n                    align: \"middle\",\n                    justify: \"space-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                            xs: 24,\n                            lg: 18,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                wrap: true,\n                                size: \"middle\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        placeholder: \"搜索订单号、客户名称\",\n                                        prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_ExportOutlined_PlusOutlined_SearchOutlined_SwapOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1149,\n                                            columnNumber: 25\n                                        }, void 0),\n                                        value: searchText,\n                                        onChange: (e)=>setSearchText(e.target.value),\n                                        style: {\n                                            width: \"256px\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                        lineNumber: 1147,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        placeholder: \"订单状态\",\n                                        value: filterStatus,\n                                        onChange: setFilterStatus,\n                                        style: {\n                                            width: \"128px\"\n                                        },\n                                        allowClear: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                                value: \"pending\",\n                                                children: \"待审核\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                                lineNumber: 1161,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                                value: \"confirmed\",\n                                                children: \"已确认\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                                lineNumber: 1162,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                                value: \"completed\",\n                                                children: \"已完成\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                                lineNumber: 1163,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                                value: \"cancelled\",\n                                                children: \"已取消\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                                lineNumber: 1164,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                        lineNumber: 1154,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RangePicker, {\n                                        value: dateRange,\n                                        onChange: setDateRange,\n                                        placeholder: [\n                                            \"开始日期\",\n                                            \"结束日期\"\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                        lineNumber: 1166,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 1146,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 1145,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                            xs: 24,\n                            lg: 6,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_ExportOutlined_PlusOutlined_SearchOutlined_SwapOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1175,\n                                            columnNumber: 29\n                                        }, void 0),\n                                        children: \"导出\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                        lineNumber: 1175,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                        type: \"primary\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_ExportOutlined_PlusOutlined_SearchOutlined_SwapOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1178,\n                                            columnNumber: 23\n                                        }, void 0),\n                                        onClick: ()=>setIsAddOrderModalVisible(true),\n                                        children: \"新建订单\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                        lineNumber: 1176,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 1174,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 1173,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                    lineNumber: 1144,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                lineNumber: 1143,\n                columnNumber: 7\n            }, undefined),\n            selectedRowKeys.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                style: {\n                    marginBottom: \"16px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_41__[\"default\"], {\n                    message: \"已选择 \".concat(selectedRowKeys.length, \" 个订单\"),\n                    type: \"info\",\n                    showIcon: true,\n                    action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                size: \"small\",\n                                onClick: handleBatchApprove,\n                                loading: batchLoading,\n                                children: \"批量审核\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 1197,\n                                columnNumber: 17\n                            }, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                size: \"small\",\n                                onClick: handleBatchUnapprove,\n                                loading: batchLoading,\n                                children: \"批量反审核\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 1204,\n                                columnNumber: 17\n                            }, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                size: \"small\",\n                                danger: true,\n                                onClick: handleBatchDelete,\n                                loading: batchLoading,\n                                children: \"批量删除\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 1211,\n                                columnNumber: 17\n                            }, void 0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                        lineNumber: 1196,\n                        columnNumber: 15\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                    lineNumber: 1191,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                lineNumber: 1190,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                title: \"销售订单列表\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_42__[\"default\"], {\n                    columns: columns,\n                    dataSource: filteredOrders,\n                    rowKey: \"id\",\n                    loading: ordersLoading || loading,\n                    rowSelection: {\n                        selectedRowKeys,\n                        onChange: (keys, rows)=>{\n                            setSelectedRowKeys(keys);\n                            setSelectedOrders(rows);\n                        },\n                        getCheckboxProps: (record)=>({\n                                disabled: record.status === \"completed\"\n                            })\n                    },\n                    pagination: {\n                        total: filteredOrders.length,\n                        pageSize: 10,\n                        showSizeChanger: true,\n                        showQuickJumper: true,\n                        showTotal: (total, range)=>\"第 \".concat(range[0], \"-\").concat(range[1], \" 条/共 \").concat(total, \" 条\"),\n                        pageSizeOptions: [\n                            \"10\",\n                            \"20\",\n                            \"50\",\n                            \"100\"\n                        ]\n                    },\n                    scroll: {\n                        x: 1600\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                    lineNumber: 1227,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                lineNumber: 1226,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_OrderDetailModal__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                visible: isDetailModalVisible,\n                onCancel: ()=>setIsDetailModalVisible(false),\n                order: selectedOrder,\n                config: _components_common_OrderDetailModal_configs_salesOrderConfig__WEBPACK_IMPORTED_MODULE_18__[\"default\"]\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                lineNumber: 1255,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sales_AddOrderModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                visible: isAddOrderModalVisible,\n                onCancel: ()=>setIsAddOrderModalVisible(false),\n                onSuccess: ()=>{\n                    setIsAddOrderModalVisible(false);\n                    refreshOrders();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                lineNumber: 1263,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_43__[\"default\"], {\n                title: \"订单变更\",\n                open: isChangeModalVisible,\n                onOk: handleChangeSubmit,\n                onCancel: ()=>setIsChangeModalVisible(false),\n                width: 600,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                    form: changeForm,\n                    layout: \"vertical\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"].Item, {\n                            name: \"changeType\",\n                            label: \"变更类型\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择变更类型\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                placeholder: \"请选择变更类型\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"quantity\",\n                                        children: \"数量变更\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                        lineNumber: 1287,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"deliveryDate\",\n                                        children: \"交期变更\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                        lineNumber: 1288,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"cancel\",\n                                        children: \"订单取消\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                        lineNumber: 1289,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 1286,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 1281,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"].Item, {\n                            name: \"reason\",\n                            label: \"变更原因\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入变更原因\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TextArea, {\n                                rows: 4,\n                                placeholder: \"请详细说明变更原因\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 1297,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 1292,\n                            columnNumber: 11\n                        }, undefined),\n                        selectedChangeType === \"quantity\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"].Item, {\n                            name: \"newQuantity\",\n                            label: \"新数量\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入新数量\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                min: 1,\n                                style: {\n                                    width: \"100%\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 1305,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 1300,\n                            columnNumber: 13\n                        }, undefined),\n                        selectedChangeType === \"deliveryDate\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"].Item, {\n                            name: \"newDeliveryDate\",\n                            label: \"新交期\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择新交期\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                style: {\n                                    width: \"100%\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                                lineNumber: 1314,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                            lineNumber: 1309,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                    lineNumber: 1280,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n                lineNumber: 1273,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n        lineNumber: 1096,\n        columnNumber: 5\n    }, undefined);\n};\n_s(OrderManagement, \"ulpOxUkaGHBxc+E0GASVMjRmbxw=\", false, function() {\n    return [\n        _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"].useApp,\n        _barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"].useForm,\n        _hooks_useOrdersData__WEBPACK_IMPORTED_MODULE_14__.useOrdersData,\n        _hooks_useEventListener__WEBPACK_IMPORTED_MODULE_12__.useDataChangeListener,\n        _hooks_useDebouncedCallback__WEBPACK_IMPORTED_MODULE_11__.useDebouncedSearch,\n        _hooks_useDataAccessMonitor__WEBPACK_IMPORTED_MODULE_13__.useDataAccessMonitor,\n        _hooks_useProductionOrders__WEBPACK_IMPORTED_MODULE_7__.useMRPProductionOrders,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = OrderManagement;\nfunction OrderManagementPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_App_Badge_Button_Card_Col_DatePicker_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Steps_Table_Tag_Tooltip_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(OrderManagement, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n            lineNumber: 1326,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\erp软件\\\\src\\\\app\\\\sales\\\\orders\\\\page.tsx\",\n        lineNumber: 1325,\n        columnNumber: 5\n    }, this);\n}\n_c1 = OrderManagementPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"OrderManagement\");\n$RefreshReg$(_c1, \"OrderManagementPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/sales/orders/page.tsx\n"));

/***/ })

});