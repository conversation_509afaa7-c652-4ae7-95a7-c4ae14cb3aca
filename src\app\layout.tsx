import { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { AntdProvider } from '@/components/providers/AntdProvider'
import { AuthProvider } from '@/components/providers/AuthProvider'
// ✅ 架构合规：初始化DataAccessManager配置
import '@/config/dataAccessConfig'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'ERP管理系统',
  description: '现代化企业资源规划管理系统',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN">
      <body className={inter.className}>
        <AntdProvider>
          <AuthProvider>
            {children}
          </AuthProvider>
        </AntdProvider>
      </body>
    </html>
  )
}
