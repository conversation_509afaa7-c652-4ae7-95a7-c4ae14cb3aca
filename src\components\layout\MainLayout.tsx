'use client'

import React, { useState, useEffect } from 'react'
import { Layout } from 'antd'
import { useAppStore } from '@/store/useAppStore'
import { styleHelpers } from '@/utils/styles/antdHelpers'
import { Sidebar } from './Sidebar'
import { Header } from './Header'

const { Content } = Layout

interface MainLayoutProps {
  children: React.ReactNode
}

export const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const { sidebarCollapsed } = useAppStore()
  const [isMobile, setIsMobile] = useState(false)

  // 检测屏幕尺寸
  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 768)
    }

    checkScreenSize()
    window.addEventListener('resize', checkScreenSize)

    return () => window.removeEventListener('resize', checkScreenSize)
  }, [])

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sidebar />
      <Layout style={{
        transition: 'all 0.3s ease',
        marginLeft: isMobile
          ? 0
          : sidebarCollapsed
            ? 80
            : 256
      }}>
        <Header />
        <Content style={{
          padding: isMobile ? styleHelpers.spacing.sm : styleHelpers.spacing.lg,
          background: styleHelpers.colors.gray[50],
          overflow: 'auto'
        }}>
          {children}
        </Content>
      </Layout>
    </Layout>
  )
}
