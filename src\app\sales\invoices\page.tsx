'use client'

import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Input,
  Select,
  Modal,
  Form,
  InputNumber,
  Row,
  Col,
  Statistic,
  DatePicker,
  message,
  Popconfirm,
  Descriptions,
  Alert,
  Timeline
} from 'antd'
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  ExportOutlined,
  FileTextOutlined,
  SendOutlined,
  CheckCircleOutlined,
  CalculatorOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import { Invoice} from '@/types'
import { styleHelpers } from '@/utils/styles/antdHelpers'
import FormSelect from '@/components/FormSelect'
import dayjs from 'dayjs'

const { Option } = Select
const { TextArea } = Input
const { RangePicker } = DatePicker

const InvoiceManagement: React.FC = () => {
  const [invoices, setInvoices] = useState<Invoice[]>([])
  const [loading, setLoading] = useState(false)
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false)
  const [isAutoInvoiceModalVisible, setIsAutoInvoiceModalVisible] = useState(false)
  const [editingInvoice, setEditingInvoice] = useState<Invoice | null>(null)
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null)
  const [form] = Form.useForm()
  const [autoForm] = Form.useForm()
  const [searchText, setSearchText] = useState('')
  const [filterStatus, setFilterStatus] = useState<string | undefined>(undefined)
  const [filterType, setFilterType] = useState<string | undefined>(undefined)

  // 模拟数据
  useEffect(() => {
    const mockInvoices: Invoice[] = [
      {
        id: '1',
        invoiceNumber: 'INV-2024-001',
        invoiceType: 'vat_special',
        customerId: '1',
        customerName: '上海包装材料有限公司',
        taxNumber: '91310000123456789X',
        invoiceDate: '2024-01-25',
        totalAmount: 106250,
        taxAmount: 13812.5,
        totalWithTax: 120062.5,
        status: 'issued',
        triggerType: 'delivery',
        relatedOrders: ['SO-2024-001'],
        relatedDeliveries: ['DN-2024-001'],
        items: [
          {
            id: '1',
            invoiceNumber: 'INV-2024-001',
            productName: '圆形餐盘202mm',
            specification: 'CP-202',
            unit: '个',
            quantity: 3000,
            unitPrice: 0.125,
            amount: 375,
            taxRate: 0.13,
            taxAmount: 48.75
          }
        ],
        remark: '发货完成自动开票',
        createdAt: '2024-01-25T10:00:00',
        updatedAt: '2024-01-25T10:00:00'
      },
      {
        id: '2',
        invoiceNumber: 'INV-2024-002',
        invoiceType: 'vat_ordinary',
        customerId: '2',
        customerName: '北京绿色包装科技公司',
        taxNumber: '91110000987654321Y',
        invoiceDate: '2024-01-28',
        totalAmount: 73000,
        taxAmount: 9490,
        totalWithTax: 82490,
        status: 'sent',
        triggerType: 'monthly',
        relatedOrders: ['SO-2024-002'],
        relatedDeliveries: ['DN-2024-002'],
        items: [
          {
            id: '2',
            invoiceNumber: 'INV-2024-002',
            productName: '方形餐盒180mm',
            specification: 'CP-201',
            unit: '个',
            quantity: 20000,
            unitPrice: 0.165,
            amount: 3300,
            taxRate: 0.13,
            taxAmount: 429
          }
        ],
        remark: '月结客户统一开票',
        createdAt: '2024-01-28T16:00:00',
        updatedAt: '2024-01-28T16:00:00'
      }
    ]
    setInvoices(mockInvoices)
  }, [])

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const statusMap = {
      'draft': { color: 'default', text: '草稿', icon: <FileTextOutlined /> },
      'issued': { color: 'blue', text: '已开票', icon: <CheckCircleOutlined /> },
      'sent': { color: 'green', text: '已发送', icon: <SendOutlined /> },
      'confirmed': { color: 'cyan', text: '已确认', icon: <CheckCircleOutlined /> }
    }
    const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: '未知', icon: null }
    return (
      <Tag color={config.color} icon={config.icon}>
        {config.text}
      </Tag>
    )
  }

  // 获取发票类型标签
  const getTypeTag = (type: string) => {
    const typeMap = {
      'vat_special': { color: 'red', text: '增值税专用发票' },
      'vat_ordinary': { color: 'blue', text: '增值税普通发票' },
      'receipt': { color: 'green', text: '收据' }
    }
    const config = typeMap[type as keyof typeof typeMap] || { color: 'default', text: '其他' }
    return <Tag color={config.color}>{config.text}</Tag>
  }

  // 获取触发类型标签
  const getTriggerTypeTag = (type: string) => {
    const typeMap = {
      'delivery': { color: 'blue', text: '发货触发' },
      'prepayment': { color: 'orange', text: '预付款' },
      'monthly': { color: 'purple', text: '月结' }
    }
    const config = typeMap[type as keyof typeof typeMap] || { color: 'default', text: '手动' }
    return <Tag color={config.color}>{config.text}</Tag>
  }

  // 表格列定义
  const columns: ColumnsType<Invoice> = [
    {
      title: '发票号码',
      dataIndex: 'invoiceNumber',
      key: 'invoiceNumber',
      width: 140,
      fixed: 'left'
    },
    {
      title: '客户名称',
      dataIndex: 'customerName',
      key: 'customerName',
      width: 180,
      ellipsis: true
    },
    {
      title: '发票类型',
      dataIndex: 'invoiceType',
      key: 'invoiceType',
      width: 140,
      render: (type: string) => getTypeTag(type)
    },
    {
      title: '开票日期',
      dataIndex: 'invoiceDate',
      key: 'invoiceDate',
      width: 120,
      sorter: (a, b) => new Date(a.invoiceDate).getTime() - new Date(b.invoiceDate).getTime()
    },
    {
      title: '金额信息',
      key: 'amountInfo',
      width: 150,
      render: (_, record) => (
        <div>
          <div>不含税: ¥{record.totalAmount.toLocaleString()}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            税额: ¥{record.taxAmount.toLocaleString()}
          </div>
          <div style={{ fontWeight: 'bold', color: '#3f8600' }}>
            含税: ¥{record.totalWithTax.toLocaleString()}
          </div>
        </div>
      )
    },
    {
      title: '触发方式',
      dataIndex: 'triggerType',
      key: 'triggerType',
      width: 100,
      render: (type: string) => getTriggerTypeTag(type)
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => getStatusTag(status)
    },
    {
      title: '关联订单',
      dataIndex: 'relatedOrders',
      key: 'relatedOrders',
      width: 120,
      render: (orders: string[]) => (
        <div>
          {orders.map(order => (
            <Tag key={order}>{order}</Tag>
          ))}
        </div>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 220,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button 
            type="link" 
            icon={<EyeOutlined />} 
            onClick={() => handleViewDetail(record)}
          >
            详情
          </Button>
          <Button 
            type="link" 
            icon={<EditOutlined />} 
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          {record.status === 'issued' && (
            <Button 
              type="link" 
              icon={<SendOutlined />} 
              onClick={() => handleSend(record.id)}
            >
              发送
            </Button>
          )}
          <Popconfirm
            title="确定要删除这张发票吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ]

  // 过滤后的发票数据
  const filteredInvoices = invoices.filter(invoice => {
    const matchesSearch = !searchText || 
      invoice.invoiceNumber.toLowerCase().includes(searchText.toLowerCase()) ||
      invoice.customerName.toLowerCase().includes(searchText.toLowerCase()) ||
      invoice.taxNumber.toLowerCase().includes(searchText.toLowerCase())
    
    const matchesStatus = !filterStatus || invoice.status === filterStatus
    const matchesType = !filterType || invoice.invoiceType === filterType
    
    return matchesSearch && matchesStatus && matchesType
  })

  // 统计数据
  const stats = {
    total: invoices.length,
    draft: invoices.filter(i => i.status === 'draft').length,
    issued: invoices.filter(i => i.status === 'issued').length,
    sent: invoices.filter(i => i.status === 'sent').length,
    totalAmount: invoices.reduce((sum, i) => sum + i.totalWithTax, 0),
    totalTax: invoices.reduce((sum, i) => sum + i.taxAmount, 0)
  }

  const handleCreate = () => {
    setEditingInvoice(null)
    setIsModalVisible(true)
    form.resetFields()
  }

  const handleAutoInvoice = () => {
    setIsAutoInvoiceModalVisible(true)
    autoForm.resetFields()
  }

  const handleEdit = (invoice: Invoice) => {
    setEditingInvoice(invoice)
    setIsModalVisible(true)
    form.setFieldsValue({
      ...invoice,
      invoiceDate: dayjs(invoice.invoiceDate)
    })
  }

  const handleViewDetail = (invoice: Invoice) => {
    setSelectedInvoice(invoice)
    setIsDetailModalVisible(true)
  }

  const handleSend = (id: string) => {
    const updatedInvoices = invoices.map(i => 
      i.id === id ? { ...i, status: 'sent' as const } : i
    )
    setInvoices(updatedInvoices)
    message.success('发票发送成功')
  }

  const handleDelete = (id: string) => {
    setInvoices(invoices.filter(i => i.id !== id))
    message.success('发票删除成功')
  }

  const handleModalOk = () => {
    form.validateFields().then(values => {
      const now = new Date().toISOString()
      const formattedValues = {
        ...values,
        invoiceDate: values.invoiceDate.format('YYYY-MM-DD')
      }

      if (editingInvoice) {
        const updatedInvoices = invoices.map(i =>
          i.id === editingInvoice.id
            ? { ...i, ...formattedValues, updatedAt: now }
            : i
        )
        setInvoices(updatedInvoices)
        message.success('发票更新成功')
      } else {
        const newInvoice: Invoice = {
          id: Date.now().toString(),
          invoiceNumber: `INV-${new Date().getFullYear()}-${String(invoices.length + 1).padStart(3, '0')}`,
          ...formattedValues,
          items: [],
          createdAt: now,
          updatedAt: now
        }
        setInvoices([...invoices, newInvoice])
        message.success('发票创建成功')
      }
      setIsModalVisible(false)
      form.resetFields()
    })
  }

  const handleModalCancel = () => {
    setIsModalVisible(false)
    form.resetFields()
  }

  const handleAutoInvoiceOk = () => {
    autoForm.validateFields().then(values => {
      // 模拟自动开票逻辑
      const { triggerType, customerIds, dateRange } = values

      message.success(`已触发${triggerType === 'delivery' ? '发货完成' :
                              triggerType === 'prepayment' ? '预付款' : '月结'}自动开票`)

      setIsAutoInvoiceModalVisible(false)
      autoForm.resetFields()
    })
  }

  const handleAutoInvoiceCancel = () => {
    setIsAutoInvoiceModalVisible(false)
    autoForm.resetFields()
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: '24px' }}>
        <h1 style={{ fontSize: '24px', fontWeight: 'bold', margin: '0 0 8px 0' }}>开票管理</h1>
        <p style={{ color: '#666', margin: 0 }}>自动化开票流程，发货触发、预付款处理、月结统一开票</p>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="发票总数"
              value={stats.total}
              suffix="张"
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="开票总额"
              value={stats.totalAmount}
              precision={0}
              prefix="¥"
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="税额合计"
              value={stats.totalTax}
              precision={0}
              prefix="¥"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="待发送发票"
              value={stats.issued}
              suffix="张"
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 开票类型分布 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="草稿"
              value={stats.draft}
              suffix="张"
              valueStyle={{ color: '#666' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="已开票"
              value={stats.issued}
              suffix="张"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="已发送"
              value={stats.sent}
              suffix="张"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 操作区域 */}
      <Card>
        <Row justify="space-between" align="middle" gutter={[16, 16]}>
          <Col xs={24} lg={18}>
            <Row gutter={[16, 16]}>
            <Input
              placeholder="搜索发票号、客户名称或税号"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: '100%' }}
            />
            <FormSelect
              placeholder="发票状态"
              value={filterStatus}
              onChange={setFilterStatus}
              style={{ width: '100%' }}
              allowClear
            >
              <Option value="draft">草稿</Option>
              <Option value="issued">已开票</Option>
              <Option value="sent">已发送</Option>
              <Option value="confirmed">已确认</Option>
            </FormSelect>
            <FormSelect
              placeholder="发票类型"
              value={filterType}
              onChange={setFilterType}
              style={{ width: '100%' }}
              allowClear
            >
              <Option value="vat_special">增值税专用发票</Option>
              <Option value="vat_ordinary">增值税普通发票</Option>
              <Option value="receipt">收据</Option>
            </FormSelect>
            </Row>
          </Col>
          <Col xs={24} lg={6}>
            <Row gutter={[8, 8]} justify="end">
              <Col>
                <Button icon={<ExportOutlined />}>导出</Button>
              </Col>
              <Col>
                <Button
                  icon={<CalculatorOutlined />}
                  onClick={handleAutoInvoice}
                >
                  自动开票
                </Button>
              </Col>
              <Col>
                <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
                  新建发票
                </Button>
              </Col>
            </Row>
          </Col>
        </Row>
      </Card>

      {/* 发票列表 */}
      <Card title="发票列表">
        <Table
          columns={columns}
          dataSource={filteredInvoices}
          rowKey="id"
          loading={loading}
          pagination={{
            total: filteredInvoices.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          scroll={{ x: 1400 }}
        />
      </Card>

      {/* 新建/编辑发票模态框 */}
      <Modal
        title={editingInvoice ? '编辑发票' : '新建发票'}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={800}
        okText="确认"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            invoiceType: 'vat_ordinary',
            status: 'draft',
            triggerType: 'delivery',
            taxRate: 0.13
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="customerId"
                label="客户ID"
                rules={[{ required: true, message: '请输入客户ID' }]}
              >
                <Input placeholder="请输入客户ID" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="customerName"
                label="客户名称"
                rules={[{ required: true, message: '请输入客户名称' }]}
              >
                <Input placeholder="请输入客户名称" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="invoiceType"
                label="发票类型"
                rules={[{ required: true, message: '请选择发票类型' }]}
              >
                <Select>
                  <Option value="vat_special">增值税专用发票</Option>
                  <Option value="vat_ordinary">增值税普通发票</Option>
                  <Option value="receipt">收据</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="triggerType"
                label="触发方式"
                rules={[{ required: true, message: '请选择触发方式' }]}
              >
                <Select>
                  <Option value="delivery">发货完成触发</Option>
                  <Option value="prepayment">预付款处理</Option>
                  <Option value="monthly">月结客户</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="invoiceDate"
                label="开票日期"
                rules={[{ required: true, message: '请选择开票日期' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="taxNumber"
                label="纳税人识别号"
                rules={[{ required: true, message: '请输入纳税人识别号' }]}
              >
                <Input placeholder="请输入纳税人识别号" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="totalAmount"
                label="不含税金额"
                rules={[{ required: true, message: '请输入不含税金额' }]}
              >
                <InputNumber
                  placeholder="请输入不含税金额"
                  min={0}
                  style={{ width: '100%' }}
                  formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={value => value!.replace(/¥\s?|(,*)/g, '') as any}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="taxAmount"
                label="税额"
                rules={[{ required: true, message: '请输入税额' }]}
              >
                <InputNumber
                  placeholder="请输入税额"
                  min={0}
                  style={{ width: '100%' }}
                  formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={value => value!.replace(/¥\s?|(,*)/g, '') as any}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="totalWithTax"
                label="价税合计"
                rules={[{ required: true, message: '请输入价税合计' }]}
              >
                <InputNumber
                  placeholder="请输入价税合计"
                  min={0}
                  style={{ width: '100%' }}
                  formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={value => value!.replace(/¥\s?|(,*)/g, '') as any}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="remark"
            label="备注"
          >
            <TextArea rows={3} placeholder="请输入备注信息" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 自动开票模态框 */}
      <Modal
        title="自动开票设置"
        open={isAutoInvoiceModalVisible}
        onOk={handleAutoInvoiceOk}
        onCancel={handleAutoInvoiceCancel}
        width={600}
        okText="执行开票"
        cancelText="取消"
      >
        <Alert
          message="自动开票规则"
          description="系统将根据设置的触发条件自动生成发票，支持发货完成触发、预付款处理、月结客户统一开票"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Form
          form={autoForm}
          layout="vertical"
          initialValues={{
            triggerType: 'delivery'
          }}
        >
          <Form.Item
            name="triggerType"
            label="触发类型"
            rules={[{ required: true, message: '请选择触发类型' }]}
          >
            <Select>
              <Option value="delivery">发货完成触发</Option>
              <Option value="prepayment">预付款处理</Option>
              <Option value="monthly">月结客户统一开票</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="customerIds"
            label="客户范围"
          >
            <Select mode="multiple" placeholder="选择客户（不选则全部客户）">
              <Option value="1">上海包装材料有限公司</Option>
              <Option value="2">北京绿色包装科技公司</Option>
              <Option value="3">广州环保餐具厂</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="dateRange"
            label="日期范围"
          >
            <RangePicker style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="invoiceType"
            label="默认发票类型"
          >
            <Select placeholder="请选择默认发票类型">
              <Option value="vat_special">增值税专用发票</Option>
              <Option value="vat_ordinary">增值税普通发票</Option>
              <Option value="receipt">收据</Option>
            </Select>
          </Form.Item>

          <div style={{ marginTop: 16 }}>
            <h4>自动开票流程</h4>
            <Timeline>
              <Timeline.Item color="blue">
                检测触发条件（发货完成/预付款到账/月结时间）
              </Timeline.Item>
              <Timeline.Item color="blue">
                获取客户开票信息和税号
              </Timeline.Item>
              <Timeline.Item color="blue">
                自动计算金额和税额
              </Timeline.Item>
              <Timeline.Item color="blue">
                生成发票并推送给客户
              </Timeline.Item>
            </Timeline>
          </div>
        </Form>
      </Modal>

      {/* 发票详情模态框 */}
      <Modal
        title="发票详情"
        open={isDetailModalVisible}
        onCancel={() => setIsDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setIsDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={900}
      >
        {selectedInvoice && selectedInvoice.invoiceNumber && selectedInvoice.customerName && (
          <div>
            <Descriptions column={2} bordered>
              <Descriptions.Item label="发票号码">{selectedInvoice.invoiceNumber}</Descriptions.Item>
              <Descriptions.Item label="客户名称">{selectedInvoice.customerName}</Descriptions.Item>
              <Descriptions.Item label="发票类型">
                {getTypeTag(selectedInvoice.invoiceType)}
              </Descriptions.Item>
              <Descriptions.Item label="触发方式">
                {getTriggerTypeTag(selectedInvoice.triggerType)}
              </Descriptions.Item>
              <Descriptions.Item label="开票日期">{selectedInvoice.invoiceDate}</Descriptions.Item>
              <Descriptions.Item label="纳税人识别号">{selectedInvoice.taxNumber}</Descriptions.Item>
              <Descriptions.Item label="不含税金额">
                ¥{selectedInvoice.totalAmount.toLocaleString()}
              </Descriptions.Item>
              <Descriptions.Item label="税额">
                ¥{selectedInvoice.taxAmount.toLocaleString()}
              </Descriptions.Item>
              <Descriptions.Item label="价税合计">
                <span style={{ fontSize: '16px', fontWeight: 'bold', color: '#3f8600' }}>
                  ¥{selectedInvoice.totalWithTax.toLocaleString()}
                </span>
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                {getStatusTag(selectedInvoice.status)}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {new Date(selectedInvoice.createdAt).toLocaleString()}
              </Descriptions.Item>
              <Descriptions.Item label="更新时间">
                {new Date(selectedInvoice.updatedAt).toLocaleString()}
              </Descriptions.Item>
              <Descriptions.Item label="关联订单" span={2}>
                {selectedInvoice.relatedOrders.map(order => (
                  <Tag key={order} color="blue" style={{ marginRight: 8 }}>
                    {order}
                  </Tag>
                ))}
              </Descriptions.Item>
              <Descriptions.Item label="关联发货单" span={2}>
                {selectedInvoice.relatedDeliveries.map(delivery => (
                  <Tag key={delivery} color="green" style={{ marginRight: 8 }}>
                    {delivery}
                  </Tag>
                ))}
              </Descriptions.Item>
              <Descriptions.Item label="备注" span={2}>
                {selectedInvoice.remark || '无'}
              </Descriptions.Item>
            </Descriptions>

            <div style={{ marginTop: 24 }}>
              <h4>发票明细</h4>
              <Table
                dataSource={selectedInvoice.items}
                rowKey="id"
                pagination={false}
                size="small"
                columns={[
                  {
                    title: '产品名称',
                    dataIndex: 'productName',
                    key: 'productName',
                    width: 150
                  },
                  {
                    title: '规格型号',
                    dataIndex: 'specification',
                    key: 'specification',
                    width: 120
                  },
                  {
                    title: '单位',
                    dataIndex: 'unit',
                    key: 'unit',
                    width: 80
                  },
                  {
                    title: '数量',
                    dataIndex: 'quantity',
                    key: 'quantity',
                    width: 100,
                    render: (quantity: number) => quantity.toLocaleString()
                  },
                  {
                    title: '单价',
                    dataIndex: 'unitPrice',
                    key: 'unitPrice',
                    width: 100,
                    render: (price: number) => `¥${price.toFixed(3)}`
                  },
                  {
                    title: '金额',
                    dataIndex: 'amount',
                    key: 'amount',
                    width: 120,
                    render: (amount: number) => (
                      <span style={{ fontWeight: 'bold' }}>
                        ¥{amount.toLocaleString()}
                      </span>
                    )
                  },
                  {
                    title: '税率',
                    dataIndex: 'taxRate',
                    key: 'taxRate',
                    width: 80,
                    render: (rate: number) => `${(rate * 100).toFixed(0)}%`
                  },
                  {
                    title: '税额',
                    dataIndex: 'taxAmount',
                    key: 'taxAmount',
                    width: 100,
                    render: (amount: number) => `¥${amount.toLocaleString()}`
                  }
                ]}
              />
            </div>

            <div style={{ marginTop: 24 }}>
              <h4>开票流程</h4>
              <Timeline>
                <Timeline.Item color="green">
                  <div>
                    <div style={{ fontWeight: 'bold' }}>发票创建</div>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      {new Date(selectedInvoice.createdAt).toLocaleString()}
                    </div>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      触发方式: {selectedInvoice.triggerType === 'delivery' ? '发货完成触发' :
                                selectedInvoice.triggerType === 'prepayment' ? '预付款处理' : '月结客户'}
                    </div>
                  </div>
                </Timeline.Item>

                {selectedInvoice.status !== 'draft' && (
                  <Timeline.Item color="blue">
                    <div>
                      <div style={{ fontWeight: 'bold' }}>发票开具</div>
                      <div style={{ fontSize: '12px', color: '#666' }}>
                        发票号: {selectedInvoice.invoiceNumber}
                      </div>
                      <div style={{ fontSize: '12px', color: '#666' }}>
                        金额: ¥{selectedInvoice.totalWithTax.toLocaleString()}
                      </div>
                    </div>
                  </Timeline.Item>
                )}

                {selectedInvoice.status === 'sent' && (
                  <Timeline.Item color="cyan">
                    <div>
                      <div style={{ fontWeight: 'bold' }}>发票发送</div>
                      <div style={{ fontSize: '12px', color: '#666' }}>
                        已发送给客户确认
                      </div>
                    </div>
                  </Timeline.Item>
                )}

                {selectedInvoice.status === 'confirmed' && (
                  <Timeline.Item color="green">
                    <div>
                      <div style={{ fontWeight: 'bold' }}>客户确认</div>
                      <div style={{ fontSize: '12px', color: '#666' }}>
                        客户已确认收到发票
                      </div>
                    </div>
                  </Timeline.Item>
                )}
              </Timeline>
            </div>
          </div>
        )}
      </Modal>
    </div>
  )
}

export default InvoiceManagement
