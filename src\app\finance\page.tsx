'use client'

import React, { useState } from 'react'
import { 
  Card, 
  Table, 
  Button, 
  Space, 
  Tag, 
  Input, 
  Select, 
  DatePicker, 
  Modal,
  Form,
  InputNumber,
  Row,
  Col,
  Statistic,
  Tabs
} from 'antd'
import { 
  PlusOutlined, 
  SearchOutlined, 
  EditOutlined, 
  DeleteOutlined,
  EyeOutlined,
  ExportOutlined,
  DollarOutlined,
  <PERSON>UpOutlined,
  <PERSON>DownOutlined,
  PieChartOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import { FinancialRecord } from '@/types'
import { styleHelpers } from '@/utils/styles/antdHelpers'

const { RangePicker } = DatePicker
const { Option } = Select
const { TextArea } = Input

const FinanceManagement: React.FC = () => {
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [activeTab, setActiveTab] = useState('records')
  const [form] = Form.useForm()

  // 模拟数据
  const financialData: FinancialRecord[] = [
    {
      id: '1',
      date: '2024-01-15',
      type: 'income',
      category: '销售收入',
      amount: 125600,
      description: '销售订单SO-2024-001收款',
      reference: 'SO-2024-001',
      status: 'approved'
    },
    {
      id: '2',
      date: '2024-01-14',
      type: 'expense',
      category: '采购支出',
      amount: 89500,
      description: '采购原材料付款',
      reference: 'PO-2024-002',
      status: 'approved'
    },
    {
      id: '3',
      date: '2024-01-13',
      type: 'expense',
      category: '运营费用',
      amount: 15600,
      description: '办公用品采购',
      status: 'pending'
    },
    {
      id: '4',
      date: '2024-01-12',
      type: 'income',
      category: '其他收入',
      amount: 8900,
      description: '设备租赁收入',
      status: 'approved'
    }
  ]

  const typeMap = {
    income: { color: 'green', text: '收入', icon: <ArrowUpOutlined /> },
    expense: { color: 'red', text: '支出', icon: <ArrowDownOutlined /> }
  }

  const statusMap = {
    pending: { color: 'orange', text: '待审核' },
    approved: { color: 'green', text: '已审核' },
    rejected: { color: 'red', text: '已拒绝' }
  }

  const columns: ColumnsType<FinancialRecord> = [
    {
      title: '日期',
      dataIndex: 'date',
      key: 'date',
      width: 100,
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 80,
      render: (type: keyof typeof typeMap) => {
        const typeInfo = typeMap[type]
        return (
          <Tag color={typeInfo.color} icon={typeInfo.icon}>
            {typeInfo.text}
          </Tag>
        )
      }
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      width: 120,
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      width: 120,
      render: (amount: number, record) => {
        const color = record.type === 'income' ? '#52c41a' : '#ff4d4f'
        const prefix = record.type === 'income' ? '+' : '-'
        return (
          <span style={{ color }}>
            {prefix}¥{amount.toLocaleString()}
          </span>
        )
      }
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      width: 200,
      ellipsis: true,
    },
    {
      title: '关联单据',
      dataIndex: 'reference',
      key: 'reference',
      width: 120,
      render: (reference: string) => reference || '-'
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: keyof typeof statusMap) => {
        const statusInfo = statusMap[status]
        return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>
      }
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Button type="text" icon={<EyeOutlined />} size="small">
            查看
          </Button>
          <Button type="text" icon={<EditOutlined />} size="small">
            编辑
          </Button>
          <Button type="text" danger icon={<DeleteOutlined />} size="small">
            删除
          </Button>
        </Space>
      ),
    },
  ]

  const handleCreateRecord = () => {
    setIsModalVisible(true)
  }

  const handleModalOk = () => {
    form.validateFields().then(values => {
      setIsModalVisible(false)
      form.resetFields()
    })
  }

  const handleModalCancel = () => {
    setIsModalVisible(false)
    form.resetFields()
  }

  // 计算统计数据
  const totalIncome = financialData
    .filter(record => record.type === 'income' && record.status === 'approved')
    .reduce((sum, record) => sum + record.amount, 0)

  const totalExpense = financialData
    .filter(record => record.type === 'expense' && record.status === 'approved')
    .reduce((sum, record) => sum + record.amount, 0)

  const netProfit = totalIncome - totalExpense

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: styleHelpers.spacing.lg }}>
      {/* 页面标题 */}
      <div className="page-header">
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <DollarOutlined style={{ fontSize: '24px', color: '#d97706', marginRight: '12px' }} />
          <div>
            <h1 className="page-title">财务管理</h1>
            <p className="page-description">管理财务收支、成本核算和财务报表</p>
          </div>
        </div>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="本月收入"
              value={totalIncome}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="本月支出"
              value={totalExpense}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="净利润"
              value={netProfit}
              precision={2}
              prefix="¥"
              valueStyle={{ color: netProfit >= 0 ? '#52c41a' : '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="待审核记录"
              value={financialData.filter(r => r.status === 'pending').length}
              suffix="条"
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容区域 */}
      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'records',
              label: '财务记录',
              children: (
                <div style={{ display: 'flex', flexDirection: 'column', gap: styleHelpers.spacing.md }}>
                  {/* 操作区域 */}
                  <Row gutter={[16, 16]} align="middle" justify="space-between">
                    <Col xs={24} lg={18}>
                      <Space wrap size="middle">
                        <Input
                          placeholder="搜索描述或关联单据"
                          prefix={<SearchOutlined />}
                          style={{ width: '256px' }}
                        />
                        <Select placeholder="记录类型" style={{ width: '128px' }}>
                          <Option value="">全部</Option>
                          <Option value="income">收入</Option>
                          <Option value="expense">支出</Option>
                        </Select>
                        <Select placeholder="审核状态" style={{ width: '128px' }}>
                          <Option value="">全部</Option>
                          <Option value="pending">待审核</Option>
                          <Option value="approved">已审核</Option>
                          <Option value="rejected">已拒绝</Option>
                        </Select>
                        <RangePicker />
                      </Space>
                    </Col>
                    <Col xs={24} lg={6}>
                      <Space>
                        <Button icon={<ExportOutlined />}>导出</Button>
                        <Button type="primary" icon={<PlusOutlined />} onClick={handleCreateRecord}>
                          新增记录
                        </Button>
                      </Space>
                    </Col>
                  </Row>

                  {/* 财务记录列表 */}
                  <Table
                    columns={columns}
                    dataSource={financialData}
                    rowKey="id"
                    pagination={{
                      total: financialData.length,
                      pageSize: 10,
                      showSizeChanger: true,
                      showQuickJumper: true,
                      showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
                    }}
                    scroll={{ x: 1000 }}
                  />
                </div>
              )
            },
            {
              key: 'reports',
              label: '财务报表',
              children: (
                <div style={{ display: 'flex', flexDirection: 'column', gap: styleHelpers.spacing.md }}>
                  <Row gutter={[16, 16]}>
                    <Col xs={24} lg={12}>
                      <Card title="收支趋势" style={{ height: '384px' }}>
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          height: '256px',
                          color: '#9ca3af'
                        }}>
                          <div style={{ textAlign: 'center' }}>
                            <PieChartOutlined style={{ fontSize: '36px', marginBottom: '16px' }} />
                            <p>收支趋势图表</p>
                            <p style={{ fontSize: '14px' }}>（图表组件将在后续集成）</p>
                          </div>
                        </div>
                      </Card>
                    </Col>
                    <Col xs={24} lg={12}>
                      <Card title="分类统计" style={{ height: '384px' }}>
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          height: '256px',
                          color: '#9ca3af'
                        }}>
                          <div style={{ textAlign: 'center' }}>
                            <PieChartOutlined style={{ fontSize: '36px', marginBottom: '16px' }} />
                            <p>分类统计图表</p>
                            <p style={{ fontSize: '14px' }}>（图表组件将在后续集成）</p>
                          </div>
                        </div>
                      </Card>
                    </Col>
                  </Row>
                </div>
              )
            }
          ]}
        />
      </Card>

      {/* 新增财务记录模态框 */}
      <Modal
        title="新增财务记录"
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={800}
        okText="确认"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            date: new Date().toISOString().split('T')[0],
            type: 'expense'
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="记录类型"
                name="type"
                rules={[{ required: true, message: '请选择记录类型' }]}
              >
                <Select placeholder="请选择记录类型">
                  <Option value="income">收入</Option>
                  <Option value="expense">支出</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="日期"
                name="date"
                rules={[{ required: true, message: '请选择日期' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="分类"
                name="category"
                rules={[{ required: true, message: '请输入分类' }]}
              >
                <Input placeholder="请输入分类" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="金额"
                name="amount"
                rules={[{ required: true, message: '请输入金额' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="请输入金额"
                  prefix="¥"
                  min={0}
                  precision={2}
                />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            label="关联单据"
            name="reference"
          >
            <Input placeholder="请输入关联单据号（可选）" />
          </Form.Item>
          <Form.Item
            label="描述"
            name="description"
            rules={[{ required: true, message: '请输入描述' }]}
          >
            <TextArea 
              placeholder="请输入详细描述" 
              rows={3}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default FinanceManagement
